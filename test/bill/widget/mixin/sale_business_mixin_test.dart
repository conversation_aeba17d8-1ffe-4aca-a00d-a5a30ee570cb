import 'package:flutter_test/flutter_test.dart';
import 'package:halo_pos/bill/entity/goods_bill.dto.dart';
import 'package:halo_pos/bill/tool/promotion/promotion.dart';

/// 测试 SaleBusinessMixin 的 doClear 方法是否正确清理促销提示
void main() {
  group('SaleBusinessMixin doClear 测试', () {
    test('doClear 应该清理促销提示', () {
      // 创建一个包含促销提示的 GoodsBillDto
      final goodsBillDto = GoodsBillDto();
      
      // 添加一些促销提示
      goodsBillDto.promotionHints = [
        PromotionHints(
          promotionId: "test_promotion_1",
          promotionType: 1,
          typeName: "订货满减",
          hints: "满39.0元,可减1元",
          showDetail: false,
          promotionGiftScope: 0,
        ),
      ];
      
      // 添加其他促销相关数据
      goodsBillDto.tips = "测试备注";
      goodsBillDto.memo = "测试备忘录";
      goodsBillDto.couponMemo = "测试优惠券备忘录";
      goodsBillDto.promotionGiftRecord = "测试促销记录";
      
      // 验证初始状态
      expect(goodsBillDto.promotionHints, isNotNull);
      expect(goodsBillDto.promotionHints!.length, equals(1));
      expect(goodsBillDto.tips, equals("测试备注"));
      expect(goodsBillDto.memo, equals("测试备忘录"));
      expect(goodsBillDto.couponMemo, equals("测试优惠券备忘录"));
      expect(goodsBillDto.promotionGiftRecord, equals("测试促销记录"));
      
      // 模拟 doClear 方法的清理逻辑
      goodsBillDto.tips = "";
      goodsBillDto.memo = "";
      goodsBillDto.couponMemo = "";
      goodsBillDto.giftCouponList.clear();
      goodsBillDto.preferentialHelp.clear();
      goodsBillDto.promotionGiftRecord = null;
      goodsBillDto.promotionHints = null; // 这是我们添加的修复
      goodsBillDto.outDetail.clear();
      
      // 验证清理后的状态
      expect(goodsBillDto.promotionHints, isNull);
      expect(goodsBillDto.tips, equals(""));
      expect(goodsBillDto.memo, equals(""));
      expect(goodsBillDto.couponMemo, equals(""));
      expect(goodsBillDto.promotionGiftRecord, isNull);
      expect(goodsBillDto.giftCouponList, isEmpty);
      expect(goodsBillDto.preferentialHelp, isEmpty);
      expect(goodsBillDto.outDetail, isEmpty);
    });
    
    test('doClear 应该处理空的促销提示', () {
      // 创建一个没有促销提示的 GoodsBillDto
      final goodsBillDto = GoodsBillDto();
      
      // 验证初始状态
      expect(goodsBillDto.promotionHints, isNull);
      
      // 模拟 doClear 方法的清理逻辑
      goodsBillDto.promotionHints = null; // 应该不会抛出异常
      
      // 验证清理后的状态
      expect(goodsBillDto.promotionHints, isNull);
    });
  });
}
