name: halo_pos
description: 网上管家婆云零售

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# version加号后面的为版本号 == VersionCode， 非必须可不更新，只能改大，不能改小。


version: 5.9.5+5905


environment:
  #  sdk: ">=2.7.0 <3.0.0"
  #升级flutter3.0以及空安全

  sdk: ">=3.7.2 <4.0.0"
  flutter: "3.29.3"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.

  xml: ^6.0.1
  http: ^1.0.0
  crypto: ^3.0.3
  encrypt: ^5.0.1
  uuid: ^3.0.7
  fluttertoast: ^8.2.12
  event_bus: ^2.0.0
  cupertino_icons: ^1.0.2
  flutter_svg: ^1.1.6
  decimal: ^2.3.2
  get: ^4.6.0
  dio: ^4.0.0
  connectivity_plus: ^6.1.3
  sqflite: ^2.2.8+4
  sqflite_common_ffi: ^2.2.5
  flutter_image_compress: ^2.4.0
  collection: ^1.17.0
  #语音插件，>=4.1.0之后使用的dart>=3.4.0，并且windows会闪退

  flutter_tts: 4.0.2
  timezone: ^0.8.0
  # 仅供两个原生平台使用的图片压缩
  # 官方插件，用于获取app内部存储空间和sd外部存储空间，保存副屏的视频和图片

  scan_gun: ^2.0.0
  path_provider: ^2.0.8
  qr_flutter: ^4.0.0
  haloui:
    #    path: ../haloui
    git:
      url: ssh://***********************:9022/wsgjp/haloui.git
      ref: dev-flutter-3.29
  halo_utils:
    #    path: ../haloui/plugins/halo_utils
    git:
      url: ssh://***********************:9022/wsgjp/haloui.git
      path: plugins/halo_utils
      ref: dev-flutter-3.29
  holophoto:
    #    path: ../haloui/plugins/holophoto
    git:
      url: ssh://***********************:9022/wsgjp/haloui.git
      path: plugins/holophoto
      ref: dev-flutter-3.29
  bluetooth:
    #    path: ../haloui/plugins/bluetooth
    git:
      url: ssh://***********************:9022/wsgjp/haloui.git
      path: plugins/bluetooth
      ref: dev-flutter-3.29
  bluetooth_print:
    #    path: ../haloui/plugins/bluetooth_print
    git:
      url: ssh://***********************:9022/wsgjp/haloui.git
      path: plugins/bluetooth_print
      ref: dev-flutter-3.29

    #windows蓝牙
    #  flutter_windows_bluetooth:
    #    path: plugins/flutter_windows_bluetooth
  flutter_windows_bluetooth:
    #      path: ../haloui/plugins/flutter_windows_bluetooth
    git:
      url: ssh://***********************:9022/wsgjp/haloui.git
      path: plugins/flutter_windows_bluetooth
      ref: dev-flutter-3.29
  #图表

  fl_chart:
    #    path: ../haloui/plugins/fl_chart-0.40.0
    git:
      url: ssh://***********************:9022/wsgjp/haloui.git
      path: plugins/fl_chart-0.40.0
      ref: dev-flutter-3.29

  quick_usb:
    #    path: ../haloui/plugins/quick_usb
    git:
      url: ssh://***********************:9022/wsgjp/haloui.git
      path: plugins/quick_usb
      ref: dev-flutter-3.29

  #开发Windows版本时，依赖的库，支持windowsUsb打印，以及在pos端对打印内容进行pos指令编码，原库作者在1.4.2已经删库跑路
  #  flutter_pos_printer_platform: 1.4.1

  #windows打印
  flutter_pos_printer_platform_image_3:
    git:
      url: https://github.com/diantahoc/flutter_pos_printer_platform.git
      ref: cf9ae0923510776e688eb08b2363c20bbd816eb8
  flutter_esc_pos_utils: ^1.0.1

  #打开设置

  app_settings: ^6.1.1

  # url_launcher: ^6.0.9 POS机没有内置浏览器
  # 改用webView
  #  flutter_webview_plugin: ^0.4.0
  flutter_easyrefresh: ^2.2.1
  webview_flutter: ^4.0.2
  # 图片上传默认背景的虚线边框

  dotted_border: ^2.0.0+1
  # 获取设备码

  #  device_info: ^2.0.3
  #  device_info_plus: ^11.3.0
  #谷歌的允许list按照index滚动的库

  scrollable_positioned_list: ^0.3.5

  #url跳转，用于windows打开浏览器，而不是内部webView,先写死6.1.11，因为6.1.12版本要求最低dart在3.0

  url_launcher: ^6.1.11
  #获取包信息，原来的package_info仅支持安卓

  package_info_plus: ^4.1.0
  #播放声音的插件 这里写死4.1.0，因为dart3.0

  audioplayers: ^6.4.0

  barcode_scan2: ^4.2.1

  #  #版本兼容 版本列表

  #  halo_pos_lower:
  #    git:
  #      url: ssh://***********************:9022/kaitian/halo_pos.git
  #      ref: release-5.1
  #  dev_dependencies:
  #    flutter_test:
  #      sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.1
  #windows视频播放

  video_player_win: ^2.0.0
  #windows多屏

  info_popup: ^4.3.1

  desktop_multi_window: ^0.2.0
  #  quick_usb: ^0.4.0
  # 这里用git中的地址，因为作者在发版打了标记之后，发现自己代码有bug，然后修复了，又没有改版本号发版。导致插件市场拿到的一直是错的代码。

  #  quick_usb:
  #    git:
  #      url: https://github.com/woodemi/quick.flutter.git
  #      path: packages/quick_usb
  #      ref: master

  # windows 快捷键

  hotkey_manager: ^0.2.3

  # windows 关闭拦截
  flutter_window_close: ^1.2.0
  flutter_smart_dialog: ^4.9.8+5

dependency_overrides:
  #windows电子秤用的quickUsb用的是1.0的ffi，而device_info用的是2.0的ffi，为了两者都能跑，只能先强制都用1.0（强制2.0发现编译过不了）

  ffi: ^2.1.4

  archive: ^4.0.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/city.json
    - assets/images/logo.png
    - assets/images/startup.png
    - assets/images/nodata.png
    - assets/images/xiaopiao.png
    - assets/images/ptype_default.png
    - assets/images/zanwutupian.png
    - assets/menu/qianxianguanli.svg
    - assets/menu/dakaiqianxiang.svg
    - assets/menu/xinzengshangpin.svg
    - assets/menu/shangpinbianji.svg
    - assets/menu/kucunchaxun.svg
    - assets/menu/yaohuoguanli.svg
    - assets/menu/pandian.svg
    - assets/menu/jiaojiebanjilu.svg
    - assets/menu/xiaoshoudanjuchaxun.svg
    - assets/menu/shouyinliushui.svg
    - assets/menu/xitongpeizhi.svg
    - assets/menu/jinruhoutai.svg
    - assets/menu/dayinpeizhi.svg
    - assets/menu/tuihuo.svg
    - assets/menu/jifenduihuan.svg
    - assets/menu/huiyuanchongzhi.svg
    - assets/menu/xinzenghuiyuan.svg
    - assets/menu/quanjudaodingdan.svg
    - assets/menu/quanjudaodingdan.svg
    - assets/menu/kuaijiejianshezhi.svg
    - assets/images/progress_indicator.svg
    - assets/images/verificationsuccessful.svg
    - assets/images/yizuofei.svg
    - assets/menu/shangpinxiaoshoutongji.svg
    - assets/images/
    - assets/menu/
    - assets/voice/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
