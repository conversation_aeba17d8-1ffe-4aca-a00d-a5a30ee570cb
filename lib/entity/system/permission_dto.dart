/// Time：2020-06-03
/// Author：yi.zhang
/// E-mail: <EMAIL>
/// Description：权限实体

class PermissionDto {
  bool? baseinfoptypeadd; //新增商品权限

  bool? shopsalesalesettingsaveGraft; //挂单权限

  bool? shopsalesalesettinggetGraft; //取单权限

  ///整单折扣权限
  bool? shopsalesalesettingbillDiscount;

  ///修改零售价
  bool? shopsalesalesettingeditRetailPrice;

  ///按商品退货
  bool? shopsalesalesettingbackGoods;

  bool? recordsheetSaleChangeBillcreate; //销售换货单创建

  bool? shopsalesalesettingeditDetailDiscount; //开单折扣
  bool? shopsalesalesettingeditDetailPrice; //开单价格
  bool? shopsalesalesettingeditOrderPreferential; //整单优惠
  bool? shopsalesalesettingeditGift; //赠品

  bool? shopsaleshiftchangesview; //交接班查看
  bool? shopsaleshiftchangessaledetail; //交接班明细查看
  bool? shopsaleshiftchangesprint; //交接班打印
  bool? shopsaleshiftchangesout; //交接班退出
  bool? shopsalesettingedit; //通用设置
  bool? shopsaleprintedit; //打印设置
  bool? shopsalescreenedit; //副屏设置
  bool? shopsaleprintmodeledit; //打印模版设置

  bool? shopsaleprintpatchworknote; //补打小票

  bool? shopsalecashboxopen; //打开钱箱

  bool? analysiscloudinventoryPositionview; //库存查询

  bool? recordsheetSaleBillQueryview;

  bool? recordsheetGoodsTransQueryview;

  bool? recordsheetGoodsTransOrderBillcreate;

  bool? recordsheetGoodsTransOrderQueryview; //调拨订单管理查看

  bool? analysiscloudouterInventorytransOnwayview; //调拨在途统计查看

  bool? analysiscloudStockCheckRecordview; //盘点记录查看

  bool? analysiscloudStockCheckRecordcreate; //新增盘点

  bool? analysiscloudStockCheckRecordmodify; //修改盘点记录

  bool? analysiscloudStockCheckRecorddelete; //删除盘点记录

  bool? recordsheetProductCostPriceGroupConfigview; //成本查看权限

  bool? recordsheetTransGoodsBillcreate;

  bool? recordsheetOtherSaleBillcreate;

  bool? recordsheetTransGoodsBillsaveBill;

  //开单权限
  bool? recordsheetSaleBillview;

  bool? recordsheetSaleBilldeleteHasBill;

  // bool? recordsheetSaleBillmodifyPrice;

  bool? recordsheetSaleBillcreate;

  //手工改优惠分摊权限，没有了
  // bool recordsheetEshopSaleBillpreferenceShare;

  bool? recordsheetSaleBackBillview;

  bool? recordsheetSaleBackBilldeleteHasBill;

  bool? recordsheetSaleBackBillcreate;

  ///查看会员
  bool? memberVipView;

  ///新增会员
  bool? memberVipAdd;

  ///编辑会员
  bool? memberVipEdit;

  ///赠送积分
  bool? memberVipGiveScore;

  ///发放权益卡
  bool? memberVipBindRightsCard;

  ///发放优惠券
  bool? memberVipCard;

  ///充值
  bool? memberVipRecharge;

  ///充值是否可以编辑赠金
  bool? memberrechargeStrategyeditGiveMoney;

  ///标签
  bool? memberVipAddTags;

  ///调货管理权限
  ///调货管理-查看
  bool? shopsaletenderManageview;

  ///调货管理-确认入库
  bool? shopsaletenderManageconfirm;

  ///调货管理-确认后自动打印单据
  bool? shopsaletenderManageautoPrint;

  ///调货管理-列配置
  bool? shopsaletenderManagecolumnConfig;

  ///调货管理-打印
  bool? shopsaletenderManageprint;

  ///调拨订单权限（要货申请）
  ///要货申请-查看
  bool? shopsaletransferOrderview;

  ///要货申请-新增
  bool? shopsaletransferOrdercreate;

  ///要货申请-提交
  bool? shopsaletransferOrderconfirm;

  ///要货申请-发货
  bool? shopsaletransferOrdersend;

  ///要货申请-删除
  bool? shopsaletransferOrderdelete;

  ///要货申请-打印
  bool? shopsaletransferOrderprint;

  ///快捷键设置
  bool? shopsalehotkeyedit;

  ///储值记录-查看
  bool? shopsalerechargeRecordview;

  ///储值记录-作废
  bool? shopsalerechargeRecordinvalidate;

  ///查看批次成本
  bool? shopsalesalesettingviewBatchCost;

  PermissionDto();

  factory PermissionDto.fromMap(Map<String, dynamic>? map) {
    if (map == null) return PermissionDto();
    PermissionDto permissionDtoBean = PermissionDto();
    permissionDtoBean.recordsheetSaleChangeBillcreate =
        map["recordsheetSaleChangeBillcreate"] ?? false;
    permissionDtoBean.shopsalesalesettingeditDetailDiscount =
        map["shopsalesalesettingeditDetailDiscount"] ?? false;
    permissionDtoBean.shopsalesalesettingeditOrderPreferential =
        map["shopsalesalesettingeditOrderPreferential"] ?? false;
    permissionDtoBean.shopsalesalesettingeditDetailPrice =
        map["shopsalesalesettingeditDetailPrice"] ?? false;
    permissionDtoBean.shopsalesalesettingeditGift =
        map["shopsalesalesettingeditGift"] ?? false;
    permissionDtoBean.baseinfoptypeadd = map["baseinfoptypeadd"];
    permissionDtoBean.shopsalesalesettingsaveGraft =
        map["shopsalesalesettingsaveGraft"];
    permissionDtoBean.shopsalesalesettinggetGraft =
        map["shopsalesalesettinggetGraft"] ?? false;
    permissionDtoBean.shopsalesalesettingbillDiscount =
        map["shopsalesalesettingbillDiscount"] ?? false;
    permissionDtoBean.shopsalesalesettingeditRetailPrice =
        map["shopsalesalesettingeditRetailPrice"];
    permissionDtoBean.shopsalesalesettingbackGoods =
        map["shopsalesalesettingbackGoods"] ?? false;
    permissionDtoBean.recordsheetSaleChangeBillcreate =
        map["recordsheetSaleChangeBillcreate"] ?? false;
    permissionDtoBean.shopsalesalesettingeditDetailDiscount =
        map["shopsalesalesettingeditDetailDiscount"] ?? false;
    permissionDtoBean.shopsalesalesettingeditOrderPreferential =
        map["shopsalesalesettingeditOrderPreferential"] ?? false;
    permissionDtoBean.shopsalesalesettingeditDetailPrice =
        map["shopsalesalesettingeditDetailPrice"] ?? false;
    permissionDtoBean.shopsalesalesettingeditGift =
        map["shopsalesalesettingeditGift"] ?? false;

    permissionDtoBean.analysiscloudinventoryPositionview =
        map["analysiscloudinventoryPositionview"] ?? false;

    permissionDtoBean.shopsalecashboxopen = map["shopsalecashboxopen"] ?? false;

    permissionDtoBean.shopsaleshiftchangesview =
        map["shopsaleshiftchangesview"] ?? false;

    permissionDtoBean.shopsaleshiftchangessaledetail =
        map["shopsaleshiftchangessaledetail"] ?? false;

    permissionDtoBean.shopsaleshiftchangesprint =
        map["shopsaleshiftchangesprint"] ?? false;

    permissionDtoBean.shopsaleshiftchangesout =
        map["shopsaleshiftchangesout"] ?? false;

    permissionDtoBean.shopsalesettingedit = map["shopsalesettingedit"] ?? false;

    permissionDtoBean.shopsaleprintedit = map["shopsaleprintedit"] ?? false;

    permissionDtoBean.shopsalescreenedit = map["shopsalescreenedit"] ?? false;

    permissionDtoBean.shopsaleprintmodeledit =
        map["shopsaleprintmodeledit"] ?? false;

    permissionDtoBean.recordsheetSaleBillQueryview =
        map["recordsheetSaleBillQueryview"] ?? false;

    permissionDtoBean.recordsheetGoodsTransQueryview =
        map["recordsheetGoodsTransQueryview"] ?? false;

    permissionDtoBean.recordsheetGoodsTransOrderBillcreate =
        map["recordsheetGoodsTransOrderBillcreate"] ?? false;

    permissionDtoBean.recordsheetGoodsTransOrderQueryview =
        map["recordsheetGoodsTransOrderQueryview"] ?? false;

    permissionDtoBean.analysiscloudouterInventorytransOnwayview =
        map["analysiscloudouterInventorytransOnwayview"] ?? false;

    permissionDtoBean.analysiscloudStockCheckRecordview =
        map["analysiscloudStockCheckRecordview"] ?? false;

    permissionDtoBean.analysiscloudStockCheckRecordcreate =
        map["analysiscloudStockCheckRecordcreate"] ?? false;

    permissionDtoBean.analysiscloudStockCheckRecordmodify =
        map["analysiscloudStockCheckRecordmodify"] ?? false;

    permissionDtoBean.analysiscloudStockCheckRecorddelete =
        map["analysiscloudStockCheckRecorddelete"] ?? false;

    permissionDtoBean.recordsheetProductCostPriceGroupConfigview =
        map["recordsheetProductCostPriceGroupConfigview"] ?? false;

    permissionDtoBean.recordsheetTransGoodsBillcreate =
        map["recordsheetTransGoodsBillcreate"] ?? false;

    permissionDtoBean.recordsheetOtherSaleBillcreate =
        map["recordsheetOtherSaleBillcreate"] ?? false;

    permissionDtoBean.recordsheetTransGoodsBillsaveBill =
        map["recordsheetTransGoodsBillsaveBill"] ?? false;

    permissionDtoBean.recordsheetSaleBillview =
        map["recordsheetSaleBillview"] ?? false;

    permissionDtoBean.recordsheetSaleBilldeleteHasBill =
        map["recordsheetSaleBilldeleteHasBill"] ?? false;

    // permissionDtoBean.recordsheetSaleBillmodifyPrice =
    //     map["recordsheetSaleBillmodifyPrice"] ?? false;

    permissionDtoBean.recordsheetSaleBillcreate =
        map["recordsheetSaleBillcreate"] ?? false;

    permissionDtoBean.recordsheetSaleBackBillview =
        map["recordsheetSaleBackBillview"] ?? false;

    permissionDtoBean.recordsheetSaleBackBilldeleteHasBill =
        map["recordsheetSaleBackBilldeleteHasBill"] ?? false;

    permissionDtoBean.recordsheetSaleBackBillcreate =
        map["recordsheetSaleBackBillcreate"] ?? false;

    permissionDtoBean.memberVipView = map["membervipview"] ?? false;
    permissionDtoBean.memberVipAdd = map["membervipadd"] ?? false;
    permissionDtoBean.memberVipEdit = map["membervipedit"] ?? false;
    permissionDtoBean.memberVipGiveScore = map["membervipgiveScore"] ?? false;
    permissionDtoBean.memberVipBindRightsCard =
        map["membervipbindRightsCard"] ?? false;
    permissionDtoBean.memberVipCard = map["membervipcard"] ?? false;
    permissionDtoBean.memberVipRecharge = map["memberviprecharge"] ?? false;
    permissionDtoBean.memberrechargeStrategyeditGiveMoney =
        map["memberrechargeStrategyeditGiveMoney"] ?? false;
    permissionDtoBean.memberVipAddTags = map["membervipaddTags"] ?? false;
    permissionDtoBean.shopsaleprintpatchworknote =
        map["shopsaleprintpatchworknote"] ?? false;
    //调货管理权限
    permissionDtoBean.shopsaletenderManageview =
        map["shopsaletenderManageview"] ?? false;
    permissionDtoBean.shopsaletenderManageconfirm =
        map["shopsaletenderManageconfirm"] ?? false;
    permissionDtoBean.shopsaletenderManageautoPrint =
        map["shopsaletenderManageautoPrint"] ?? false;
    permissionDtoBean.shopsaletenderManagecolumnConfig =
        map["shopsaletenderManagecolumnConfig"] ?? false;
    permissionDtoBean.shopsaletenderManageprint =
        map["shopsaletenderManageprint"] ?? false;
    permissionDtoBean.shopsaletransferOrderview =
        map["shopsaletransferOrderview"] ?? false;
    permissionDtoBean.shopsaletransferOrdercreate =
        map["shopsaletransferOrdercreate"] ?? false;
    permissionDtoBean.shopsaletransferOrderconfirm =
        map["shopsaletransferOrderconfirm"] ?? false;
    permissionDtoBean.shopsaletransferOrdersend =
        map["shopsaletransferOrdersend"] ?? false;
    permissionDtoBean.shopsaletransferOrderdelete =
        map["shopsaletransferOrderdelete"] ?? false;
    permissionDtoBean.shopsaletransferOrderprint =
        map["shopsaletransferOrderprint"] ?? false;
    permissionDtoBean.shopsalehotkeyedit = map["shopsalehotkeyedit"] ?? false;
    permissionDtoBean.shopsalerechargeRecordview =
        map["shopsalerechargeRecordview"] ?? false;
    permissionDtoBean.shopsalerechargeRecordinvalidate =
        map["shopsalerechargeRecordinvalidate"] ?? false;
    permissionDtoBean.shopsalesalesettingviewBatchCost =
        map["shopsalesalesettingviewBatchCost"] ?? false;
    return permissionDtoBean;
  }

  Map toJson() => {
        "baseinfoptypeadd": baseinfoptypeadd,
        "shopsalesalesettingsaveGraft": shopsalesalesettingsaveGraft,
        "shopsalesalesettinggetGraft": shopsalesalesettinggetGraft,
        "shopsalesalesettingbillDiscount": shopsalesalesettingbillDiscount,
        "shopsalesalesettingeditRetailPrice":
            shopsalesalesettingeditRetailPrice,
        "shopsalesalesettingbackGoods": shopsalesalesettingbackGoods,
        "shopsalecashboxopen": shopsalecashboxopen,
        "shopsaleshiftchangesview": shopsaleshiftchangesview,
        "shopsaleshiftchangessaledetail": shopsaleshiftchangessaledetail,
        "shopsaleshiftchangesprint": shopsaleshiftchangesprint,
        "shopsaleshiftchangesout": shopsaleshiftchangesout,
        "shopsalesettingedit": shopsalesettingedit,
        "shopsaleprintedit": shopsaleprintedit,
        "shopsalescreenedit": shopsalescreenedit,
        "shopsaleprintmodeledit": shopsaleprintmodeledit,
        "analysiscloudinventoryPositionview":
            analysiscloudinventoryPositionview,
        "recordsheetSaleBillQueryview": recordsheetSaleBillQueryview,
        "recordsheetSaleBillview": recordsheetSaleBillview,
        "recordsheetSaleBilldeleteHasBill": recordsheetSaleBilldeleteHasBill,
        // "recordsheetSaleBillmodifyPrice": recordsheetSaleBillmodifyPrice,
        "recordsheetSaleBillcreate": recordsheetSaleBillcreate,
        "recordsheetSaleBackBillview": recordsheetSaleBackBillview,
        "recordsheetSaleBackBilldeleteHasBill":
            recordsheetSaleBackBilldeleteHasBill,
        "recordsheetSaleBackBillcreate": recordsheetSaleBackBillcreate,
        "recordsheetGoodsTransQueryview": recordsheetGoodsTransQueryview,
        "recordsheetGoodsTransOrderBillcreate":
            recordsheetGoodsTransOrderBillcreate,
        "recordsheetProductCostPriceGroupConfigview":
            recordsheetProductCostPriceGroupConfigview,
        "recordsheetGoodsTransOrderQueryview":
            recordsheetGoodsTransOrderQueryview,
        "analysiscloudouterInventorytransOnwayview":
            analysiscloudouterInventorytransOnwayview,
        "recordsheetTransGoodsBillcreate": recordsheetTransGoodsBillcreate,
        "recordsheetGoodsTransOrderQueryview":
            recordsheetGoodsTransOrderQueryview,
        "analysiscloudouterInventorytransOnwayview":
            analysiscloudouterInventorytransOnwayview,
        "analysiscloudStockCheckRecordview": analysiscloudStockCheckRecordview,
        "analysiscloudStockCheckRecordcreate":
            analysiscloudStockCheckRecordcreate,
        "analysiscloudStockCheckRecordmodify":
            analysiscloudStockCheckRecordmodify,
        "analysiscloudStockCheckRecorddelete":
            analysiscloudStockCheckRecorddelete,
        "recordsheetTransGoodsBillcreate": recordsheetTransGoodsBillcreate,
        "recordsheetOtherSaleBillcreate": recordsheetOtherSaleBillcreate,
        "recordsheetTransGoodsBillsaveBill": recordsheetTransGoodsBillsaveBill,
        "membervipview": memberVipView,
        "membervipadd": memberVipAdd,
        "membervipedit": memberVipEdit,
        "membervipgiveScore": memberVipGiveScore,
        "membervipbindRightsCard": memberVipBindRightsCard,
        "membervipcard": memberVipCard,
        "memberviprecharge": memberVipRecharge,
        "memberrechargeStrategyeditGiveMoney":
            memberrechargeStrategyeditGiveMoney,
        "membervipaddTags": memberVipAddTags,
        "shopsaleprintpatchworknote": shopsaleprintpatchworknote,
        "shopsaletenderManageview": shopsaletenderManageview,
        "shopsaletenderManageconfirm": shopsaletenderManageconfirm,
        "shopsaletenderManageautoPrint": shopsaletenderManageautoPrint,
        "shopsaletenderManagecolumnConfig": shopsaletenderManagecolumnConfig,
        "shopsaletenderManageprint": shopsaletenderManageprint,
        "shopsaletransferOrderview": shopsaletransferOrderview,
        "shopsaletransferOrdercreate": shopsaletransferOrdercreate,
        "shopsaletransferOrderconfirm": shopsaletransferOrderconfirm,
        "shopsaletransferOrdersend": shopsaletransferOrdersend,
        "shopsaletransferOrderdelete": shopsaletransferOrderdelete,
        "shopsaletransferOrderprint": shopsaletransferOrderprint,
        "shopsalehotkeyedit": shopsalehotkeyedit,
        "shopsalerechargeRecordview": shopsalerechargeRecordview,
        "shopsalerechargeRecordinvalidate": shopsalerechargeRecordinvalidate,
        "shopsalesalesettingviewBatchCost": shopsalesalesettingviewBatchCost,
      };
}
