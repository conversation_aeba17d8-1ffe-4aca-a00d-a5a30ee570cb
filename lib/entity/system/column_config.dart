enum ColumnType {
  all, //全部
  orderNumber, //序号
  userCode, //商品编码
  barCode, //条码
  pName, //商品名称
  number, //数量
  price, //单价
  discount, //折扣
  reducedPrice, //优惠
  currentPrice, //现价
  subtotalPrice, //小计
  unit, //单位
  stockQty, //库存
  taxRate, //税率
  taxTotal, //税额
  setting, //设置
}

/// 调拨管理专用列配置类型
enum TenderColumnType {
  all, // 全部
  image, // 图片
  pName, // 商品名称
  userCode, // 商品编号
  barCode, // SKU条码
  attributeFormat, // 属性格式
  attributeCombo, // 属性组合
  serialNumber, // 序列号
  produceDate, // 生产日期
  qualityDays, // 保质期
  expireDate, // 到期日期
  batchNumber, // 批次号
  spec, // 规格
  model, // 型号
  number, // 数量
  unit, // 单位
  stock, // 库存
  setting, // 设置
}

/// 要货申请专用列配置类型
enum StoreRequisitionColumnType {
  all, // 全部
  image, // 图片
  pName, // 商品名称
  userCode, // 商品编号
  barCode, // SKU条码
  attributeFormat, // 属性格式
  attributeCombo, // 属性组合
  spec, // 规格
  model, // 型号
  number, // 数量
  unit, // 单位
  stock, // 库存
  setting, // 设置
}

List<ColumnConfig> columnBillingConfigListSaleBack({bool? isTaxEnable}) {
  List<ColumnConfig> configList = [];
  configList.addAll([
    ColumnConfig(title: "全部", type: ColumnType.all),
    ColumnConfig(title: "商品编号", type: ColumnType.userCode, isShow: false),
    ColumnConfig(title: "条码", type: ColumnType.barCode),
    ColumnConfig(title: "商品名称", type: ColumnType.pName, isRequired: true),
    ColumnConfig(title: "数量", type: ColumnType.number, isRequired: true),
    ColumnConfig(title: "单位", type: ColumnType.unit),
    ColumnConfig(title: "单价(元)", type: ColumnType.price),
    ColumnConfig(title: "最终折扣", type: ColumnType.discount),
    ColumnConfig(title: "最终优惠", type: ColumnType.reducedPrice),
    ColumnConfig(title: "现价(元)", type: ColumnType.currentPrice),
    ColumnConfig(title: "小计(元)", type: ColumnType.subtotalPrice),
  ]);

  if (null != isTaxEnable && isTaxEnable) {
    configList.addAll([
      ColumnConfig(title: "税率(%)", type: ColumnType.taxRate, isShow: false),
      ColumnConfig(title: "税额(元)", type: ColumnType.taxTotal, isShow: false),
    ]);
  }
  return configList;
}

// List<Map> columnBillingConfigListSale = [
//   ColumnConfig(title: "全部", type: ColumnType.all).toJson(),
//   ColumnConfig(title: "商品编号", type: ColumnType.userCode, isShow: false)
//       .toJson(),
//   ColumnConfig(title: "条码", type: ColumnType.barCode).toJson(),
//   ColumnConfig(title: "商品名称", type: ColumnType.pName, isRequired: true)
//       .toJson(),
//   ColumnConfig(title: "数量", type: ColumnType.number, isRequired: true).toJson(),
//   ColumnConfig(title: "单位", type: ColumnType.unit).toJson(),
//   ColumnConfig(title: "单价(元)", type: ColumnType.price).toJson(),
//   ColumnConfig(title: "折", type: ColumnType.discount, isShow: false).toJson(),
// ];

///销售出库的列配置项
List<ColumnConfig> getColumnBillingConfigListSale({
  bool? isOpenStock,
  bool? isTaxEnable,
}) {
  List<ColumnConfig> configList = [];
  configList.addAll([
    ColumnConfig(title: "全部", type: ColumnType.all),
    ColumnConfig(title: "序号", type: ColumnType.orderNumber, isShow: true),
    ColumnConfig(title: "商品编号", type: ColumnType.userCode, isShow: false),
    ColumnConfig(title: "条码", type: ColumnType.barCode),
    ColumnConfig(title: "商品名称", type: ColumnType.pName, isRequired: true),
    ColumnConfig(title: "数量", type: ColumnType.number, isRequired: true),
    ColumnConfig(title: "单位", type: ColumnType.unit),
    ColumnConfig(title: "单价(元)", type: ColumnType.price),
    ColumnConfig(title: "折", type: ColumnType.discount, isShow: false),
  ]);
  if (null != isOpenStock && isOpenStock) {
    configList.add(
      ColumnConfig(title: "库存", type: ColumnType.stockQty, isShow: false),
    );
  }
  configList.addAll([
    ColumnConfig(title: "优惠(元)", type: ColumnType.reducedPrice),
    ColumnConfig(title: "现价(元)", type: ColumnType.currentPrice, isShow: false),
    ColumnConfig(title: "小计(元)", type: ColumnType.subtotalPrice),
  ]);

  if (null != isTaxEnable && isTaxEnable) {
    configList.addAll([
      ColumnConfig(title: "税率(%)", type: ColumnType.taxRate, isShow: false),
      ColumnConfig(title: "税额(元)", type: ColumnType.taxTotal, isShow: false),
    ]);
  }
  return configList;
}

// List<Map> columnBillingConfigListSaleNoStockQty = [
//   ColumnConfig(title: "全部", type: ColumnType.all).toJson(),
//   ColumnConfig(title: "商品编号", type: ColumnType.userCode, isShow: false)
//       .toJson(),
//   ColumnConfig(title: "条码", type: ColumnType.barCode).toJson(),
//   ColumnConfig(title: "商品名称", type: ColumnType.pName, isRequired: true)
//       .toJson(),
//   ColumnConfig(title: "数量", type: ColumnType.number, isRequired: true).toJson(),
//   ColumnConfig(title: "单位", type: ColumnType.unit).toJson(),
//   ColumnConfig(title: "单价(元)", type: ColumnType.price).toJson(),
//   ColumnConfig(title: "优惠(元)", type: ColumnType.reducedPrice).toJson(),
//   ColumnConfig(title: "现价(元)", type: ColumnType.currentPrice, isShow: false)
//       .toJson(),
//   ColumnConfig(title: "小计(元)", type: ColumnType.subtotalPrice).toJson(),
//   ColumnConfig(title: "税率(%)", type: ColumnType.taxRate, isShow: false)
//       .toJson(),
//   ColumnConfig(title: "税额(元)", type: ColumnType.taxTotal, isShow: false)
//       .toJson(),
// ];

///单据查询列配置项
List<ColumnConfig> columnBillsConfigList({bool? isTaxEnable}) {
  List<ColumnConfig> configList = [];
  configList.addAll([
    ColumnConfig(title: "全部", type: ColumnType.all),
    ColumnConfig(title: "商品名称", type: ColumnType.pName, isRequired: true),
    ColumnConfig(title: "商品编号", type: ColumnType.userCode, isShow: false),
    ColumnConfig(title: "条码", type: ColumnType.barCode, isShow: false),
    ColumnConfig(title: "数量", type: ColumnType.number, isRequired: true),
    ColumnConfig(title: "单位", type: ColumnType.unit, isRequired: true),
    ColumnConfig(title: "单价(元)", type: ColumnType.price, isRequired: true),
    ColumnConfig(title: "折扣", type: ColumnType.discount),
    ColumnConfig(title: "优惠(元)", type: ColumnType.reducedPrice),
    ColumnConfig(title: "现价(元)", type: ColumnType.currentPrice),
    ColumnConfig(title: "小计(元)", type: ColumnType.subtotalPrice),
  ]);
  if (null != isTaxEnable && isTaxEnable) {
    configList.addAll([
      ColumnConfig(title: "税率(%)", type: ColumnType.taxRate, isShow: false),
      ColumnConfig(title: "税额(元)", type: ColumnType.taxTotal, isShow: false),
    ]);
  }
  return configList;
}

const Map<ColumnType, num> columnWidthScale = {
  ColumnType.orderNumber: 6,
  ColumnType.userCode: 10,
  ColumnType.barCode: 8,
  ColumnType.pName: 20,
  ColumnType.number: 14,
  ColumnType.unit: 6,
  ColumnType.price: 8,
  ColumnType.taxRate: 6,
  ColumnType.taxTotal: 8,
  ColumnType.discount: 6,
  ColumnType.stockQty: 6,
  ColumnType.reducedPrice: 8,
  ColumnType.currentPrice: 8,
  ColumnType.subtotalPrice: 10,
};

class ColumnConfig {
  String title;
  bool isShow;
  bool isRequired;
  ColumnType? type;

  ColumnConfig({
    this.type,
    this.title = "",
    this.isShow = true,
    this.isRequired = false,
  });

  static ColumnConfig fromMap(Map<dynamic, dynamic>? map) {
    if (map == null) return ColumnConfig();
    ColumnConfig columnConfig = ColumnConfig();
    columnConfig.type = typeFromString(map["type"]);
    columnConfig.isShow = map["isShow"];
    columnConfig.isRequired = map["isRequired"];
    columnConfig.title = map["title"];
    return columnConfig;
  }

  String typeToString() {
    switch (type) {
      case ColumnType.all:
        return "all";
      case ColumnType.orderNumber:
        return "orderNumber";
      case ColumnType.userCode:
        return "userCode";
      case ColumnType.barCode:
        return "barCode";
      case ColumnType.pName:
        return "pName";
      case ColumnType.number:
        return "number";
      case ColumnType.price:
        return "price";
      case ColumnType.discount:
        return "discount";
      case ColumnType.stockQty:
        return "stockQty";
      case ColumnType.reducedPrice:
        return "reducedPrice";
      case ColumnType.currentPrice:
        return "currentPrice";
      case ColumnType.subtotalPrice:
        return "subtotalPrice";
      case ColumnType.unit:
        return "unit";
      case ColumnType.taxRate:
        return "taxRate";
      case ColumnType.taxTotal:
        return "taxTotal";
      default:
        return "0";
    }
  }

  static ColumnType typeFromString(String typeString) {
    switch (typeString) {
      case "all":
        return ColumnType.all;
      case "orderNumber":
        return ColumnType.orderNumber;
      case "userCode":
        return ColumnType.userCode;
      case "barCode":
        return ColumnType.barCode;
      case "pName":
        return ColumnType.pName;
      case "number":
        return ColumnType.number;
      case "price":
        return ColumnType.price;
      case "discount":
        return ColumnType.discount;
      case "stockQty":
        return ColumnType.stockQty;
      case "reducedPrice":
        return ColumnType.reducedPrice;
      case "currentPrice":
        return ColumnType.currentPrice;
      case "subtotalPrice":
        return ColumnType.subtotalPrice;
      case "unit":
        return ColumnType.unit;
      case "taxRate":
        return ColumnType.taxRate;
      case "taxTotal":
        return ColumnType.taxTotal;
      default:
        return ColumnType.all;
    }
  }

  Map toJson() => {
    "type": typeToString(),
    "title": title,
    "isShow": isShow,
    "isRequired": isRequired,
  };
}

/// 要货申请和调拨管理列配置
class TenderColumnConfig {
  String title;
  bool isShow;
  bool isRequired;
  TenderColumnType? type;

  TenderColumnConfig({
    this.type,
    this.title = "",
    this.isShow = true,
    this.isRequired = false,
  });

  static TenderColumnConfig fromMap(Map<dynamic, dynamic>? map) {
    if (map == null) return TenderColumnConfig();
    TenderColumnConfig columnConfig = TenderColumnConfig();
    columnConfig.type = tenderTypeFromString(map["type"]);
    columnConfig.isShow = map["isShow"];
    columnConfig.isRequired = map["isRequired"];
    columnConfig.title = map["title"];
    return columnConfig;
  }

  Map<String, dynamic> toJson() {
    return {
      "title": title,
      "isShow": isShow,
      "isRequired": isRequired,
      "type": typeToString(),
    };
  }

  String typeToString() {
    if (type == null) return "";
    return type.toString().split('.').last;
  }

  static TenderColumnType? tenderTypeFromString(String? typeStr) {
    if (typeStr == null) return null;
    for (TenderColumnType type in TenderColumnType.values) {
      if (type.toString().split('.').last == typeStr) {
        return type;
      }
    }
    return null;
  }
}

/// 要货申请列配置
class StoreRequisitionColumnConfig {
  String title;
  bool isShow;
  bool isRequired;
  StoreRequisitionColumnType? type;

  StoreRequisitionColumnConfig({
    this.type,
    this.title = "",
    this.isShow = true,
    this.isRequired = false,
  });

  static StoreRequisitionColumnConfig fromMap(Map<dynamic, dynamic>? map) {
    if (map == null) return StoreRequisitionColumnConfig();
    StoreRequisitionColumnConfig columnConfig = StoreRequisitionColumnConfig();
    columnConfig.type = typeFromString(map["type"]);
    columnConfig.isShow = map["isShow"];
    columnConfig.isRequired = map["isRequired"];
    columnConfig.title = map["title"];
    return columnConfig;
  }

  Map<String, dynamic> toJson() {
    return {
      "title": title,
      "isShow": isShow,
      "isRequired": isRequired,
      "type": typeToString(),
    };
  }

  String typeToString() {
    if (type == null) return "";
    return type.toString().split('.').last;
  }

  static StoreRequisitionColumnType? typeFromString(String? typeStr) {
    if (typeStr == null) return null;
    for (StoreRequisitionColumnType type in StoreRequisitionColumnType.values) {
      if (type.toString().split('.').last == typeStr) {
        return type;
      }
    }
    return null;
  }
}

/// 要货申请列配置项
List<StoreRequisitionColumnConfig> storeRequisitionConfigList() {
  List<StoreRequisitionColumnConfig> configList = [];
  configList.addAll([
    StoreRequisitionColumnConfig(
      title: "全部",
      type: StoreRequisitionColumnType.all,
    ),
    StoreRequisitionColumnConfig(
      title: "图片",
      type: StoreRequisitionColumnType.image,
      isShow: true,
    ),
    StoreRequisitionColumnConfig(
      title: "商品名称",
      type: StoreRequisitionColumnType.pName,
      isRequired: true,
      isShow: true,
    ),
    StoreRequisitionColumnConfig(
      title: "商品编号",
      type: StoreRequisitionColumnType.userCode,
      isShow: false,
    ),
    StoreRequisitionColumnConfig(
      title: "SKU条码",
      type: StoreRequisitionColumnType.barCode,
      isShow: true,
    ),
    StoreRequisitionColumnConfig(
      title: "属性格式",
      type: StoreRequisitionColumnType.attributeFormat,
      isShow: false,
    ),
    StoreRequisitionColumnConfig(
      title: "属性组合",
      type: StoreRequisitionColumnType.attributeCombo,
      isShow: true,
    ),
    StoreRequisitionColumnConfig(
      title: "规格",
      type: StoreRequisitionColumnType.spec,
      isShow: false,
    ),
    StoreRequisitionColumnConfig(
      title: "型号",
      type: StoreRequisitionColumnType.model,
      isShow: false,
    ),
    StoreRequisitionColumnConfig(
      title: "数量",
      type: StoreRequisitionColumnType.number,
      isRequired: true,
      isShow: true,
    ),
    StoreRequisitionColumnConfig(
      title: "单位",
      type: StoreRequisitionColumnType.unit,
      isRequired: true,
      isShow: true,
    ),
  ]);

  return configList;
}

/// 调拨管理列配置项
List<TenderColumnConfig> tenderManageConfigList() {
  List<TenderColumnConfig> configList = [];
  configList.addAll([
    TenderColumnConfig(title: "全部", type: TenderColumnType.all),
    TenderColumnConfig(title: "图片", type: TenderColumnType.image, isShow: true),
    TenderColumnConfig(
      title: "商品名称",
      type: TenderColumnType.pName,
      isRequired: true,
      isShow: true,
    ),
    TenderColumnConfig(
      title: "商品编号",
      type: TenderColumnType.userCode,
      isShow: false,
    ),
    TenderColumnConfig(
      title: "SKU条码",
      type: TenderColumnType.barCode,
      isShow: true,
    ),
    TenderColumnConfig(
      title: "属性格式",
      type: TenderColumnType.attributeFormat,
      isShow: false,
    ),
    TenderColumnConfig(
      title: "属性组合",
      type: TenderColumnType.attributeCombo,
      isShow: true,
    ),
    TenderColumnConfig(
      title: "序列号",
      type: TenderColumnType.serialNumber,
      isShow: false,
    ),
    TenderColumnConfig(
      title: "生产日期",
      type: TenderColumnType.produceDate,
      isShow: false,
    ),
    TenderColumnConfig(
      title: "保质期",
      type: TenderColumnType.qualityDays,
      isShow: false,
    ),
    TenderColumnConfig(
      title: "到期日期",
      type: TenderColumnType.expireDate,
      isShow: false,
    ),
    TenderColumnConfig(
      title: "批次号",
      type: TenderColumnType.batchNumber,
      isShow: true,
    ),
    TenderColumnConfig(title: "规格", type: TenderColumnType.spec, isShow: false),
    TenderColumnConfig(
      title: "型号",
      type: TenderColumnType.model,
      isShow: false,
    ),
    TenderColumnConfig(
      title: "数量",
      type: TenderColumnType.number,
      isRequired: true,
      isShow: true,
    ),
    TenderColumnConfig(
      title: "单位",
      type: TenderColumnType.unit,
      isRequired: true,
      isShow: true,
    ),
  ]);

  return configList;
}
