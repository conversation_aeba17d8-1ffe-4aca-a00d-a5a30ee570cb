///
///@ClassName: print_config_dto
///@Description: 打印配置实体
///@Author: tanglan
///@Date: 2025/4/27
class PrintConfigInfo {
  String id = "0";
  String profileId = "0";

  ///门店id
  String otypeId = "0";

  ///收银机id
  String cashierId = "0";

  ///配置类型（目前和单据保持一致）
  String printType = "";

  ///打印字段key
  String fieldKey = "";

  ///是否选中
  bool selected = false;

  ///自定义展示名称
  String customFieldName = "";

  ///位置(0=居左 1=居中 2=居右)
  int printLocation = 1;

  ///是否同步其他模板(0=否 1=是)
  bool syncOtherTemplate = false;

  ///特殊配置(使用json进行存储)
  String contentConfig = "";

  PrintConfigInfo();

  factory PrintConfigInfo.fromMap(Map<String, dynamic>? map) {
    if (map == null) return PrintConfigInfo();
    PrintConfigInfo printConfigDto = PrintConfigInfo();
    printConfigDto.id = map['id'] ?? "0";
    printConfigDto.profileId = map['profileId'] ?? "0";
    printConfigDto.otypeId = map['otypeId'] ?? "0";
    printConfigDto.cashierId = map['cashierId'] ?? "0";
    printConfigDto.printType = map['printType'] ?? "";
    printConfigDto.fieldKey = map['fieldKey'] ?? "";
    printConfigDto.selected = map['selected'] ?? false;
    printConfigDto.customFieldName = map['customFieldName'] ?? "";
    printConfigDto.printLocation = map['printLocation'] ?? 1;
    printConfigDto.contentConfig = map['contentConfig'] ?? "";
    printConfigDto.syncOtherTemplate = map['syncOtherTemplate'] ?? false;
    return printConfigDto;
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "profileId": profileId,
    "otypeId": otypeId,
    "cashierId": cashierId,
    "printType": printType,
    "fieldKey": fieldKey,
    "selected": selected,
    "customFieldName": customFieldName,
    "printLocation": printLocation,
    "contentConfig": contentConfig,
    "syncOtherTemplate": syncOtherTemplate,
  };
}
