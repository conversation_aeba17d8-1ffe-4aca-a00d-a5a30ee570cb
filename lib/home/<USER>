import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/haloui.dart';

import '../../../bill/bill/bill_edit_sale_fresh.dart';
import '../../../bill/bill/bill_edit_sale_normal.dart';
import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/tool/sale_event_buds.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/setting/bill_page_type.dart';
import '../../../home/<USER>/custom_app_bar.dart';
import '../../../home/<USER>/menu.dart';
import '../../../plugin/secondary_screen_plugin.dart';
import '../../../vip/recharge/vip_recharge_page.dart';
import '../application.dart';
import '../common/string_res.dart';
import '../common/tool/dialog_util.dart';
import '../common/tool/sync_info_dialog.dart';
import '../common/tool/version_check.dart';
import '../hotkey/hotkey_manager.dart';
import '../offline/connnect_tool.dart';
import '../offline/offline_tool.dart';
import '../plugin/secondary_screen_windows.dart';
import '../widgets/base/halo_pos_alert_dialog.dart';

///
///@ClassName: root
///@Description: 框架页
///@Author: tanglan
///@Date: 7/27/21 1:23 PM
typedef OnMenuResultCallback = Future Function();

class Root extends StatefulWidget {
  const Root({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _RootState();
}

class _RootState extends State<Root> {
  num billPageType = SpTool.getSetting().billPageType;
  DateTime? _lastBackTime;
  GoodsBillDto goodsBillDto = GoodsBillDto();
  bool hasNewVersion = false;
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  StreamSubscription? subscription;

  @override
  void initState() {
    super.initState();
    HotkeyManager.registerAllHotkey();
    ConnectTool().addConnectListen(context);

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);

    SecondaryScreenPlugin.showSecondaryScreenImage(firstShow: true);
    if (Platform.isWindows && SpTool.getSetting().enableSecondScreen) {
      showSecondaryWindow();
    }
    initData();
    // DecimalDisplayHelper.instance.getDecimal();
    subscription = SaleEventBus.getInstance().on<String>().listen((event) {
      if (event == SaleEventBus.updateSaleBillView) {
        setState(() {
          billPageType = SpTool.getSetting().billPageType;
        });
      }
    });
  }

  Future<void> initData() async {
    hasNewVersion = await VersionCheck.haveNewVersion();
    setState(() {});
  }

  @override
  void dispose() {
    HotkeyManager.unregisterAllHotkey();
    HotkeyManager.unregisterAllCallback();
    subscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Application.init(context);
    return PopScope(
      canPop: false,
      //点击两次返回按钮退出程序
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        _pop(context);
      },
      child: Scaffold(
        key: scaffoldKey,
        resizeToAvoidBottomInset: false,
        drawerScrimColor: Colors.transparent,
        drawerEnableOpenDragGesture: false,
        endDrawerEnableOpenDragGesture: false,
        backgroundColor: Colors.white,
        drawer: _buildDrawer(context),
        // endDrawer: _buildEndDrawer(),
        appBar: _buildAppbar(context),
        body: _billEditSaleHome(),
      ),
    );
  }

  _billEditSaleHome() {
    if (billPageType == BillPageType.SCAN_AND_SELECT.index) {
      return BillEditSaleFreshPage(
        submitCallback: () {
          // scaffoldKey.currentState.openEndDrawer();
        },
      );
    } else {
      return BillEditSaleNormalPage(
        submitCallback: () {
          // scaffoldKey.currentState.openEndDrawer();
        },
      );
    }
  }

  // _buildEndDrawer() {
  //   return SlipSettlementPage();
  // }

  void _menuClick(BuildContext context) {
    Scaffold.of(context).openDrawer();
  }

  void _syncClick(BuildContext context) {
    if (OffLineTool().isOfflineLogin) {
      HaloPosAlertDialog.showAlertDialog(
        context,
        content: "离线登录情况仅支持基本开单功能，不能同步信息，请连接网络并正常登录",
        showCancel: false,
        showTitle: false,
      );
      return;
    }
    DialogUtil.showAlertDialog(context, child: const SyncInfoDialog());
  }

  //TODO 线上客服点击
  void _onlineClick(BuildContext context) {
    HaloDialog(
      context,
      height: 100.h,
      width: 100.h,
      child: Center(
        child: HaloContainer(
          padding: EdgeInsets.only(
            top: 48.h,
            left: 24.w,
            right: 24.w,
            bottom: 16.w,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          crossAxisAlignment: CrossAxisAlignment.end,
          direction: Axis.vertical,
          color: Colors.white,
          children: [
            Text(
              "请拨打028-85310000转000进行咨询",
              style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: ScreenUtil().setSp(24),
              ),
            ),
            SizedBox(height: 48.h),
            HaloButton(
              text: "确定",
              buttonType: HaloButtonType.outlinedButton,
              height: 42.h,
              outLineWidth: 1.h,
              onPressed: () {
                NavigateUtil.pop(context);
              },
            ),
          ],
        ),
      ),
    ).show();
  }

  //TODO 消息中心点击
  void _messageClick(BuildContext context) {
    HaloToast.show(context, msg: "暂时不支持消息功能");
  }

  //region  构建组件
  AppBar _buildAppbar(BuildContext context) {
    return AppBar(
      titleSpacing: 0,
      elevation: 1,
      toolbarHeight: 70.w,
      leading: Container(),
      leadingWidth: 0,
      actions: [Container()],
      title: Builder(
        builder: (BuildContext context) {
          return CustomAppBar(
            syncClick: () => _syncClick(context),
            menuClick: () => _menuClick(context),
            messageClick: () => _messageClick(context),
            onlineClick: () => _onlineClick(context),
            hasNewVersion: hasNewVersion,
          );
        },
      ),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Menu((builder) async {
      Widget target = builder(context);
      Future? result = await NavigateUtil.navigateTo(context, target);
      if (target is VipRechargePage) {
        /// _globalKey.currentState.refreshVipInfo(_globalKey.currentContext);
        SaleEventBus.getInstance().fire(SaleEventBus.updateVip);
      }
      return result;
    });
  }

  //endregion

  //点击返回按钮
  void _pop(BuildContext context) {
    if (_lastBackTime == null ||
        DateTime.now().difference(_lastBackTime!) >
            const Duration(seconds: 2)) {
      _lastBackTime = DateTime.now();
      HaloToast.show(
        context,
        msg: StringRes.QUITE_AFTER_CLICK_AGAIN.getText(context),
      );
    } else {
      SystemNavigator.pop();
    }
  }
}
