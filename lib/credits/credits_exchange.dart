import 'dart:io';

import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import './../bill/entity/ptype/ptype_prop_dto.dart';
import './../bill/entity/ptype_suit_model.dart';
import './../bill/model/bill_model.dart';
import './../bill/tool/scan_tool.dart';
import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../bill/model/ptype_model.dart';
import '../bill/tool/promotion/card.dart';
import '../common/login/login_center.dart';
import '../common/style/app_color_helper.dart';
import '../common/tool/dialog_util.dart';
import '../common/tool/sp_tool.dart';
import '../entity/system/permission_dto.dart';
import '../enum/bill_post_state.dart';
import '../enum/bill_type.dart';
import '../iconfont/icon_font.dart';
import '../plugin/secondary_screen_windows.dart';
import '../startup.dart';
import '../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../vip/utils/svip_util.dart';
import '../widgets/base/base_stateful_page.dart';
import '../widgets/halo_pos_label.dart';
import 'credits_commit.dart';
import 'credits_detail_item.dart';
import 'entity/promotion_credits_dto.dart';
import 'entity/promotion_credits_entity.dart';
import 'entity/promotion_exchange_goods_requset.dart';
import 'model/credits_model.dart';

///积分兑换页面
class CreditsExchangePages extends BaseStatefulPage {
  final VipWithLevelAssertsRightsCardDTO vipInfo;

  const CreditsExchangePages({Key? key, required this.vipInfo})
      : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _CreditsExchangePagesState();
}

class _CreditsExchangePagesState
    extends BaseStatefulPageState<CreditsExchangePages> {
  List<String> sortStr = ["默认排序", "积分从低到高", "积分从高到低"];
  List<String> sortCreditsStr = [
    "全部积分",
    "1-100",
    "101-1000",
    "1001-3000",
    "3001以上"
  ];
  int sorStrIndex = 0;
  int _sortCreditsStrIndex = 0;
  late PermissionDto permissionDto;

  int get sortCreditsStrIndex {
    return _sortCreditsStrIndex;
  }

  set sortCreditsStrIndex(int value) {
    _sortCreditsStrIndex = value;
    if (_sortCreditsStrIndex == 0) {
      betweenMin = null;
      betweenMax = null;
      return;
    }
    if (_sortCreditsStrIndex == 1) {
      betweenMin = 1;
      betweenMax = 100;
      return;
    }
    if (_sortCreditsStrIndex == 2) {
      betweenMin = 101;
      betweenMax = 1000;
      return;
    }
    if (_sortCreditsStrIndex == 3) {
      betweenMin = 1001;
      betweenMax = 3000;
      return;
    }
    if (_sortCreditsStrIndex == 4) {
      betweenMin = 3001;
      betweenMax = null;
      return;
    }
  }

  int? betweenMin;
  int? betweenMax;

  int? page;
  int? pageSize;
  int? total;

  bool isHaveMore = false;
  int? clickIndex;

  PromotionCreditsEntity creditsEntity = PromotionCreditsEntity();
  List<PromotionCreditsDTO> list = [];

  @override
  void initState() {
    super.initState();
    page = 1;
    pageSize = 20;
    showScoreExchangeForWin([]);
    initData();
    permissionDto = SpTool.getPermission();
  }

  @override
  void dispose() {
    super.dispose();
    hideScoreExchangeForWin();
  }

  initData() {
    CreditsModel.getPromotionCreditsList(
            context, page!, pageSize!, sorStrIndex, betweenMin, betweenMax)
        .then((value) {
      if (!mounted) return;
      setState(() {
        creditsEntity = value!;
        if (page == 1) {
          list.clear();
          isHaveMore = true;
        }
        total = int.parse(creditsEntity.total!);
        list.addAll(creditsEntity.list ?? []);
        list = list.where((element) => element.creditsName != null).toList();
        if (Platform.isWindows) {
          showScoreExchangeForWin(list
              .map((e) => ScoreExchangeForWindows(
                  name: e.getName(),
                  score: e.preferential,
                  count: e.changeCount,
                  picUrl: e.picUrl,
                  ptypeGroup: e.ptypeGroup,
                  valueType: int.tryParse(e.valueType ?? "")))
              .toList());
        }
      });
    });
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    if (list.isEmpty) {
      return Center(
        child: HaloEmptyContainer(
          gravity: EmptyGravity.CENTER,
          image: Image.asset('assets/images/nodata.png'),
          title: "暂无数据",
          titleStyle: TextStyle(
              decoration: TextDecoration.none,
              fontSize: ScreenUtil().setSp(30),
              color: Colors.grey),
        ),
      );
    }
    return EasyRefresh(
      header: MaterialHeader(),
      footer: MaterialFooter(),
      behavior: Platform.isWindows ? WindowsScrollBehavior() : null,
      child: GridView.builder(
          itemCount: list.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 20.w,
              mainAxisSpacing: 23.w,
              mainAxisExtent: 414.w),
          itemBuilder: (BuildContext context, int index) {
            return GestureDetector(
              onTap: () {
                clickIndex = index;
                setState(() {});
                DialogUtil.showAlertDialog(context,
                    child: CreditsCommit(
                      promotionCreditsDTO: list[index],
                      onCommit: () async {
                        if (isVipExpired(widget.vipInfo.vip!.validDate,
                            vipType: widget.vipInfo.level!.vipType == true
                                ? 1
                                : 0)) {
                          HaloToast.show(context, msg: "该会员已经过期，无法积分兑换");
                          return;
                        }
                        PromotionCreditsDTO promotionCreditsDTO = list[index];
                        if (check(promotionCreditsDTO)) {
                          ExchangeGoodsRequest request =
                              buildRequestData(promotionCreditsDTO);
                          GoodsBillDto? goodsBillDto;
                          //商品或套餐
                          if (promotionCreditsDTO.ptypeGroup == 3 ||
                              promotionCreditsDTO.ptypeGroup == 2) {
                            goodsBillDto = await _getGoodsBill(
                                context, promotionCreditsDTO);
                            if (goodsBillDto == null) {
                              return;
                            } else {
                              goodsBillDto.memo = creditsExchange;
                            }
                          }
                          goodsBillDto?.date =
                              DateUtil.formatDate(DateTime.now());
                          goodsBillDto?.storeManagerId =
                              SpTool.getStoreInfo()?.ownerId ?? "";
                          request.goodsBill = goodsBillDto;
                          request.cashierId = SpTool.getCashierInfo().id;
                          if (!mounted) return;
                          CreditsModel.exchangeCreditsGoods(context, request)
                              .then((value) {
                            Navigator.pop(context);
                            if (value != null && value["code"] == "500") {
                              showDialog(
                                  context: context,
                                  builder: (context) =>
                                      _FailDialog(value["message"]));
                            } else {
                              PtypeModel.changePtypeStockQtyByGoodsBill(
                                  outDetail: goodsBillDto?.outDetail ?? []);
                              HaloToast.showSuccess(context, msg: "兑换成功");
                              setState(() {
                                promotionCreditsDTO.changeCount++;
                                widget.vipInfo.asserts!.availableScore =
                                    widget.vipInfo.asserts!.availableScore! -
                                        int.parse(
                                            promotionCreditsDTO.preferential!);
                              });
                            }
                          });
                        }
                      },
                      onCancel: () {
                        clickIndex = null;
                        setState(() {});
                      },
                    )).then((value) => {});
              },
              child: CreditsDetailItem(
                list[index],
                isSelect: clickIndex == index ? true : false,
              ),
            );
          }),
      onRefresh: () async {
        page = 1;
        initData();
      },
      onLoad: () async {
        if (isHaveMore) {
          page = page! + 1;
          initData();
        }
      },
    );
  }

  ///拉取一张出库单
  Future<GoodsBillDto?> _getGoodsBill(
      BuildContext context, PromotionCreditsDTO promotionCreditsDTO) async {
    if (promotionCreditsDTO.ptypeGroup != 2 &&
        promotionCreditsDTO.ptypeGroup != 3) {
      return null;
    }

    //拉取一张新单据
    GoodsBillDto? goodsBillDto = await BillModel.getGoodsBill(
      context,
      "", //vchcode
      BillTypeData[BillType.SaleBill], //vchtype
      BillBusinessTypeString[BillBusinessType.SaleNormal],
    );
    if (goodsBillDto == null || goodsBillDto.vchcode == null) {
      if (mounted) {
        HaloToast.show(context, msg: "获取单据失败");
      }
      return null;
    }
    //拉去好的单据填入基本信息
    var loginUser = LoginCenter.getLoginUser();
    var storeInfo = SpTool.getStoreInfo()!;
    goodsBillDto.createEtypeId = loginUser.employeeId;
    goodsBillDto.vipCardId = widget.vipInfo.vip?.id;
    goodsBillDto.createEfullname = loginUser.user;
    goodsBillDto.createUserCode = loginUser.userCode ?? "";
    goodsBillDto.ktypeId = storeInfo.ktypeId;
    goodsBillDto.kfullname = storeInfo.ktypeName;
    goodsBillDto.btypeId = storeInfo.btypeId;
    goodsBillDto.bfullname = storeInfo.btypeName;
    goodsBillDto.otypeId = storeInfo.otypeId;
    goodsBillDto.ofullname = storeInfo.otypeFullname;
    goodsBillDto.etypeId = loginUser.employeeId;
    goodsBillDto.efullname = loginUser.user ?? "";
    goodsBillDto.source = "积分兑换";
    //这一坨不知道干嘛的
    goodsBillDto.freightAtypeName = "";
    goodsBillDto.freightBillNo = "";
    goodsBillDto.freightBtypeId = "0";
    goodsBillDto.freightBtypeName = "";
    goodsBillDto.freightaTypeTotal = "0";
    goodsBillDto.shareType = 0;
    goodsBillDto.dfullname = "";
    goodsBillDto.billType = "goodsBill";
    goodsBillDto.postState =
        BillPostStateString[BillPostState.PROCESS_COMPLETED];
    goodsBillDto.confirm = true; //避免各种奇奇怪怪的单据异常导致需要再次确认
    goodsBillDto.outDetail;
    //单据金额
    goodsBillDto.currencyBillTotal = "0";
    //处理套餐
    if (promotionCreditsDTO.ptypeGroup == 2) {
      if (!mounted) return null;
      //根据套餐id拉取套餐明细
      List<GoodsDetailDto>? comboGoodsList =
          await getComboById(context, promotionCreditsDTO.ptypeId!);
      if (comboGoodsList == null || comboGoodsList.isEmpty) {
        if (mounted) HaloToast.show(context, msg: "获取套餐信息失败");
        return null;
      }
      goodsBillDto.outDetail.addAll(comboGoodsList);
    }
    //处理普通商品
    else if (promotionCreditsDTO.ptypeGroup == 3) {
      List<PtypePropDto> prop = [];
      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId1) &&
          promotionCreditsDTO.propvalueId1 != "0") {
        prop.add(PtypePropDto()
          ..propvalueId = promotionCreditsDTO.propvalueId1
          ..propvalueName = promotionCreditsDTO.propValueName1);
      }
      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId2) &&
          promotionCreditsDTO.propvalueId2 != "0") {
        prop.add(PtypePropDto()
          ..propvalueId = promotionCreditsDTO.propvalueId2
          ..propvalueName = promotionCreditsDTO.propValueName2);
      }

      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId3) &&
          promotionCreditsDTO.propvalueId3 != "0") {
        prop.add(PtypePropDto()
          ..propvalueId = promotionCreditsDTO.propvalueId3
          ..propvalueName = promotionCreditsDTO.propValueName3);
      }

      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId4) &&
          promotionCreditsDTO.propvalueId4 != "0") {
        prop.add(PtypePropDto()
          ..propvalueId = promotionCreditsDTO.propvalueId4
          ..propvalueName = promotionCreditsDTO.propValueName4);
      }

      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId5) &&
          promotionCreditsDTO.propvalueId5 != "0") {
        prop.add(PtypePropDto()
          ..propvalueId = promotionCreditsDTO.propvalueId5
          ..propvalueName = promotionCreditsDTO.propValueName5);
      }

      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId6) &&
          promotionCreditsDTO.propvalueId6 != "0") {
        prop.add(PtypePropDto()
          ..propvalueId = promotionCreditsDTO.propvalueId6
          ..propvalueName = promotionCreditsDTO.propValueName6);
      }

      GoodsDetailDto dto = GoodsDetailDto()
        ..prop = prop
        ..kfullname = storeInfo.ktypeName
        ..ktypeId = storeInfo.ktypeId
        ..pFullName = promotionCreditsDTO.creditsName ?? ""
        ..ptypeId = promotionCreditsDTO.ptypeId ?? "0"
        ..skuId = promotionCreditsDTO.skuId ?? "0"
        ..unitId = promotionCreditsDTO.unitId ?? "0"
        ..unitRate = num.tryParse(promotionCreditsDTO.unitRate ?? "1") ?? 1
        ..unitName = promotionCreditsDTO.unitName ?? ""
        ..unitQty = 1
        ..stockQty = 1
        ..fullbarcode = promotionCreditsDTO.fullbarcode ?? ""
        ..batchenabled = promotionCreditsDTO.batchenabled == 1
        ..picUrl = promotionCreditsDTO.picUrl
        ..costMode = promotionCreditsDTO.costMode ?? 0
        ..snenabled = promotionCreditsDTO.snenabled ?? 0
        ..batchNo = promotionCreditsDTO.batchNo ?? ""
        ..serialNoList = promotionCreditsDTO.serialNoList
        ..produceDate = promotionCreditsDTO.produceDate
        ..expireDate = promotionCreditsDTO.expireDate
        ..pUserCode = promotionCreditsDTO.pUserCode
        ..costId = promotionCreditsDTO.costId
        ..batchPrice = promotionCreditsDTO.batchPrice
      ..pcategory = promotionCreditsDTO.pcategory??0;
      goodsBillDto.outDetail.add(dto);
    } else {
      return null;
    }
    if (!mounted) return null;
    //处理自动带出批次
    // await ScanTool.getPtypeAutoBatch(goodsBillDto.outDetail, context);
    for (int i = 0; i < goodsBillDto.outDetail.length; i++) {
      GoodsDetailDto dto = goodsBillDto.outDetail[i];
      dto.currencyPrice = 0;
      dto.currencyTaxTotal = 0;
      dto.currencyTotal = 0;
      dto.currencyDisedPrice = 0;
      dto.currencyDisedTaxedPrice = 0;
      dto.currencyDisedTaxedTotal = 0;
      dto.currencyDisedTotal = 0;
      dto.preferentialDiscount = 0;
      dto.memo = "促销：积分兑换";
      // dto.costPrice = 0;
      //排除套餐行
      if (dto.comboRow) {
        continue;
      }
      if (!mounted) return null;
      //处理批次和序列号，如果有任意商品没有选择序列号，则中断流程
      // List<GoodsDetailDto>? result = await ScanTool.handleScanResult(
      //     context, dto, goodsBillDto.outDetail, BillType.SaleBill,
      //     snLimitGoodsCount: true);
      // if (result?.any((element) => element.batchNo.isEmpty) == true) {
      //   if (mounted) HaloToast.show(context, msg: "请选择批次号");
      //   return null;
      // }
    }
    return goodsBillDto;
  }

  ///获取套餐
  Future<List<GoodsDetailDto>?> getComboById(
      BuildContext context, String comboId) async {
    PtypeSuitModel? combo = await BillModel.getComboById(context, comboId);
    if (null != combo) {
      combo.count = 1;
      return ScanTool.transformComboToGoodsList(combo, BillType.SaleBill);
    }
    return null;
  }

  @override
  String getActionBarTitle() => "";

  @override
  buildAppBar() {
    return PreferredSize(
        preferredSize: Size.fromHeight(80.h),
        child: AppBar(
          backgroundColor: AppColorHelper(context).getAppBarColor(),
          titleSpacing: 0.0,
          toolbarHeight: 80.h,
          automaticallyImplyLeading: false,
          title: Container(
            alignment: Alignment.center,
            height: 80.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(children: [
                  _buildBack(),
                  HaloPosLabel("积分兑换",
                      textStyle: TextStyle(
                          color:
                              AppColorHelper(context).getTitleBoldTextColor(),
                          fontSize: widget.titleTextSize.sp)),
                ]),
                _buildVip(),
              ],
            ),
          ),
          centerTitle: false,
        ));
  }

  /*返回*/
  _buildBack() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => NavigateUtil.pop(context),
      child: Container(
          padding: EdgeInsets.zero,
          width: 80.w,
          height: double.infinity,
          alignment: Alignment.center,
          child: IconFont(
            IconNames.ngp_left_back,
            size: 32.w,
            color: ColorUtil.color2String(
                AppColorHelper(context).getTitleBoldTextColor()),
          )),
    );
  }

  //会员信息
  _buildVip() {
    return GestureDetector(
      child: HaloContainer(
        padding: EdgeInsets.only(right: 10.w),
        children: [
          HaloPosLabel(
            widget.vipInfo.vip?.name ?? "",
            textStyle: TextStyle(fontSize: 24.sp, color: Colors.black),
          ),
          RichText(
              text: TextSpan(
                  text: "(可用积分：",
                  style: TextStyle(
                      fontSize: 24.sp,
                      color: Colors.black,
                      fontWeight: FontWeight.bold),
                  children: [
                TextSpan(
                    text: widget.vipInfo.asserts?.availableScore.toString(),
                    style: TextStyle(
                        fontSize: 24.sp,
                        color: Colors.red,
                        fontWeight: FontWeight.bold)),
                TextSpan(
                    text: "分)",
                    style: TextStyle(
                        fontSize: 24.sp,
                        color: Colors.black,
                        fontWeight: FontWeight.bold))
              ]))
        ],
      ),
    );
  }

  @override
  Widget buildTopBody(BuildContext context) {
    return Container(
      height: 144.w,
      width: double.infinity,
      margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 23.w, bottom: 20.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.w), color: Colors.white),
      child: HaloContainer(
        direction: Axis.vertical,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: CustomRadioList(
              titles: sortStr,
              selectIndex: sorStrIndex,
              onChange: (index) {
                setState(() {
                  sorStrIndex = index;
                  page = 1;
                  initData();
                });
              },
            ),
          ),
          Divider(
            color: ColorUtil.stringColor("#DBDBDB"),
            height: 1.w,
          ),
          Expanded(
            child: CustomRadioList(
              titles: sortCreditsStr,
              selectIndex: sortCreditsStrIndex,
              onChange: (index) {
                setState(() {
                  sortCreditsStrIndex = index;
                  page = 1;
                  initData();
                });
              },
            ),
          )
        ],
      ),
    );
  }

  ExchangeGoodsRequest buildRequestData(
      PromotionCreditsDTO promotionCreditsDTO) {
    ExchangeGoodsRequest request = ExchangeGoodsRequest();
    request.vipId = widget.vipInfo.vip?.id;
    request.ktypeId = SpTool.getStoreInfo()!.ktypeId;
    request.etypeId = LoginCenter.getLoginUser().employeeId;
    request.otypeId = SpTool.getStoreInfo()!.otypeId;
    request.btypeId = SpTool.getStoreInfo()!.btypeId;
    request.propValueId1 = promotionCreditsDTO.propvalueId1;
    request.propValueId2 = promotionCreditsDTO.propvalueId2;
    request.propValueId3 = promotionCreditsDTO.propvalueId3;
    request.propValueId4 = promotionCreditsDTO.propvalueId4;
    request.propValueId5 = promotionCreditsDTO.propvalueId5;
    request.propValueId6 = promotionCreditsDTO.propvalueId6;

    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName1)) {
      request.propValueName1 = promotionCreditsDTO.propValueName1;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName2)) {
      request.propValueName2 = promotionCreditsDTO.propValueName2;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName3)) {
      request.propValueName3 = promotionCreditsDTO.propValueName3;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName4)) {
      request.propValueName4 = promotionCreditsDTO.propValueName4;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName5)) {
      request.propValueName5 = promotionCreditsDTO.propValueName5;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName6)) {
      request.propValueName6 = promotionCreditsDTO.propValueName6;
    }

    request.ptypeGroup = promotionCreditsDTO.ptypeGroup;
    request.pid = promotionCreditsDTO.pid;
    request.score = Decimal.parse(promotionCreditsDTO.preferential!);
    request.amount = Decimal.parse(promotionCreditsDTO.price.toString());
    request.skuId = promotionCreditsDTO.skuId;
    request.unitId = promotionCreditsDTO.unitId;
    request.promotionPtypeId = promotionCreditsDTO.promotionPtypeId;
    request.changeCount = promotionCreditsDTO.changeCount;
    request.unitRate = promotionCreditsDTO.unitRate;
    request.ptypeId = promotionCreditsDTO.ptypeId;
    request.fullbarcode = promotionCreditsDTO.fullbarcode;
    return request;
  }

  bool check(PromotionCreditsDTO promotionCreditsDTO) {
    if (widget.vipInfo.asserts!.availableScore! <
        int.parse(promotionCreditsDTO.preferential!)) {
      HaloToast.showError(context, msg: "会员积分不足!");
      return false;
    }
    return true;
  }

  @override
  Future<void> onInitState() async {}
}

class CustomRadioList extends StatelessWidget {
  final List<String> titles;
  final int selectIndex;
  final Function(int) onChange;

  const CustomRadioList(
      {Key? key,
      required this.titles,
      required this.selectIndex,
      required this.onChange})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      children: _buildItem(),
    );
  }

  List<Widget> _buildItem() {
    return titles.map((e) {
      return GestureDetector(
        onTap: () {
          onChange(titles.indexOf(e));
        },
        child: Padding(
          padding: EdgeInsets.only(left: 40.w, right: 40.w),
          child: HaloPosLabel(e,
              textStyle: TextStyle(
                  fontSize: 24.sp,
                  color: titles.indexOf(e) == selectIndex
                      ? ColorUtil.stringColor("#4679FC")
                      : Colors.black,
                  height: 1)),
        ),
      );
    }).toList();
  }
}

///兑换失败弹窗
class _FailDialog extends StatelessWidget {
  final String message;

  const _FailDialog(this.message, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      child: SizedBox(
        width: 488.w,
        height: 444.h,
        child: Dialog(
          insetPadding: EdgeInsets.zero,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.w),
            ),
            padding: EdgeInsets.only(
                top: 50.h, bottom: 60.h, left: 46.w, right: 46.w),
            child: Column(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Icon(Icons.error, color: Colors.red, size: 72.w),
                      Padding(
                        padding: EdgeInsets.only(top: 22.h, bottom: 14.h),
                        child: Text(
                          "兑换失败",
                          style: TextStyle(
                              color: const Color(0xFF333333),
                              fontSize: 30.sp,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                      Text(
                        message,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: const Color(0xFF666666),
                          fontSize: 24.sp,
                        ),
                      )
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    width: 236.w,
                    height: 66.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.w),
                      border: Border.all(
                          color: const Color(0xFF999999), width: 2.w),
                    ),
                    child: Text(
                      "关闭",
                      style: TextStyle(
                        color: const Color(0xFF333333),
                        fontSize: 26.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
