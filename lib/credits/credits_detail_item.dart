import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import 'entity/promotion_credits_dto.dart';

class CreditsDetailItem extends StatelessWidget {
  final PromotionCreditsDTO promotionCreditsDTO;
  final bool isSelect;

  const CreditsDetailItem(this.promotionCreditsDTO, {Key? key, this.isSelect = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    BoxDecoration decoration = BoxDecoration(
        borderRadius: BorderRadius.circular(8.w), color: Colors.white);
    if (isSelect) {
      decoration = BoxDecoration(
          borderRadius: BorderRadius.circular(8.w),
          color: Colors.white,
          border: Border.fromBorderSide(
            BorderSide(color: ColorUtil.stringColor("#4679FC")),
          ));
    }
    return Container(
      decoration: decoration,
      child: HaloContainer(
        direction: Axis.vertical,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(left: 25.w, right: 25.w, top: 25.w),
            width: double.infinity,
            height: 250.w,
            child: PromotionCreditsDTO.getPic(
                ptypeGroup: promotionCreditsDTO.ptypeGroup,
                picUrl: promotionCreditsDTO.picUrl ?? "",
                valueType: int.tryParse(
                    promotionCreditsDTO.valueType ?? "")),
          ),
          Expanded(
              child: Padding(
            padding: EdgeInsets.only(left: 25.w, right: 25.w, top: 20.w),
            child: HaloPosLabel(
              promotionCreditsDTO.ptypeGroup! == 7
                  ? "储值金额：${promotionCreditsDTO.price}元"
                  : promotionCreditsDTO.getName()!,
              textStyle: TextStyle(
                  fontSize: 26.w, color: ColorUtil.stringColor("#333333")),
            ),
          )),
          Expanded(
              child: Padding(
            padding: EdgeInsets.only(left: 25.w, right: 25.w),
            child: HaloPosLabel(
              "${promotionCreditsDTO.preferential!}积分",
              textStyle: TextStyle(
                  fontSize: 30.w, color: ColorUtil.stringColor("#FF4141")),
            ),
          )),
          Expanded(
              child: Padding(
            padding: EdgeInsets.only(left: 25.w, right: 25.w),
            child: HaloPosLabel(
              "已兑换${promotionCreditsDTO.changeCount}次",
              textStyle: TextStyle(
                  fontSize: 24.w, color: ColorUtil.stringColor("#999999")),
            ),
          ))
        ],
      ),
    );
  }
}
