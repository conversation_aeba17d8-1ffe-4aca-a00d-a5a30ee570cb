///数据库相关配置
class DatabaseConfig {
  ///是否开启本地缓存
  bool enableLocalPtype;

  ///登录后是否自动同步数据
  bool loginSync;

  ///是否自动同步库存（定时同步）
  bool autoSyncStock;

  ///是否包含本地商品数据，
  ///只要同步过一次数据库，则此字段必定为true
  bool hasPtypeData;

  ///商品表时间戳
  int ptypeTableTimestamp;

  ///商品表时间戳
  int skuTableTimestamp;

  ///unit表时间戳
  int unitTableTimestamp;

  ///商品价格表时间戳
  int ptypePriceTableTimestamp;

  ///门店价格本表时间戳
  int otypePriceTableTimestamp;

  ///库存表时间戳
  int stockTableTimestamp;

  ///商品权限表时间戳
  int ptypeLimitTableTimestamp;

  ///套餐明细表时间戳
  int comboDetailTableTimestamp;

  ///商品条码表时间戳
  int fullBarcodeTableTimestamp;

  ///商品条码表时间戳
  int xcodeTableTimestamp;

  ///批量设置商品所有表格时间戳
  set ptypeTimestamp(int timeStamp) {
    ptypeTableTimestamp = timeStamp;
    skuTableTimestamp = timeStamp;
    unitTableTimestamp = timeStamp;
    ptypePriceTableTimestamp = timeStamp;
    otypePriceTableTimestamp = timeStamp;
    stockTableTimestamp = timeStamp;
    ptypeLimitTableTimestamp = timeStamp;
    comboDetailTableTimestamp = timeStamp;
    fullBarcodeTableTimestamp = timeStamp;
    xcodeTableTimestamp = timeStamp;
  }

  DatabaseConfig({
    bool? loginSync,
    bool? autoSyncStock,
    bool? enableLocalPtype,
    bool? hasPtypeData,
    int? ptypeTableTimestamp,
    int? skuTableTimestamp,
    int? unitTableTimestamp,
    int? ptypePriceTableTimestamp,
    int? otypePriceTableTimestamp,
    int? stockTableTimestamp,
    int? ptypeLimitTableTimestamp,
    int? comboDetailTableTimestamp,
    int? fullBarcodeTableTimestamp,
    int? xcodeTableTimestamp,
  })  : loginSync = loginSync ?? true,
        autoSyncStock = autoSyncStock ?? true,
        enableLocalPtype = enableLocalPtype ?? false,
        hasPtypeData = hasPtypeData ?? false,
        ptypeTableTimestamp = ptypeTableTimestamp ?? 0,
        skuTableTimestamp = skuTableTimestamp ?? 0,
        unitTableTimestamp = unitTableTimestamp ?? 0,
        ptypePriceTableTimestamp = ptypePriceTableTimestamp ?? 0,
        otypePriceTableTimestamp = otypePriceTableTimestamp ?? 0,
        stockTableTimestamp = stockTableTimestamp ?? 0,
        ptypeLimitTableTimestamp = ptypeLimitTableTimestamp ?? 0,
        comboDetailTableTimestamp = comboDetailTableTimestamp ?? 0,
        fullBarcodeTableTimestamp = fullBarcodeTableTimestamp ?? 0,
        xcodeTableTimestamp = xcodeTableTimestamp ?? 0;

  Map<String, dynamic> toJson() {
    return {
      'loginSync': loginSync,
      'autoSyncStock': autoSyncStock,
      'enableLocalPtype': enableLocalPtype,
      'hasLocalData': hasPtypeData,
      'ptypeTableTimestamp': ptypeTableTimestamp,
      'skuTableTimestamp': skuTableTimestamp,
      'unitTableTimestamp': unitTableTimestamp,
      'ptypePriceTableTimestamp': ptypePriceTableTimestamp,
      'otypePriceTableTimestamp': otypePriceTableTimestamp,
      'stockTableTimestamp': stockTableTimestamp,
      'ptypeLimitTableTimestamp': ptypeLimitTableTimestamp,
      'comboDetailTableTimestamp': comboDetailTableTimestamp,
      'fullBarcodeTableTimestamp': fullBarcodeTableTimestamp,
      'xcodeTableTimestamp': xcodeTableTimestamp,
    };
  }

  DatabaseConfig.fromMap(Map<String, dynamic> map)
      : this(
          loginSync: map['loginSync'],
          autoSyncStock: map['autoSyncStock'],
          enableLocalPtype: map['enableLocalPtype'],
          hasPtypeData: map['hasLocalData'],
          ptypeTableTimestamp: map['ptypeTableTimestamp'],
          skuTableTimestamp: map['skuTableTimestamp'],
          unitTableTimestamp: map['unitTableTimestamp'],
          ptypePriceTableTimestamp: map['ptypePriceTableTimestamp'],
          otypePriceTableTimestamp: map['otypePriceTableTimestamp'],
          stockTableTimestamp: map['stockTableTimestamp'],
          ptypeLimitTableTimestamp: map['ptypeLimitTableTimestamp'],
          comboDetailTableTimestamp: map['comboDetailTableTimestamp'],
          fullBarcodeTableTimestamp: map['fullBarcodeTableTimestamp'],
          xcodeTableTimestamp: map['xcodeTableTimestamp'],
        );
}
