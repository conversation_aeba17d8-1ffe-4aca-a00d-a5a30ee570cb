///数据库商品表实体类
class PtypeDO {
  /// 商品ID
  String id;

  /// 帐套ID
  String profileId;

  /// 层级ID
  String typeid;

  /// 父类层级ID
  String partypeid;

  /// 商品编号
  String usercode;

  /// 商品全名
  String fullname;

  /// 商品简名
  String shortname;

  /// 拼音码
  String namepy;

  /// 分类标记
  bool classed;

  /// 商品规格
  String standard;

  /// 商品型号
  String ptypeType;

  /// 成本算法
  int costMode;

  /// 商品类型：0-实物商品，1-虚拟商品, 2-套餐商品
  int pcategory;

  /// 税率
  num taxRate;

  /// 参考成本价
  num costPrice;

  /// 是否限制仓库（0全部仓库可见  1部分仓库可见）
  bool ktypeLimit;

  /// 序列号管理:0=未启用，1=严格管理，2=宽松管理
  int snenabled;

  /// 是否启用属性管理
  bool propenabled;

  /// 是否开启批次管理
  bool batchenabled;

  /// 保质期天数(天):0不启用保质期 大于1启用
  int protectDays;

  /// 保质期表示方式:0天 1周 2月 3年
  int protectDaysUnit;

  /// 单位商品重量
  num weight;

  /// 重量单位:0-g,1-kg
  int weightUnit;

  /// SKU价格管理 0未启用 1启用
  int skuPrice;

  ///排序
  String rowindex;

  /// 条码，主要是套餐使用此字段作为条码
  String barcode;

  /// 商品图片，通过关联base_ptype_pic这张表来查询
  String? picUrl;

  PtypeDO({
    String? id,
    String? profileId,
    String? typeid,
    String? partypeid,
    String? usercode,
    String? fullname,
    String? shortname,
    String? namepy,
    bool? classed,
    String? standard,
    String? ptypeType,
    int? costMode,
    int? pcategory,
    num? taxRate,
    num? costPrice,
    bool? ktypeLimit,
    int? snenabled,
    bool? propenabled,
    bool? batchenabled,
    int? protectDays,
    int? protectDaysUnit,
    num? weight,
    int? weightUnit,
    int? skuPrice,
    String? barcode,
    String? rowindex,
    this.picUrl,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        typeid = typeid ?? "",
        partypeid = partypeid ?? "",
        usercode = usercode ?? "",
        fullname = fullname ?? "",
        shortname = shortname ?? "",
        namepy = namepy ?? "",
        classed = classed ?? false,
        standard = standard ?? "",
        ptypeType = ptypeType ?? "",
        costMode = costMode ?? 0,
        pcategory = pcategory ?? 0,
        taxRate = taxRate ?? 0,
        costPrice = costPrice ?? 0,
        ktypeLimit = ktypeLimit ?? false,
        snenabled = snenabled ?? 0,
        propenabled = propenabled ?? false,
        batchenabled = batchenabled ?? false,
        protectDays = protectDays ?? 0,
        protectDaysUnit = protectDaysUnit ?? 0,
        weight = weight ?? 0,
        weightUnit = weightUnit ?? 0,
        skuPrice = skuPrice ?? 0,
        rowindex = rowindex ?? "0",
        barcode = barcode ?? "";

  PtypeDO.fromMap(Map<String, dynamic> map)
      : this(
          id: map['id'],
          profileId: map['profileId'],
          typeid: map['typeid'],
          partypeid: map['partypeid'],
          usercode: map['usercode'],
          fullname: map['fullname'],
          shortname: map['shortname'],
          namepy: map['namepy'],
          classed: map['classed'],
          standard: map['standard'],
          ptypeType: map['ptypeType'],
          costMode: map['costMode'],
          pcategory: map['pcategory'],
          taxRate: map['taxRate'],
          costPrice: map['costPrice'],
          ktypeLimit: map['ktypeLimit'],
          snenabled: map['snenabled'],
          propenabled: map['propenabled'],
          batchenabled: map['batchenabled'],
          protectDays: map['protectDays'],
          protectDaysUnit: map['protectDaysUnit'],
          weight: map['weight'],
          weightUnit: map['weightUnit'],
          skuPrice: map['skuPrice'],
          barcode: map['barcode'],
          picUrl: map['picUrl'],
          rowindex: map['rowindex'],
        );

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'typeid': typeid,
      'partypeid': partypeid,
      'usercode': usercode,
      'fullname': fullname,
      'shortname': shortname,
      'namepy': namepy,
      'classed': classed,
      'standard': standard,
      'ptypeType': ptypeType,
      'costMode': costMode,
      'pcategory': pcategory,
      'taxRate': taxRate,
      'costPrice': costPrice,
      'ktypeLimit': ktypeLimit,
      'snenabled': snenabled,
      'propenabled': propenabled,
      'batchenabled': batchenabled,
      'protectDays': protectDays,
      'protectDaysUnit': protectDaysUnit,
      'weight': weight,
      'weightUnit': weightUnit,
      'skuPrice': skuPrice,
      'barcode': barcode,
      'picUrl': picUrl,
      'rowindex': rowindex,
    };
  }
}

///数据库商品sku库存实体类
class StockDO {
  /// skuId
  String id;

  /// 账套id
  String profileId;

  /// 仓库id
  String ktypeId;

  /// skuId
  String skuId;

  /// 商品id
  String ptypeId;

  /// 库存数量
  num qty;

  StockDO({
    String? id,
    String? profileId,
    String? ktypeId,
    String? skuId,
    String? ptypeId,
    num? qty,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        ktypeId = ktypeId ?? "0",
        skuId = skuId ?? "0",
        ptypeId = ptypeId ?? "0",
        qty = qty ?? 0;

  StockDO.fromMap(Map<String, dynamic> map)
      : this(
          id: map['id'],
          profileId: map['profileId'],
          ktypeId: map['ktypeId'],
          skuId: map['skuId'],
          ptypeId: map['ptypeId'],
          qty: map['qty'],
        );

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'ktypeId': ktypeId,
      'skuId': skuId,
      'ptypeId': ptypeId,
      'qty': qty,
    };
  }
}

/// 数据库商品价格实体类
/// 分为商品价格表
/// 和店铺价格本
/// 使用ptypeId、skuId、unitId关联商品，如果商品是套餐(pcategory = 2)或商品未开启sku定价(skuprice = false)时，则skuId为0，不关联sku。
/// 当代表店铺价格本中的价格时，还需关联门店id（xtype_id）,取
class PriceDO {
  /// skuId
  String id;

  /// 账套id
  String profileId;

  /// skuId
  String skuId;

  /// 商品id
  String ptypeId;

  /// 单位id
  String unitId;

  /// 零售价格，商品价格本取此价格
  num? retailPrice;

  /// 门店价格本中的销售价格
  num? salePrice;

  /// 门店价格本中的会员价格
  num? saleOtypeVipPrice;

  /// 门店id，当代表门店价格本中的价格时，此字段不为空
  String? xtypeId;

  PriceDO({
    String? id,
    String? profileId,
    String? skuId,
    String? ptypeId,
    String? unitId,
    this.retailPrice,
    this.salePrice,
    this.saleOtypeVipPrice,
    this.xtypeId,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        ptypeId = ptypeId ?? "0",
        skuId = skuId ?? "0",
        unitId = unitId ?? "0";

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'skuId': skuId,
      'ptypeId': ptypeId,
      'unitId': unitId,
      'retailPrice': retailPrice,
      'salePrice': salePrice,
      'saleOtypeVipPrice': saleOtypeVipPrice,
      'xtypeId': xtypeId,
    };
  }

  ///商品价格表对应的json
  Map<String, dynamic> toPtypePriceJson() => toJson()
    ..remove("salePrice")
    ..remove("saleOtypeVipPrice")
    ..remove("xtypeId");

  ///门店价格本对应的json
  Map<String, dynamic> toOtypePriceJson() => toJson()..remove("retailPrice");

  PriceDO.fromMap(Map<String, dynamic> map)
      : this(
          id: map['id'],
          profileId: map['profileId'],
          skuId: map['skuId'],
          ptypeId: map['ptypeId'],
          unitId: map['unitId'],
          retailPrice: map['retailPrice'],
          salePrice: map['salePrice'],
          saleOtypeVipPrice: map['saleOtypeVipPrice'],
          xtypeId: map['xtypeId'],
        );
}

///数据库商品权限实体类
class PtypeLimitDO {
  /// skuId
  String id;

  /// 账套id
  String profileId;

  /// 商品id
  String ptypeId;

  /// skuId
  String etypeId;

  PtypeLimitDO({
    String? id,
    String? profileId,
    String? ptypeId,
    String? etypeId,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        ptypeId = ptypeId ?? "0",
        etypeId = etypeId ?? "0";

  PtypeLimitDO.fromMap(Map<String, dynamic> map)
      : this(
          id: map['id'],
          profileId: map['profileId'],
          ptypeId: map['ptypeId'],
          etypeId: map['etypeId'],
        );

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'ptypeId': ptypeId,
      'etypeId': etypeId,
    };
  }
}

///数据库商品sku实体类
class SkuDO {
  /// skuId
  String id;

  /// 账套id
  String profileId;

  /// 商品id
  String ptypeId;

  /// 属性id1
  String propId1;

  /// 属性名称1
  String propName1;

  /// 属性值id1
  String propvalueId1;

  /// 属性值名称1
  String propvalueName1;

  /// 属性id2
  String propId2;

  /// 属性名称2
  String propName2;

  /// 属性值id2
  String propvalueId2;

  /// 属性值名称2
  String propvalueName2;

  /// 属性id3
  String propId3;

  /// 属性名称3
  String propName3;

  /// 属性值id3
  String propvalueId3;

  /// 属性值名称3
  String propvalueName3;

  /// 属性id4
  String propId4;

  /// 属性名称4
  String propName4;

  /// 属性值id4
  String propvalueId4;

  /// 属性值名称4
  String propvalueName4;

  /// 属性id5
  String propId5;

  /// 属性名称5
  String propName5;

  /// 属性值id5
  String propvalueId5;

  /// 属性值名称5
  String propvalueName5;

  /// 属性id6
  String propId6;

  /// 属性名称6
  String propName6;

  /// 属性值id6
  String propvalueId6;

  /// 属性值名称6
  String propvalueName6;

  /// sku图片地址
  String picUrl;

  /// 属性名称组合
  String propNames;

  /// 属性值组合
  String propvalueNames;

  SkuDO({
    String? id,
    String? profileId,
    String? ptypeId,
    String? propId1,
    String? propName1,
    String? propvalueId1,
    String? propvalueName1,
    String? propId2,
    String? propName2,
    String? propvalueId2,
    String? propvalueName2,
    String? propId3,
    String? propName3,
    String? propvalueId3,
    String? propvalueName3,
    String? propId4,
    String? propName4,
    String? propvalueId4,
    String? propvalueName4,
    String? propId5,
    String? propName5,
    String? propvalueId5,
    String? propvalueName5,
    String? propId6,
    String? propName6,
    String? propvalueId6,
    String? propvalueName6,
    String? picUrl,
    String? propNames,
    String? propvalueNames,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        ptypeId = ptypeId ?? "0",
        propId1 = propId1 ?? "0",
        propName1 = propName1 ?? "",
        propvalueId1 = propvalueId1 ?? "0",
        propvalueName1 = propvalueName1 ?? "",
        propId2 = propId2 ?? "0",
        propName2 = propName2 ?? "",
        propvalueId2 = propvalueId2 ?? "0",
        propvalueName2 = propvalueName2 ?? "",
        propId3 = propId3 ?? "0",
        propName3 = propName3 ?? "",
        propvalueId3 = propvalueId3 ?? "0",
        propvalueName3 = propvalueName3 ?? "",
        propId4 = propId4 ?? "0",
        propName4 = propName4 ?? "",
        propvalueId4 = propvalueId4 ?? "0",
        propvalueName4 = propvalueName4 ?? "",
        propId5 = propId5 ?? "0",
        propName5 = propName5 ?? "",
        propvalueId5 = propvalueId5 ?? "0",
        propvalueName5 = propvalueName5 ?? "",
        propId6 = propId6 ?? "0",
        propName6 = propName6 ?? "",
        propvalueId6 = propvalueId6 ?? "0",
        propvalueName6 = propvalueName6 ?? "",
        picUrl = picUrl ?? "",
        propNames = propNames ?? "",
        propvalueNames = propvalueNames ?? "";

  SkuDO.fromMap(Map<String, dynamic> map)
      : this(
          id: map['id'],
          profileId: map['profileId'],
          ptypeId: map['ptypeId'],
          propId1: map['propId1'],
          propName1: map['propName1'],
          propvalueId1: map['propvalueId1'],
          propvalueName1: map['propvalueName1'],
          propId2: map['propId2'],
          propName2: map['propName2'],
          propvalueId2: map['propvalueId2'],
          propvalueName2: map['propvalueName2'],
          propId3: map['propId3'],
          propName3: map['propName3'],
          propvalueId3: map['propvalueId3'],
          propvalueName3: map['propvalueName3'],
          propId4: map['propId4'],
          propName4: map['propName4'],
          propvalueId4: map['propvalueId4'],
          propvalueName4: map['propvalueName4'],
          propId5: map['propId5'],
          propName5: map['propName5'],
          propvalueId5: map['propvalueId5'],
          propvalueName5: map['propvalueName5'],
          propId6: map['propId6'],
          propName6: map['propName6'],
          propvalueId6: map['propvalueId6'],
          propvalueName6: map['propvalueName6'],
          picUrl: map['picUrl'],
          propNames: map['propNames'],
          propvalueNames: map['propvalueNames'],
        );

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'ptypeId': ptypeId,
      'propId1': propId1,
      'propName1': propName1,
      'propvalueId1': propvalueId1,
      'propvalueName1': propvalueName1,
      'propId2': propId2,
      'propName2': propName2,
      'propvalueId2': propvalueId2,
      'propvalueName2': propvalueName2,
      'propId3': propId3,
      'propName3': propName3,
      'propvalueId3': propvalueId3,
      'propvalueName3': propvalueName3,
      'propId4': propId4,
      'propName4': propName4,
      'propvalueId4': propvalueId4,
      'propvalueName4': propvalueName4,
      'propId5': propId5,
      'propName5': propName5,
      'propvalueId5': propvalueId5,
      'propvalueName5': propvalueName5,
      'propId6': propId6,
      'propName6': propName6,
      'propvalueId6': propvalueId6,
      'propvalueName6': propvalueName6,
      'picUrl': picUrl,
      'propNames': propNames,
      'propvalueNames': propvalueNames,
    };
  }
}

///数据库商品单位实体类
class UnitDO {
  /// skuId
  String id;

  /// 账套id
  String profileId;

  /// 商品id
  String ptypeId;

  /// 单位序号(不引用)基本单位则为1 其他依次递增
  int unitCode;

  /// 单位名称
  String unitName;

  /// 换算关系
  num unitRate;

  /// 单位重量
  num ptypeWeight;

  UnitDO({
    String? id,
    String? profileId,
    String? ptypeId,
    int? unitCode,
    String? unitName,
    num? unitRate,
    num? ptypeWeight,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        ptypeId = ptypeId ?? "0",
        unitCode = unitCode ?? 1,
        unitName = unitName ?? "",
        unitRate = unitRate ?? 0,
        ptypeWeight = ptypeWeight ?? 0;

  UnitDO.fromMap(Map<String, dynamic> map)
      : this(
            id: map['id'],
            profileId: map['profileId'],
            ptypeId: map['ptypeId'],
            unitCode: map['unitCode'],
            unitName: map['unitName'],
            unitRate: map['unitRate'],
            ptypeWeight: map['ptypeWeight']);

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'ptypeId': ptypeId,
      'unitCode': unitCode,
      'unitName': unitName,
      'unitRate': unitRate,
      'ptypeWeight': ptypeWeight
    };
  }
}

///数据库套餐明细行实体类
class ComboDetailDO {
  /// skuId
  String id;

  /// 账套id
  String profileId;

  /// 套餐id
  String comboId;

  /// skuId
  String skuId;

  /// 商品id
  String ptypeId;

  /// 单位id
  String unitId;

  /// 数量
  num qty;

  /// 价格
  num price;

  /// 金额
  num total;

  /// 赠品
  bool gifted;

  /// 分摊比例(%)
  num scale;

  ComboDetailDO({
    String? id,
    String? profileId,
    String? comboId,
    String? skuId,
    String? ptypeId,
    String? unitId,
    num? qty,
    num? price,
    num? total,
    bool? gifted,
    num? scale,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        comboId = comboId ?? "0",
        skuId = skuId ?? "0",
        ptypeId = ptypeId ?? "0",
        unitId = unitId ?? "0",
        qty = qty ?? 0,
        price = price ?? 0,
        total = total ?? 0,
        gifted = gifted ?? false,
        scale = scale ?? 0;

  ComboDetailDO.fromMap(Map<String, dynamic> map)
      : this(
          id: map['id'],
          profileId: map['profileId'],
          comboId: map['comboId'],
          skuId: map['skuId'],
          ptypeId: map['ptypeId'],
          unitId: map['unitId'],
          qty: map['qty'],
          price: map['price'],
          total: map['total'],
          gifted: map['gifted'],
          scale: map['scale'],
        );

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'comboId': comboId,
      'skuId': skuId,
      'ptypeId': ptypeId,
      'unitId': unitId,
      'qty': qty,
      'price': price,
      'total': total,
      'gifted': gifted,
      'scale': scale,
    };
  }
}

///数据库商品条码实体类
class FullBarcodeDO {
  /// skuId
  String id;

  /// 账套id
  String profileId;

  /// 商品id
  String ptypeId;

  /// skuId
  String skuId;

  /// 单位id
  String unitId;

  /// 完整条码
  String fullbarcode;

  int defaulted;

  FullBarcodeDO({
    String? id,
    String? profileId,
    String? ptypeId,
    String? skuId,
    String? unitId,
    String? fullbarcode,
    int? defaulted,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        ptypeId = ptypeId ?? "0",
        skuId = skuId ?? "0",
        unitId = unitId ?? "0",
        fullbarcode = fullbarcode ?? "",
        defaulted = defaulted ?? 0;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'ptypeId': ptypeId,
      'skuId': skuId,
      'unitId': unitId,
      'fullbarcode': fullbarcode,
      'defaulted': defaulted,
    };
  }

  FullBarcodeDO.fromMap(Map<String, dynamic> map)
      : this(
          id: map['id'],
          profileId: map['profileId'],
          ptypeId: map['ptypeId'],
          skuId: map['skuId'],
          unitId: map['unitId'],
          fullbarcode: map['fullbarcode'],
          defaulted: map['defaulted'],
        );
}

///数据库商品条码实体类
class XcodeDO {
  /// skuId
  String id;

  /// 账套id
  String profileId;

  /// 商品id
  String ptypeId;

  /// skuId
  String skuId;

  /// 单位id
  String unitId;

  /// 商家编码
  String xcode;

  int defaulted;

  ///SKU编号所属类型：0商品SKU编号　１套餐SKU编号
  int infoType;

  XcodeDO({
    String? id,
    String? profileId,
    String? ptypeId,
    String? skuId,
    String? unitId,
    String? xcode,
    int? defaulted,
    int? infoType,
  })  : id = id ?? "0",
        profileId = profileId ?? "0",
        ptypeId = ptypeId ?? "0",
        skuId = skuId ?? "0",
        unitId = unitId ?? "0",
        xcode = xcode ?? "",
        defaulted = defaulted ?? 0,
        infoType = infoType ?? 0;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'ptypeId': ptypeId,
      'skuId': skuId,
      'unitId': unitId,
      'xcode': xcode,
      'defaulted': defaulted,
      'infoType': infoType
    };
  }

  XcodeDO.fromMap(Map<String, dynamic> map)
      : this(
            id: map['id'],
            profileId: map['profileId'],
            ptypeId: map['ptypeId'],
            skuId: map['skuId'],
            unitId: map['unitId'],
            xcode: map['xcode'],
            defaulted: map['defaulted'],
            infoType: map['infoType']);
}

///商品全部信息
class PtypeAllInfo {
  List<PtypeDO>? ptypeList;
  List<SkuDO>? skuList;
  List<UnitDO>? unitList;
  List<PriceDO>? priceList;
  List<PriceDO>? otypePriceList;
  List<StockDO>? stockList;
  List<PtypeLimitDO>? ptypeLimitList;
  List<ComboDetailDO>? comboDetailList;
  List<FullBarcodeDO>? fullBarcodeList;
  List<XcodeDO>? ptypeXcodeList;

  PtypeAllInfo.fromMap(Map<String, dynamic> map) {
    ptypeList = (map['ptypeList'] as List<dynamic>?)
        ?.map((e) => PtypeDO.fromMap(e))
        .toList();
    skuList = (map['skuList'] as List<dynamic>?)
        ?.map((e) => SkuDO.fromMap(e))
        .toList();
    unitList = (map['unitList'] as List<dynamic>?)
        ?.map((e) => UnitDO.fromMap(e))
        .toList();
    priceList = (map['priceList'] as List<dynamic>?)
        ?.map((e) => PriceDO.fromMap(e))
        .toList();
    otypePriceList = (map['otypePriceList'] as List<dynamic>?)
        ?.map((e) => PriceDO.fromMap(e))
        .toList();
    stockList = (map['stockList'] as List<dynamic>?)
        ?.map((e) => StockDO.fromMap(e))
        .toList();
    ptypeLimitList = (map['ptypeLimitList'] as List<dynamic>?)
        ?.map((e) => PtypeLimitDO.fromMap(e))
        .toList();
    comboDetailList = (map['comboDetailList'] as List<dynamic>?)
        ?.map((e) => ComboDetailDO.fromMap(e))
        .toList();
    fullBarcodeList = (map['fullBarcodeList'] as List<dynamic>?)
        ?.map((e) => FullBarcodeDO.fromMap(e))
        .toList();
    ptypeXcodeList = (map['ptypeXcodeList'] as List<dynamic>?)
        ?.map((e) => XcodeDO.fromMap(e))
        .toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'ptypeList': ptypeList?.map((e) => e.toJson()).toList(),
      'skuList': skuList?.map((e) => e.toJson()).toList(),
      'unitList': unitList?.map((e) => e.toJson()).toList(),
      'priceList': priceList?.map((e) => e.toJson()).toList(),
      'otypePriceList': otypePriceList?.map((e) => e.toJson()).toList(),
      'stockList': stockList?.map((e) => e.toJson()).toList(),
      'ptypeLimitList': ptypeLimitList?.map((e) => e.toJson()).toList(),
      'comboDetailList': comboDetailList?.map((e) => e.toJson()).toList(),
      'fullBarcodeList': fullBarcodeList?.map((e) => e.toJson()).toList(),
      'ptypeXcodeList': ptypeXcodeList?.map((e) => e.toJson()).toList(),
    };
  }
}
