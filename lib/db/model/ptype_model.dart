import 'package:flutter/cupertino.dart';
import 'package:halo_utils/http/base_model.dart';

import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../entity/page_info.dart';
import '../entity/ptype.dart';

const int _pageSize = 2000;

///商品信息同步的model
class PtypeModel {
  ///获取商品信息
  static Future<PtypeAllInfo?> getPtypeAllInfoByIds(
      BuildContext context, List<String> ptypeIds) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        autoHandleError: false,
        method: RequestMethod.getPosPtypeAllInfoByIds,
        isLoading: true,
        data: ptypeIds);
    if(responseDto.data == null) {
      return null;
    }
    return PtypeAllInfo.fromMap(responseDto.data);
  }

  ///获取商品信息
  static Future<PageInfo<PtypeDO>> getPtype(BuildContext context,
      {required int pageIndex, int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        autoHandleError: false,
        method: RequestMethod.getPosPtypeList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        });
    return PageInfo.fromMap(responseDto.data,
        mapper: (e) => PtypeDO.fromMap(e));
  }

  ///获取价格信息
  ///type 0=商品价格 1=门店价格本
  static Future<PageInfo<PriceDO>> getPtypePrice(BuildContext context,
      {required int type,
      required int pageIndex,
      int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        method: RequestMethod.getPosPtypePriceList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
          "queryParams": {"type": type},
        });
    return PageInfo.fromMap(responseDto.data,
        mapper: (e) => PriceDO.fromMap(e));
  }

  ///获取权限信息
  static Future<PageInfo<PtypeLimitDO>> getPtypeLimit(BuildContext context,
      {required int pageIndex, int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        method: RequestMethod.getPosPtypeLimitList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        });
    return PageInfo.fromMap(responseDto.data,
        mapper: (e) => PtypeLimitDO.fromMap(e));
  }

  ///获取库存信息
  static Future<PageInfo<StockDO>> getPtypeStock(BuildContext context,
      {required int pageIndex, int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        method: RequestMethod.getPosPtypeStockList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        });
    return PageInfo.fromMap(responseDto.data,
        mapper: (e) => StockDO.fromMap(e));
  }

  ///获取商品sku信息
  static Future<PageInfo<SkuDO>> getPtypeSku(BuildContext context,
      {required int pageIndex, int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        method: RequestMethod.getPosPtypeSkuList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        });
    return PageInfo.fromMap(responseDto.data, mapper: (e) => SkuDO.fromMap(e));
  }

  ///获取商品unit信息
  static Future<PageInfo<UnitDO>> getPtypeUnit(BuildContext context,
      {required int pageIndex, int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        method: RequestMethod.getPosPtypeUnitList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        });
    return PageInfo.fromMap(responseDto.data, mapper: (e) => UnitDO.fromMap(e));
  }

  ///获取商品套餐明细信息
  static Future<PageInfo<ComboDetailDO>> getPtypeComboDetail(
      BuildContext context,
      {required int pageIndex,
      int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        method: RequestMethod.getPosPtypeComboDetailList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        });
    return PageInfo.fromMap(responseDto.data,
        mapper: (e) => ComboDetailDO.fromMap(e));
  }

  ///获取商品条码信息
  static Future<PageInfo<FullBarcodeDO>> getFullBarcode(BuildContext context,
      {required int pageIndex, int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        method: RequestMethod.getPosPtypeFullBarcodeList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        });
    return PageInfo.fromMap(responseDto.data,
        mapper: (e) => FullBarcodeDO.fromMap(e));
  }

  ///获取商品编码
  static Future<PageInfo<XcodeDO>> getPtypeXcode(BuildContext context,
      {required int pageIndex, int pageSize = _pageSize}) async {
    ResponseModel responseDto = await HttpUtil.request(context,
        method: RequestMethod.getPtypeXcodeList,
        isLoading: false,
        data: {
          "pageIndex": pageIndex,
          "pageSize": pageSize,
        });
    return PageInfo.fromMap(responseDto.data,
        mapper: (e) => XcodeDO.fromMap(e));
  }
}
