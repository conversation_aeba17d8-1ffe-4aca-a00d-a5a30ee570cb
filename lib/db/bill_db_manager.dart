import 'dart:convert';

import 'package:sqflite/sqflite.dart';

import '../bill/entity/goods_bill.dto.dart';
import 'database_helper.dart';

/// 创建时间：2023/12/26
/// 作者：xiaotiaochong
/// 描述：单据离线数据

class BillDBManager {
  //登录用户表
  static const String saleBillTable = "offline_sale_bill";

  //region table
  /*
   创建离线 登录用户 表
    */
  static Future<void> createSaleBillTable(Database db) async {
    await db.execute(
        'CREATE TABLE IF NOT EXISTS $saleBillTable ( id INTEGER PRIMARY KEY AUTOINCREMENT, '
        'profile_id VARCHAR(100), '
        'vchcode VARCHAR(100), '
        'json VARCHAR  )');
  }

  static Future<int> insertSaleBill(
      {required String profileId,
      required String vchcode,
      required String json}) async {
    final db = await DatabaseHelper.instance.database;
    return await db.insert(saleBillTable,
        {'profile_id': profileId, 'vchcode': vchcode, 'json': json},
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<List<GoodsBillDto>> selectSaleBill({
    required String profileId,
  }) async {
    final db = await DatabaseHelper.instance.database;
    List<Map> list = await db.rawQuery(
        'SELECT * FROM $saleBillTable'
        ' WHERE profile_id = ?',
        [profileId]);
    return list
        .map((e) => GoodsBillDto.fromMap(jsonDecode(e['json'])))
        .toList();
  }

  static Future<int> selectSaleBillCount({
    required String profileId,
  }) async {
    final db = await DatabaseHelper.instance.database;
    int? count = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT count(0) as count FROM $saleBillTable'
        ' WHERE profile_id = ? group by profile_id',
        [profileId]));
    return count ?? 0;
  }

  static Future<int> deleteSaleBillList({
    required String profileId,
    required List<String> vchcodeList,
  }) async {
    final db = await DatabaseHelper.instance.database;
    String vchcodeStringList = "";
    for (var i = 0; i < vchcodeList.length; i++) {
      String vchcode = vchcodeList[i];
      if (i == 0) {
        vchcodeStringList += "($vchcode";
      } else {
        vchcodeStringList += ", $vchcode";
      }
      if (i == vchcodeList.length - 1) {
        vchcodeStringList += ")";
      }
    }
    String sqlString =
        'DELETE FROM $saleBillTable WHERE profile_id = $profileId AND vchcode in $vchcodeStringList';
    return await db.rawDelete(sqlString);
  }

  static Future<int> deleteSaleBillFromProfileId(
      {required String profileId}) async {
    final db = await DatabaseHelper.instance.database;
    return await db.rawDelete(
        'DELETE FROM $saleBillTable WHERE profile_id = ?', [profileId]);
  }
}
