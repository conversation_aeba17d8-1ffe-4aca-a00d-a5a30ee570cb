import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import '../../../common/base_type_util.dart';
import '../common/login/login_center.dart';
import 'bill_db_manager.dart';
import 'login_user_db_mannager.dart';

///@ClassName: db_manager
///@Description: 本地数据库工具类
///@Author: tanglan
///@Date: 2023/12/18

class DatabaseHelper {
  ///版本号
  static const int _version = 1;
  static Database? _database;
  static final DatabaseHelper instance = DatabaseHelper._();

  ///数据库名称
  String get _databaseName =>
      " ZYXYunLinShou_${LoginCenter.getLoginUser().profileId}.db";

  DatabaseHelper._();

  Future<Database> get database async {
    if (_database?.isOpen == true) {
      return _database!;
    }
    return _database = await initDatabase();
  }

  Future<Database> initDatabase() async {
    Directory appDir;
    if (Platform.isWindows) {
      appDir = await getApplicationSupportDirectory();
    } else {
      appDir = await getApplicationDocumentsDirectory();
    }
    String path = join(appDir.path, _databaseName);

    if (Platform.isWindows) {
      sqfliteFfiInit();
      final databaseFactory = databaseFactoryFfi;
      return await databaseFactory.openDatabase(
        path,
        options: OpenDatabaseOptions(
            version: _version, //
            onCreate: _onCreate,
            onUpgrade: _onUpgrade, onOpen: _onOpen),
      );
    }
    // await deleteDatabase(path);
    return await openDatabase(
      path,
      version: _version, // Specify the latest version number
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onOpen: _onOpen,
    );
  }

  ///关闭数据库
  ///当退出登录时调用
  static void close() async {
    if (_database?.isOpen == true) {
      _database!.close();
    }
    _database = null;
  }

  //初始化数据库
  Future<void> _onCreate(Database database, int version) async {
    // todo 这里创建数据库后无需马上创建表格，而是在同步数据的时候创建表格
    debugPrint("_onCreate database");
    // final db = database;
    // await PTypeDbManager.createPtypeTable(db, version);
  }

  //升级数据库
  Future<void> _onUpgrade(Database database, oldVersion, newVersion) async {
    final db = database;
    debugPrint("_onUpgrade database");
    for (int i = oldVersion + 1; i <= newVersion; i++) {
      switch (i) {
        case 2:
          LoginUserDBManager.createLoginUserTable(db);
          // Upgrade from version 1 to 2
          // await db.execute(
          //     'ALTER TABLE your_table_name ADD COLUMN new_column TEXT');
          break;
      }
    }
  }

  Future<void> _onOpen(Database db) async {
    ///用户
    await LoginUserDBManager.createLoginUserTable(db);

    ///权限
    await LoginUserDBManager.createPermissionConfigTable(db);
    ///单据
    await BillDBManager.createSaleBillTable(db);
    debugPrint("open database");
  }

  Future<void> dropTable(String tableName) async {
    final db = await database;
    await db.execute("drop table $tableName");
  }

  //批量插入数据
  Future<void> batchInsert(
      String tableName, List<Map<String, dynamic>> list) async {
    if (list.isEmpty) {
      return;
    }
    final db = await database;
    var batchInsert = db.batch();
    for (var item in list) {
      batchInsert.insert(tableName, item);
    }
    await batchInsert.commit();
  }

  //批量插入数据
  Future<void> batchUpdate(
      String tableName, List<Map<String, dynamic>> list) async {
    if (list.isEmpty) {
      return;
    }
    final db = await database;
    var batchUpdate = db.batch();
    for (var item in list) {
      batchUpdate
          .update(tableName, item, where: "id = ?", whereArgs: [item["id"]]);
    }
    await batchUpdate.commit();
  }

  ///将驼峰命名转化为下划线命名，用于将实体类存入数据库
  static Map<String, dynamic> changeCamelCaseMapToUnderscoreCaseMap(
      Map<String, dynamic> source) {
    return source.map((key, value) =>
        MapEntry(BaseTypeUtil.changeCamelCaseToUnderscoreCase(key), value));
  }

  ///将下划线命名转化为驼峰命名，用于从数据库数据转化为实体类
  static Map<String, dynamic> changeUnderscoreCaseMapToCamelCaseMap(
      Map<String, dynamic> source) {
    return source.map((key, value) =>
        MapEntry(BaseTypeUtil.changeUnderscoreCaseToCamelCase(key), value));
  }

  ///给sql搜索增加分页
  static StringBuffer addSearchLimit(int pageIndex, int pageSize,
      {String? sourceSql, StringBuffer? sourceSqlBuffer}) {
    sourceSqlBuffer ??= StringBuffer(sourceSql ?? "");
    int offset = 0;
    if (pageIndex > 1) {
      offset = (pageIndex - 1) * pageSize;
    }
    sourceSqlBuffer.write(" LIMIT $pageSize OFFSET $offset");
    return sourceSqlBuffer;
  }

  ///移除多余的","
  static void removeEnd(StringBuffer sql, String removeWord) {
    if (removeWord.isEmpty) return;
    //去除多余的,
    String checkSql = sql.toString().trimRight();
    while (checkSql.endsWith(removeWord)) {
      checkSql = checkSql.substring(0, checkSql.length - removeWord.length);
      sql.clear();
      sql.write("$checkSql ");
    }
  }
}
