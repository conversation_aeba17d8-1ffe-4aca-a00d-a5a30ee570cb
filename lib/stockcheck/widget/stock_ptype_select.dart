import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_search.dart';

import '../../common/change_notifier.dart';
import '../../common/style/app_color_helper.dart';
import '../../common/style/app_colors.dart';
import '../../iconfont/icon_font.dart';
import '../entity/stock_check_info_entity.dart';
import 'stock_ptype_list.dart';

class StockPtypeSelect extends StatefulWidget {
  final Function(List<StockCheckInfoEntity> item) onChanged;

  const StockPtypeSelect({Key? key,required this.onChanged}) : super(key: key);

  @override
  State createState() => _StockPtypeSelectState();
}

class _StockPtypeSelectState extends State<StockPtypeSelect> {
  InterceptValueNotifier filterValue = InterceptValueNotifier("");

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color.fromRGBO(0, 0, 0, 0),
      body: Container(
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 80.w, horizontal: 300.w),
              decoration: const BoxDecoration(
                color: Colors.white,
                //设置四周圆角 角度
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: 830.h,
                  // maxWidth: ScreenUtil().screenWidth - 900.w,
                ),
                child: Column(
                  children: [
                    HaloContainer(
                      padding: EdgeInsets.only(left: 19.w, right: 15),
                      color: Colors.white24,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      height: 72.h,
                      children: [
                        Text(
                          "商品选择",
                          style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 28.sp,
                              color: AppColorHelper(context)
                                  .getTitleBoldTextColor()),
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            color: Colors.transparent,
                            alignment: Alignment.centerRight,
                            width: 100,
                            height: 30,
                            child: IconFont(
                              IconNames.close,
                              size: 30,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Divider(
                      color: AppColors.lineColor,
                      height: 1,
                    ),
                    _buildSearch(),
                    // const Divider(
                    //   color: AppColors.lineColor,
                    //   height: 1,
                    // ),
                    _buildTop(),
                    // const Divider(
                    //   color: AppColors.lineColor,
                    //   height: 1,
                    // ),
                    Expanded(
                      child: StockPtypeList(onChanged: (i,item){
                        widget.onChanged(item);
                      },filterValue: filterValue,),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTop() {
    return HaloContainer(
      padding: EdgeInsets.only(left: 40.w, right: 19.w),
      // mainAxisSize: MainAxisSize.max,
      color: ColorUtil.stringColor("#F5F5F5"),
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      height: 60.h,
      children: [
        Expanded(
            flex: 3,
            child: Text(
              "商品名称/编号",
              style: TextStyle(
                  color: AppColorHelper(context).getTitleTextColor(),
                  fontSize: 22.sp),
            )),
        SizedBox(
          width: 250.w,
          child: Text(
            "SKU条码",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 350.w,
          child: Text(
            "属性组合",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 100.w,
          child: Text(
            "单位",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 150.w,
          child: Text(
            "实物库存",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
      ],
    );
  }


  Widget _buildSearch() {
    return HaloSearch(
      value: filterValue.value.toString(),
      inputBackGround: const Color(0x00000000),
      isShowClear: true,
      isShowScan: false,
      isShowFilter: false,
      inputHintTextColor: ColorUtil.stringColor("#979CA2"),
      hintText: "输入商品名称/商品编号/SKU条码查询",
      onSubmitted: (text) async {
        filterValue.value = text;
      },
    );
  }
}
