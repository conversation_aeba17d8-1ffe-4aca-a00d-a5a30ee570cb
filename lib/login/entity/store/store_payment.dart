///
///@ClassName: store_payment
///@Description:
///@Author: tanglan
///@Date: 2022/12/16
class StorePayway {
  String? id;
  String? profileId;
  String? otypeId;
  String? paywayId;
  String? paywayName; //支付方式名称"
  int? paywayType; //"支付方式类型：0=现金，1=银行，2=淘淘谷，3=预存款"
  String? atypeId; //收款账户id
  String? atypeFullname; //收款账户名称
  int? payType; //聚合支付支付类型：1 淘淘谷
  int? product; //支付产品，如淘淘谷扫码支付
  String? content; //聚合支付支付配置内容

  static StorePayway fromMap(Map<String, dynamic> map) {
    StorePayway storePayment = StorePayway();
    storePayment.id = map['id'];
    storePayment.profileId = map['profileId'];
    storePayment.otypeId = map['otypeId'];
    storePayment.paywayId = map['paywayId'];
    storePayment.paywayName = map['paywayName'];
    storePayment.paywayType = map['paywayType'];
    storePayment.atypeId = map['atypeId'];
    storePayment.atypeFullname = map['atypeFullname'];
    storePayment.payType = map['payType'];
    storePayment.product = map['product'];
    storePayment.content = map['content'];
    return storePayment;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "otypeId": otypeId,
        "paywayId": paywayId,
        "paywayName": paywayName,
        "paywayType": paywayType,
        "atypeId": atypeId,
        "atypeFullname": atypeFullname,
        "payType": payType,
        "product": product,
        "content": content
      };
}
