import '../../../entity/print/print_config_info.dart';
import '../../../login/entity/store/store_atype.dart';
import '../../../login/entity/store/store_cashier.dart';
import '../../../login/entity/store/store_etype.dart';
import '../../../login/entity/store/store_payment.dart';
import 'barcode_scale_config.dart';

/// address : "string"
/// btypeId : 0
/// btypeName : "string"
/// businessType : 0
/// cashierAtypeId : 0
/// cashierAtypeName : "string"
/// cashiers : [{"createTime":"2021-11-19T03:30:57.049Z","deleted":true,"fullname":"string","id":0,"modifyTime":"2021-11-19T03:30:57.049Z","otypeId":0,"profileId":0,"stoped":true,"usercode":"string"}]
/// city : "string"
/// closeTime : "string"
/// createTime : "2021-11-19T03:30:57.049Z"
/// deleted : true
/// district : "string"
/// fullAddress : "string"
/// fullname : "string"
/// ktypeId : 0
/// ktypeName : "string"
/// latitude : 0
/// longitude : 0
/// memo : "string"
/// mobile : "string"
/// mode : 0
/// modifyTime : "2021-11-19T03:30:57.049Z"
/// multiAtypeIds : "string"
/// multiAtypeNames : "string"
/// multiAtypes : [{"atypeId":0,"atypeName":"string","id":0,"otypeId":0,"profileId":0}]
/// ocategory : "全部"
/// openTime : "string"
/// otypeFullname : "string"
/// otypeId : 0
/// payAtypeId : 0
/// payAtypeName : "string"
/// profileId : 0
/// shopOwner : "string"
/// stoped : true
/// usercode : "string"

class StoreInfo {
  bool? openStoreValuePassword;
  bool? crossCashBill;
  int? passwordType;

  String? address;
  String? btypeId;
  String? btypeName;
  int? businessType;

  // String cashierAtypeId;
  // String cashierAtypeName;
  List<StoreCashier>? cashiers;
  String? province;
  String? city;
  String? closeTime;
  String? createTime;
  bool? deleted;
  String? district;
  String? fullname;
  String? ktypeId = "";
  String? ktypeName = "";
  num? latitude;
  num? longitude;
  String? memo;
  String? mobile;
  String? cellphone;
  int? mode;
  String? modifyTime;
  String? multiAtypeIds;
  String? multiAtypeNames;
  List<StoreAtype>? multiAtypes;
  List<StoreEtype>? etypeList;
  List<StorePayway>? paywayList;
  dynamic ocategory;
  String? openTime;
  String? otypeFullname;
  String? otypeId;
  String? ownerId;

  // String payAtypeId;
  // String payAtypeName;
  String? profileId;
  String? shopOwner;
  bool? stoped;

  ///是否授权云零售登录
  bool? allowPosLogin;
  String? usercode;
  String? noBarPtypeId;
  String? payContent;
  String? storedMoneyAtypeId; //储值账户ID
  String? storedMoneyAtypeName; //储值账户名称
  BarcodeScaleConfig? barcodeScaleConfig; //条码秤配置
  String? shopLogoUrl; //"门店logoUrl
  String? shopScanUrl; //门店二维码
  String? shopScanMemo; //门店二维码说明文字
  String? shopNotice; //门店公告
  num? minDiscount;

  ///手工最小折扣

  static StoreInfo fromMap(Map<String, dynamic> map) {
    StoreInfo storeInfoBean = StoreInfo();
    storeInfoBean.openStoreValuePassword =
        map["openStoreValuePassword"] ?? false;
    storeInfoBean.crossCashBill = map["crossCashBill"];
    storeInfoBean.passwordType = map["passwordType"] ?? 0;
    storeInfoBean.address = map['address'];
    storeInfoBean.btypeId = map['btypeId'];
    storeInfoBean.minDiscount = map['minDiscount'];
    storeInfoBean.ownerId = map['ownerId'];
    storeInfoBean.btypeName = map['btypeName'];
    storeInfoBean.businessType = map['businessType'];
    // storeInfoBean.cashierAtypeId = map['cashierAtypeId'];
    // storeInfoBean.cashierAtypeName = map['cashierAtypeName'];
    storeInfoBean.storedMoneyAtypeId = map['storedMoneyAtypeId'];
    storeInfoBean.storedMoneyAtypeName = map['storedMoneyAtypeName'];
    storeInfoBean.payContent = map['payContent'];
    storeInfoBean.cashiers = (map['cashiers'] as List?)
            ?.map((o) => StoreCashier.fromMap(o))
            .toList() ??
        [];
    storeInfoBean.province = map['province'];
    storeInfoBean.city = map['city'];
    storeInfoBean.closeTime = map['closeTime'];
    storeInfoBean.createTime = map['createTime'];
    storeInfoBean.deleted = map['deleted'];
    storeInfoBean.district = map['district'];
    storeInfoBean.fullname = map['fullname'];
    storeInfoBean.ktypeId = map['ktypeId'];
    storeInfoBean.ktypeName = map['ktypeName'];
    storeInfoBean.latitude = map['latitude'];
    storeInfoBean.longitude = map['longitude'];
    storeInfoBean.memo = map['memo'];
    storeInfoBean.mobile = map['mobile'];
    storeInfoBean.mode = map['mode'];
    storeInfoBean.modifyTime = map['modifyTime'];
    storeInfoBean.paywayList = (map['paywayList'] as List?)
        ?.map((o) => StorePayway.fromMap(o))
        .toList();

    storeInfoBean.ocategory =
        map['ocategory'] == null ? "" : map['ocategory'].toString();
    storeInfoBean.openTime = map['openTime'];
    storeInfoBean.otypeFullname = map['otypeFullname'];
    storeInfoBean.otypeId = map['otypeId'];
    // storeInfoBean.payAtypeId = map['payAtypeId'];
    // storeInfoBean.payAtypeName = map['payAtypeName'];
    storeInfoBean.profileId = map['profileId'];
    storeInfoBean.shopOwner = map['shopOwner'];
    storeInfoBean.stoped = map['stoped'];
    storeInfoBean.allowPosLogin = map['allowPosLogin'];
    storeInfoBean.shopLogoUrl = map['shopLogoUrl'];
    storeInfoBean.shopScanUrl = map['shopScanUrl'];
    storeInfoBean.shopScanMemo = map['shopScanMemo'];
    storeInfoBean.shopNotice = map['shopNotice'];

    storeInfoBean.noBarPtypeId = map["noBarPtypeId"];
    storeInfoBean.etypeList = (map['etypeList'] as List?)
            ?.map((o) => StoreEtype.fromMap(o))
            .toList() ??
        [];
    storeInfoBean.barcodeScaleConfig =
        BarcodeScaleConfig.fromMap(map["barcodeScaleConfig"]);
    storeInfoBean.cellphone = map["cellphone"];
    return storeInfoBean;
  }

  Map toJson() => {
        "crossCashBill": crossCashBill,
        "openStoreValuePassword": openStoreValuePassword,
        "payContent": payContent,
        "address": address,
        "btypeId": btypeId,
        "ownerId": ownerId,
        "btypeName": btypeName,
        "businessType": businessType,
        // "cashierAtypeId": cashierAtypeId,
        // "cashierAtypeName": cashierAtypeName,
        "cashiers": cashiers,
        "city": city,
        "province": province,
        "closeTime": closeTime,
        "createTime": createTime,
        "deleted": deleted,
        "district": district,
        "minDiscount": minDiscount,
        "fullname": fullname,
        "ktypeId": ktypeId,
        "ktypeName": ktypeName,
        "latitude": latitude,
        "longitude": longitude,
        "passwordType": passwordType,
        "memo": memo,
        "mobile": mobile,
        "mode": mode,
        "modifyTime": modifyTime,
        "multiAtypeIds": multiAtypeIds,
        "multiAtypeNames": multiAtypeNames,
        "multiAtypes": multiAtypes,
        "ocategory": ocategory,
        "openTime": openTime,
        "otypeFullname": otypeFullname,
        "otypeId": otypeId,
        // "payAtypeId": payAtypeId,
        // "payAtypeName": payAtypeName,
        "profileId": profileId,
        "shopOwner": shopOwner,
        "stoped": stoped,
        "allowPosLogin": allowPosLogin,
        "usercode": usercode,
        "noBarPtypeId": noBarPtypeId,
        "storedMoneyAtypeId": storedMoneyAtypeId,
        "storedMoneyAtypeName": storedMoneyAtypeName,
        "etypeList": etypeList,
        "paywayList": paywayList,
        "barcodeScaleConfig": barcodeScaleConfig,
        "shopLogoUrl": shopLogoUrl,
        "shopScanUrl": shopScanUrl,
        "shopScanMemo": shopScanMemo,
        "shopNotice": shopNotice,
        "cellphone": cellphone,
      };
}
