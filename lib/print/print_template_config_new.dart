import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/bill/model/base_info_model.dart';
import 'package:halo_pos/bill/tool/bill_tool.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../bill/tool/decimal_display_helper.dart';
import '../common/login/login_center.dart';
import '../common/string_res.dart';
import '../common/style/app_colors.dart';
import '../common/tool/image_tool.dart';
import '../entity/print/print_config_info.dart';
import '../entity/print/print_decorate_config.dart';
import '../entity/print/print_general_config.dart';
import '../entity/system/system_config_dto.dart';
import '../entity/upload_qiniu_dto.dart';
import '../enum/bill_type.dart';
import '../enum/setting/print_width_type.dart';
import '../iconfont/icon_font.dart';
import '../login/entity/store/store_cashier.dart';
import '../login/entity/store/store_info.dart';
import '../login/model/login_user_model.dart';
import '../login/model/store_model.dart';
import '../widgets/base/base_stateful_page.dart';
import '../widgets/base/halo_pos_alert_dialog.dart';
import '../widgets/dotted_line.dart';
import '../widgets/halo_pos_label.dart';
import 'tool/print_config.dart';
import 'tool/print_tool.dart';

///
///@ClassName: print_template_config_new
///@Description:
///@Author: tanglan
///@Date: 2025/4/22
///

class PrintTemplateConfigNewPage extends BaseStatefulPage {
  const PrintTemplateConfigNewPage({Key? key}) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() {
    return _PrintTemplateConfigState();
  }
}

class _PrintTemplateConfigState
    extends BaseStatefulPageState<PrintTemplateConfigNewPage> {
  BillType selectPrintBill = BillType.SaleBill;
  final StoreInfo _storeInfo = SpTool.getStoreInfo() ?? StoreInfo();
  final StoreCashier storeCashier = SpTool.getCashierInfo();
  final SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
  Map<String, List<PrintConfigInfo>> allBillPrintConfigList =
      SpTool.getPrintFieldInfo();

  Map<String, PrintConfigInfo> _printMapConfigList = {};
  List<String>? billPrintConfig;
  String selectField = "";

  GlobalKey<_PrintViewPageState> printViewGlobalView = GlobalKey();
  GlobalKey<_CustomPrintConfigPageState> printCustomConfigGlobalView =
      GlobalKey();

  @override
  String getActionBarTitle() {
    return "收银小票样式设置";
  }

  @override
  Future<void>? onInitState() {
    billPrintConfig =
        PrintFieldConfig.printConfigName(
          isTaxEnable:
              systemConfigDto.sysGlobalEnabledTax &&
              systemConfigDto.sysGlobalEnabledSaleTax,
        )[selectPrintBill];
    _printMapConfigList = PrintTool.getBillPrintMap(
      BillTypeData[selectPrintBill] ?? "",
    );
    return Future.value(null);
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      direction: Axis.horizontal,
      color: AppColors.pageBackgroundColor,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 5,
          child: _BillPrintFieldPage(
            selectPrintBill,
            _printMapConfigList,
            systemConfigDto,
            storeInfo: _storeInfo,
            storeCashier: storeCashier,
            billPrintConfig: billPrintConfig,
            onBillTypeChangeCallBack: (BillType billType) {
              setState(() {
                selectField = "";
                selectPrintBill = billType;
                _printMapConfigList = PrintTool.getBillPrintMap(
                  BillTypeData[selectPrintBill] ?? "",
                  printConfigList:
                      allBillPrintConfigList[BillTypeData[selectPrintBill]],
                );
                billPrintConfig = PrintFieldConfig.getBillPrintConfigName(
                  billType: selectPrintBill,
                );
              });
            },
            onFieldSelectCallBack: (
              BillType billType,
              PrintConfigInfo printConfigInfo,
            ) {
              setState(() {
                selectField = printConfigInfo.fieldKey;
                _printMapConfigList[selectField] = printConfigInfo;
              });
            },
          ),
        ),
        Expanded(
          flex: 6,
          child: _PrintViewPage(
            key: printViewGlobalView,
            printConfigMap: _printMapConfigList,
            billPrintConfig: billPrintConfig,
            billType: selectPrintBill,
            storeInfo: _storeInfo,
            systemConfigDto: systemConfigDto,
            selectField: selectField,
          ),
        ),
        Expanded(
          flex: 4,
          child: _CustomPrintConfigPage(
            _printMapConfigList[selectField],
            key: printCustomConfigGlobalView,
            selectFieldKey: selectField,
            onEditChangeBack: (
              PrintConfigInfo printConfig, {
              bool? syncOtherBill,
            }) {
              _printMapConfigList[selectField] = printConfig;
              printViewGlobalView.currentState?.refreshView(
                _printMapConfigList,
              );
            },
          ),
        ),
        // ,
      ],
    );
  }

  handleSyncOtherTemplate() {
    ///勾选了已同步的配置
    List<PrintConfigInfo> syncOtherTemplateList =
        _printMapConfigList.values
            .where((item) => item.syncOtherTemplate)
            .toList();
    Map<String, PrintConfigInfo> oldSelectPrintConfigList =
        PrintTool.getBillPrintMap(BillTypeData[selectPrintBill] ?? "");
    for (PrintConfigInfo item in syncOtherTemplateList) {
      ///未更改的需同步的配置无需同步
      ///未变更不处理
      if (oldSelectPrintConfigList.containsKey(item.fieldKey) &&
          item.toJson().toString() ==
              oldSelectPrintConfigList[item.fieldKey]?.toJson().toString()) {
        continue;
      }

      ///遍历其他的模板
      for (BillType billType in PrintFieldConfig.printBillList.keys) {
        ///当前模版不处理
        if (billType == selectPrintBill) {
          continue;
        }
        List<PrintConfigInfo> printConfigList =
            allBillPrintConfigList[BillTypeData[billType]] ?? [];
        Map<String, PrintConfigInfo> mapPrintConfig = {};
        for (PrintConfigInfo printItem in printConfigList) {
          mapPrintConfig[printItem.fieldKey] = printItem;
        }
        PrintConfigInfo printConfigInfo = PrintConfigInfo.fromMap(
          item.toJson(),
        );

        if (mapPrintConfig.containsKey(item.fieldKey)) {
          PrintConfigInfo oldPrintConfig = mapPrintConfig[item.fieldKey]!;
          printConfigInfo.selected = oldPrintConfig.selected;
        }
        printConfigInfo.id = BillTool.getVchcode();
        printConfigInfo.printType = BillTypeData[billType] ?? '';
        mapPrintConfig[item.fieldKey] = printConfigInfo;
        allBillPrintConfigList[BillTypeData[billType] ?? ""] =
            mapPrintConfig.values.toList();
      }
    }
  }

  @override
  Widget buildBottomBody(BuildContext context) {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      color: Colors.white,
      border: Border(
        top: BorderSide(color: AppColors.pageBackgroundColor, width: 1.h),
      ),
      margin: EdgeInsets.only(top: 12.h),
      padding: EdgeInsets.symmetric(vertical: 20.h),
      children: [
        HaloButton(
          text: "保存",
          height: 70.h,
          borderRadius: 6.w,
          width: 180.w,
          fontSize: AppPosSize.firstTitleFontSize.sp,
          backgroundColor: AppColors.accentColor,
          onPressed: () async {
            handleSyncOtherTemplate();
            allBillPrintConfigList[BillTypeData[selectPrintBill] ?? ""] =
                _printMapConfigList.values.toList();
            List<PrintConfigInfo> printList =
                allBillPrintConfigList.values
                    .expand((configs) => configs)
                    .toList();

            await StoreModel.savePrintConfig(context, printList);
            SpTool.savePrintFieldInfo(printList);
            if (context.mounted) {
              HaloToast.show(context, msg: "打印信息保存成功");
            }
          },
        ),
        HaloContainer(
          margin: EdgeInsets.only(left: 20.w),
          children: [
            HaloButton(
              text: "取消",
              buttonType: HaloButtonType.outlinedButton,
              borderColor: AppColors.btnBorderColor,
              textColor: AppColors.normalTextColor,
              height: 70.h,
              borderRadius: 6.w,
              width: 180.w,
              fontSize: AppPosSize.firstTitleFontSize.sp,
              outLineWidth: 1.h,
              onPressed: () {
                NavigateUtil.pop(context);
              },
            ),
          ],
        ),
      ],
    );
  }
}

//region  单据打印字段配置展示
class _BillPrintFieldPage extends StatefulWidget {
  final BillType billType;
  final StoreCashier storeCashier;
  final StoreInfo storeInfo;
  final Map<String, PrintConfigInfo> printConfig;
  final SystemConfigDto systemConfigDto;
  final Function(BillType billType, PrintConfigInfo printConfigDto)
  onFieldSelectCallBack;
  final Function(BillType billType) onBillTypeChangeCallBack;
  final List<String>? billPrintConfig;

  const _BillPrintFieldPage(
    this.billType,
    this.printConfig,
    this.systemConfigDto, {
    required this.storeCashier,
    required this.storeInfo,
    required this.onFieldSelectCallBack,
    required this.billPrintConfig,
    required this.onBillTypeChangeCallBack,
  });

  @override
  State<StatefulWidget> createState() {
    return _BillPrintFieldPageState();
  }
}

class _BillPrintFieldPageState extends State<_BillPrintFieldPage> {
  final GlobalKey _printBillGlobalKey = GlobalKey(); //门店选择框key
  late Map<String, PrintConfigInfo> _printConfig;

  @override
  void initState() {
    _printConfig = widget.printConfig;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant _BillPrintFieldPage oldWidget) {
    _printConfig = widget.printConfig;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      mainAxisSize: MainAxisSize.max,
      color: Colors.white,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildBillListSelect(),
        Expanded(
          child: _PrintFieldShowConfigPage(
            storeInfo: widget.storeInfo,
            storeCashier: widget.storeCashier,
            printConfig: _printConfig,
            printType: BillTypeData[widget.billType] ?? "",
            printConfigName: widget.billPrintConfig ?? [],
            onEditPrintConfigCallBack: (PrintConfigInfo changePrintConfig) {
              _printConfig[changePrintConfig.fieldKey] = changePrintConfig;
              widget.onFieldSelectCallBack(widget.billType, changePrintConfig);
            },
          ),
        ),
      ],
    );
  }

  //region 单据选择项配置
  ///构建打印单据选择
  Widget buildBillListSelect() {
    return GestureDetector(
      onTap: () {
        _showBillSelectWindow(context);
      },
      behavior: HitTestBehavior.opaque,
      child: HaloContainer(
        borderRadius: const BorderRadius.all(Radius.circular(6)),
        border: Border.all(color: AppColors.borderColor),
        margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 8.h),
        key: _printBillGlobalKey,
        children: [
          Expanded(
            child: HaloContainer(
              children: [
                IconFont(IconNames.xiaopiao_1, size: 24.w),
                SizedBox(width: 8.w),
                Text(
                  PrintFieldConfig.printBillList[widget.billType] ?? "",
                  style: TextStyle(
                    color: AppColors.titleBoldTextColor,
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          IconFont(IconNames.jiantou, size: 12.w, color: "#FF979797"),
        ],
      ),
    );
  }

  //单据选择列表
  _showBillSelectWindow(BuildContext context) {
    HaloPopWindow().show(
      _printBillGlobalKey,
      intervalLeft: 24.w,
      intervalTop: -18.h,
      gravity: PopWindowGravity.bottom,
      backgroundColor: Colors.transparent,
      // intervalTop: 10,
      child: Container(
        width: 660.w,
        height: PrintFieldConfig.printBillList.length * 61.w,
        constraints: BoxConstraints(maxHeight: 800.w),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: AppColors.dividerColor),
          borderRadius: BorderRadius.all(Radius.circular(8.w)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(127),
              offset: const Offset(10, 40),
              blurRadius: 45,
              spreadRadius: 0,
            ),
          ],
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(0),
          itemCount: PrintFieldConfig.printBillList.values.length,
          itemBuilder: (buildContext, index) {
            return buildBillSelectItem(
              name: PrintFieldConfig.printBillList.values.elementAt(index),
              index: index,
              onSelectItem: (selectIndex) {
                HaloPopWindow().disMiss();
                BillType selectBillType = PrintFieldConfig.printBillList.keys
                    .elementAt(selectIndex);
                if (selectBillType == widget.billType) {
                  return;
                }

                if (checkUnSaveTips()) {
                  HaloPosAlertDialog.showAlertDialog(
                    context,
                    dismissOnTouchOutside: false,
                    dismissOnBackKeyPress: false,
                    content: StringRes.TIP_PRINT_CHANGE_BILL.getText(context),
                    onSubmitCallBack: () {
                      widget.onBillTypeChangeCallBack(selectBillType);
                    },
                  );
                  return;
                }
                widget.onBillTypeChangeCallBack(selectBillType);
              },
            );
          },
        ),
      ),
    );
  }

  ///单据弹框行组件
  Widget buildBillSelectItem({
    required String name,
    required int index,
    Function(int)? onSelectItem,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (null != onSelectItem) {
          onSelectItem(index);
        }
      },
      child: Container(
        height: 60.w,
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(color: AppColors.dividerColor)),
        ),
        child: Text(
          name,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(fontSize: 24.sp, color: AppColors.normalTextColor),
        ),
      ),
    );
  }

  ///单据切换未保存提示
  bool checkUnSaveTips() {
    return jsonEncode(widget.printConfig[widget.billType]) !=
        jsonEncode(_printConfig[widget.billType]);
  }

  //endregion
}

//region 打印字段显示配置信息组件
class _PrintFieldShowConfigPage extends StatefulWidget {
  final StoreInfo storeInfo;
  final StoreCashier storeCashier;

  final Function(PrintConfigInfo changePrintConfig) onEditPrintConfigCallBack;

  ///获取对应单据的打印配置名称
  final List<String> printConfigName;

  ///获取对应单据的打印配置项
  final Map<String, PrintConfigInfo> printConfig;

  ///获取对应单据的打印配置项
  final String printType;

  const _PrintFieldShowConfigPage({
    required this.storeCashier,
    required this.storeInfo,
    required this.printConfig,
    required this.printConfigName,
    required this.printType,
    required this.onEditPrintConfigCallBack,
  });

  @override
  State<StatefulWidget> createState() {
    return _PrintFieldShowConfigPageState();
  }
}

class _PrintFieldShowConfigPageState extends State<_PrintFieldShowConfigPage> {
  late Map<String, PrintConfigInfo> _printConfig;

  ///获取对应单据的打印配置项
  Map<PrintConfigGroup, bool> fieldConfigClose = {};

  ScrollController scrollController = ScrollController();

  @override
  void initState() {
    _printConfig = widget.printConfig;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant _PrintFieldShowConfigPage oldWidget) {
    _printConfig = widget.printConfig;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller: scrollController,
      child: HaloContainer(
        crossAxisAlignment: CrossAxisAlignment.start,
        direction: Axis.vertical,
        children: getPrintConfigListWidget(),
      ),
    );
  }

  ///构建单据打印字段内容
  List<Widget> getPrintConfigListWidget() {
    List<Widget> printItems = [];
    for (PrintConfigGroup item in PrintConfigGroup.values) {
      List<String>? groupFieldList =
          PrintFieldConfig.printConfigGroupList[item];
      if (null == groupFieldList || groupFieldList.isEmpty) {
        continue;
      }

      ///将单据配置所需配置的打印字段进行分组
      List<String> billGroupPrintField =
          widget.printConfigName
              .where((element) => groupFieldList.contains(element))
              .toList();

      if (billGroupPrintField.isEmpty) {
        continue;
      }
      String groupName = PrintFieldConfig.printConfigGroupName[item] ?? "";
      printItems.add(_buildItem(item, groupName, billGroupPrintField));
    }
    return printItems;
  }

  ///构建单据配置的每一类分组组件
  ///billGroupPrintField 单据配置中每一组分组的内容
  Widget _buildItem(
    PrintConfigGroup item,
    String title,
    List<String> billGroupPrintField,
  ) {
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      margin: EdgeInsets.only(bottom: 20.h),
      children: [
        HaloContainer(
          color: AppColors.pageBackgroundColor,
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          padding: EdgeInsets.only(left: 16.w, top: 8.h, bottom: 8.h),
          children: [
            HaloPosLabel(
              title,
              textStyle: TextStyle(
                fontSize: 22.sp,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                if (!fieldConfigClose.containsKey(item)) {
                  setState(() {
                    fieldConfigClose[item] = true;
                  });
                } else {
                  setState(() {
                    fieldConfigClose[item] = !fieldConfigClose[item]!;
                  });
                }
              },

              child: Container(
                width: 80.w,
                height: 36.h,
                alignment: Alignment.centerRight,
                padding: EdgeInsets.only(right: 24.w),
                child: Transform.rotate(
                  angle: fieldConfigClose[item] ?? false ? 0 : math.pi,
                  child: IconFont(
                    IconNames.jiantou,
                    size: 12.w,
                    color: "#FF979797",
                  ),
                ),
              ),
            ),
          ],
        ),
        Visibility(
          visible:
              !fieldConfigClose.containsKey(item) || !fieldConfigClose[item]!,
          child: Container(
            padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 18.h),
            child: GridListView(
              dataCount: billGroupPrintField.length,
              columnCount: 5,
              itemBuilder: (context, index) {
                String fieldKey = billGroupPrintField[index];
                // bool isCheck = _printConfig[fieldKey] ?? false;
                return buildFieldItem(fieldKey);
              },
            ),
          ),
        ),
        // buildItemContent(item.index == PrintConfigGroup.otherInfo.index),
      ],
    );
  }

  ///构建打印字段项
  Widget buildFieldItem(String fieldKey) {
    bool isCheck = _printConfig[fieldKey]?.selected ?? false;
    return GestureDetector(
      onTap: () {
        PrintConfigInfo? printConfigDto = _printConfig[fieldKey];
        if (_printConfig.containsKey(fieldKey) && null != printConfigDto) {
          printConfigDto.selected = !isCheck;
          printConfigDto.fieldKey = fieldKey;
          printConfigDto.cashierId = widget.storeCashier.id ?? "0";
        } else {
          printConfigDto = PrintConfigInfo();
          printConfigDto.id = BillTool.getVchcode();
          printConfigDto.selected = true;
          printConfigDto.fieldKey = fieldKey;
          printConfigDto.printType = widget.printType;
          printConfigDto.otypeId = widget.storeInfo.otypeId ?? "0";
          printConfigDto.cashierId = widget.storeCashier.id ?? "0";
          printConfigDto.profileId = widget.storeCashier.profileId ?? "0";
        }

        ///不允许取消的 强制将选中置为true
        if (PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)) {
          printConfigDto.selected = true;
        }
        widget.onEditPrintConfigCallBack(printConfigDto);
      },
      child: Container(
        decoration: BoxDecoration(
          color: getFieldItemBackGroundColor(fieldKey, isCheck),
          border: Border.all(color: getFieldItemBorderColor(fieldKey, isCheck)),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: Stack(
          clipBehavior: Clip.hardEdge,
          children: [
            Container(
              constraints: BoxConstraints(minHeight: 48.h),
              alignment: Alignment.center,
              child: Text(
                PrintFieldConfig.printConfigNameStr[fieldKey] ?? "未配置",
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: getFieldItemFontColor(fieldKey, isCheck),
                  fontSize: 20.sp,
                ),
              ),
            ),
            Visibility(
              visible:
                  isCheck ||
                  PrintFieldConfig.unCancelPrintConfig.contains(fieldKey),
              child: Positioned(
                right: -1,
                bottom: -1,
                child: IconFont(
                  IconNames.xuanzhonggou,
                  color:
                      PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)
                          ? "#FFB9BAC0"
                          : "#FF2769FF",
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  //region 打印显示字段颜色配置

  ///获取字段配置项背景色
  Color getFieldItemBackGroundColor(String fieldKey, bool isCheck) {
    //选择选中不能取消选择的打印字段（必选字段）
    if (PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)) {
      return AppColors.unEnableBackgroundBColor;
    }
    if (isCheck) {
      return AppColors.selectbackGroundColor;
    }
    return Colors.white;
  }

  ///获取字段配置项边框色
  Color getFieldItemBorderColor(String fieldKey, bool isCheck) {
    //选择选中不能取消选择的打印字段（必选字段）
    if (PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)) {
      return AppColors.unEnableBorderColor;
    }
    if (isCheck) {
      return AppColors.accentColor;
    }
    return AppColors.unEnableBorderColor;
  }

  ///获取字段配置项的边框颜色
  Color getFieldItemFontColor(String fieldKey, bool isCheck) {
    //选择选中不能取消选择的打印字段（必选字段）
    if (PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)) {
      return AppColors.titleBoldTextColor;
    }
    if (isCheck) {
      return AppColors.accentColor;
    }
    return AppColors.titleBoldTextColor;
  }

  //endregion
}
//endregion

//endregion

//region 打印预览信息组件
class _PrintViewPage extends StatefulWidget {
  final Map<String, PrintConfigInfo> printConfigMap;
  final BillType billType;
  final StoreInfo storeInfo;
  final SystemConfigDto systemConfigDto;
  final List<String>? billPrintConfig;
  final String? selectField;

  const _PrintViewPage({
    Key? key,
    required this.printConfigMap,
    required this.billType,
    required this.storeInfo,
    required this.billPrintConfig,
    required this.systemConfigDto,
    this.selectField,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _PrintViewPageState();
  }
}

class _PrintViewPageState extends State<_PrintViewPage> {
  Map<String, PrintConfigInfo> _printConfigMap = {};
  PrintWidth _printWidth =
      PrintWidth.values[SpTool.getPrintPageWidth()] ?? PrintWidth.w_58mm;

  @override
  initState() {
    super.initState();
    _printConfigMap = widget.printConfigMap;
  }

  //
  @override
  void didUpdateWidget(covariant _PrintViewPage oldWidget) {
    _printConfigMap = widget.printConfigMap;
    super.didUpdateWidget(oldWidget);
  }

  refreshView(Map<String, PrintConfigInfo> printConfig) {
    setState(() {
      _printConfigMap = printConfig;
    });
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      mainAxisSize: MainAxisSize.max,
      children: [
        HaloContainer(
          margin: EdgeInsets.only(bottom: 26.h),
          children: [
            buildPrintPageWidthItem(PrintWidth.w_58mm),
            buildPrintPageWidthItem(PrintWidth.w_80mm),
          ],
        ),
        Expanded(
          child: SingleChildScrollView(
            child: HaloContainer(
              width: _printWidth == PrintWidth.w_58mm ? 365.w : 504.w,
              direction: Axis.vertical,
              padding: EdgeInsets.symmetric(vertical: 12.h),
              borderShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(127),
                  blurRadius: 5,
                  spreadRadius: 0,
                ),
              ],
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              color: Colors.white,
              children: [
                _buildPrintTitle(),
                _buildPrintDetail(),
                _buildPrintDivider(),
                _buildPrintSumAndFeeInfo(),
                _buildVipInfo(),
                _buildReceiverInfo(),
                _buildShopInfo(),
                _buildPrintInfo(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPrintDivider() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 24.w),
      child: DottedLine(strokeWidth: 1.w),
    );
  }

  GestureDetector buildPrintPageWidthItem(PrintWidth printWidth) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _printWidth = printWidth;
          SpTool.savePrintPageWidth(_printWidth.index);
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: 12.h),
        decoration: BoxDecoration(
          color:
              _printWidth == printWidth ? AppColors.accentColor : Colors.white,
          borderRadius:
              printWidth == PrintWidth.w_58mm
                  ? BorderRadius.only(bottomLeft: Radius.circular(6.w))
                  : BorderRadius.only(bottomRight: Radius.circular(6.w)),
        ),
        child: Text(
          printWidth.name,
          style: TextStyle(
            color:
                _printWidth == printWidth
                    ? Colors.white
                    : AppColors.normalFontColor,
            fontSize: AppPosSize.secondaryTitleFontSize.sp,
          ),
        ),
      ),
    );
  }

  buildViewItem({
    required String fieldKey,
    required Widget child,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    EdgeInsetsGeometry? padding,
  }) {
    if (!PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      fieldKey,
    )) {
      return const SizedBox();
    }
    bool isSelected =
        StringUtil.isNotEmpty(widget.selectField) &&
        widget.selectField == fieldKey;
    Widget childContent = HaloContainer(
      padding: padding ?? EdgeInsets.symmetric(horizontal: 20.w),
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: mainAxisAlignment,
      color: isSelected ? AppColors.accentColor.withAlpha(25) : Colors.white,
      children: [child],
    );

    if (!isSelected) {
      return childContent;
    }
    return DottedBorder(
      color: AppColors.accentColor,
      dashPattern: const [2, 2],
      strokeWidth: 1.w,
      child: childContent,
    );
  }

  ///构建打印表头
  Widget _buildPrintTitle() {
    List<Widget> weights = [];

    ///表头信息
    _addDecorateInfo(weights, fieldKey: PrintFieldConfig.decorateTop);

    String? billTypeTitle = PrintTool.buildShowFieldName(
      _printConfigMap,
      PrintFieldConfig.billName,
      defaultShowName: billTypeDataName[widget.billType] ?? '',
    );

    weights.add(
      Center(
        child: HaloContainer(
          children: [
            buildViewItem(
              fieldKey: PrintFieldConfig.shopName,
              padding: EdgeInsets.zero,
              child: Text(
                "${widget.storeInfo.fullname}",
                style: TextStyle(
                  fontSize: 26.sp,
                  color: AppColors.normalTextColor,
                ),
              ),
            ),
            Visibility(
              visible: PrintTool.isShowFieldName(
                widget.billPrintConfig,
                _printConfigMap,
                PrintFieldConfig.billName,
              ),
              child: buildViewItem(
                padding: EdgeInsets.zero,
                fieldKey: PrintFieldConfig.billName,
                child: Text(
                  "·$billTypeTitle",
                  style: TextStyle(
                    fontSize: 26.sp,
                    color: AppColors.normalTextColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    ///门店表头信息
    weights.add(_buildShopLogo());

    ///收银员名称
    _addRowWidget(
      widgets: weights,
      fieldKey: PrintFieldConfig.cashierName,
      value: LoginCenter.getLoginUser().user,
    );

    ///收银员工号
    _addRowWidget(
      widgets: weights,
      value: "0001",
      fieldKey: PrintFieldConfig.cashierNo,
    );

    _addBillNumber(weights, PrintFieldConfig.billNumber);

    ///订单编号（全渠道订单使用）
    _addBillNumber(weights, PrintFieldConfig.orderNumber);

    _addRowWidget(
      widgets: weights,
      value: "xxx",
      fieldKey: PrintFieldConfig.kFullName,
    );
    _addRowWidget(
      widgets: weights,
      value: "xxx",
      fieldKey: PrintFieldConfig.kFullName2,
    );
    _addRowWidget(
      widgets: weights,
      value: "xxx",
      fieldKey: PrintFieldConfig.eTypeName,
    );
    _addRowWidget(
      widgets: weights,
      value: "xxx",
      fieldKey: PrintFieldConfig.createETypeName,
    );
    _addRowWidget(
      widgets: weights,
      value: "2021-08-21 18:08:09",
      fieldKey: PrintFieldConfig.billDate,
    );
    _addRowWidget(
      widgets: weights,
      value: "张三",
      fieldKey: PrintFieldConfig.billGuide,
    );
    _addRowWidget(
      widgets: weights,
      value: "xxxxx",
      fieldKey: PrintFieldConfig.billMemo,
    );
    _addRowWidget(
      widgets: weights,
      value: "xxxxx",
      fieldKey: PrintFieldConfig.summary,
    );
    _addRowWidget(
      widgets: weights,
      value: "xxxx",
      fieldKey: PrintFieldConfig.comment,
    );

    _addRowWidget(
      widgets: weights,
      value: "2021-08-21 18:08:09",
      fieldKey: PrintFieldConfig.payDate,
    );
    _addRowWidget(
      widgets: weights,
      value: "xxxxx",
      fieldKey: PrintFieldConfig.buyerMessage,
    );

    return buildSinglePartWidget(weights);
  }

  ///门店logo
  Widget _buildShopLogo() {
    return buildViewItem(
      fieldKey: PrintFieldConfig.shopLogo,
      mainAxisAlignment:
          PrintFieldConfig
              .printLocationAlignment[_printConfigMap[PrintFieldConfig.shopLogo]
                  ?.printLocation ??
              0] ??
          MainAxisAlignment.center,
      child: PrintTool.buildImageView(
        widget.storeInfo.shopLogoUrl,
        width: 120,
        localFile: false,
      ),
    );
  }

  ///构建单据编号
  void _addBillNumber(List<Widget> weights, String fieldKey) {
    if (!PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      fieldKey,
    )) {
      return;
    }

    PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
      _printConfigMap[fieldKey],
    );

    ///二维码展示
    if (printGeneralConfig.content == BillNumberModeEnum.scan.index) {
      weights.add(
        buildViewItem(
          mainAxisAlignment: MainAxisAlignment.center,
          fieldKey: fieldKey,
          child: HaloContainer(
            direction: Axis.vertical,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              QrImageView(
                data: "WSD-20210821-001",
                version: QrVersions.auto,
                size: 100.0,
              ),
              HaloPosLabel(
                "WSD-20210821-001",
                textStyle: TextStyle(
                  fontSize: 22.sp,
                  color: AppColors.normalTextColor,
                ),
              ),
            ],
          ),
        ),
      );
      return;
    }

    ///文本展示
    _addRowWidget(
      widgets: weights,
      value: "WSD-20210821-001",
      fieldKey: fieldKey,
    );
  }

  Widget _buildPrintDetail() {
    ///换货单需要打印换入和换出，单独处理
    if (widget.billType == BillType.SaleChangeBill) {
      return _buildChangePrintContent(showPrice: true);
    }

    ///调拨单以及调拨订单不打印价格
    return _buildPrintContent(
      showPrice:
          widget.billType != BillType.TransferOrder &&
          widget.billType != BillType.GoodsTrans,
    );
  }

  ///构建打印表体
  Widget _buildPrintContent({bool showPrice = true}) {
    ///预览商品数据
    List<Map<String, dynamic>> demoPTypeList = [
      {
        "name": PrintTool.buildShowFieldName(
          _printConfigMap,
          PrintFieldConfig.pFullName,
          defaultShowName: "名称",
        ),
        "retailPrice": PrintTool.buildShowFieldName(
          _printConfigMap,
          PrintFieldConfig.retailPrice,
          defaultShowName: "零售价",
        ),
        "price": "现价",
        "qty": "数量",
        "total": "小计",
      },
      {
        "name": "衬衫",
        "retailPrice": "10.05",
        "price": "10.04",
        "qty": "1",
        "total": "10.4",
        "batchNo": "x001",
        "brandName": "品牌",
        "propName1": "红色",
        "propName2": "M",
        "protectDay": "2023-10-12",
        "pTypeUserCode": "2323",
        "skuBarCode": "3344",
        "taxRate": "4",
        "taxTotal": "0.38",
      },
      {
        "name": "蜂蜜芥末脆皮鸡爽炸鸡腿堡末脆皮鸡爽炸鸡腿堡",
        "retailPrice": "12.02354",
        "price": "12.0581",
        "qty": "2",
        "total": "24.1161",
        "pTypeUserCode": "123",
        "sn": "343;3434",
        "standard": "规格",
        "type": "型号",
        "brandName": "品牌",
        "taxRate": "13",
        "taxTotal": "1.5",
      },
      {
        "name": "汉堡套餐",
        "retailPrice": "30",
        "price": "30",
        "qty": "1",
        "total": "30",
        "pTypeUserCode": "4577",
        "skuBarCode": "5566",
        "isCombo": true,
      },
      {
        "name": "中杯可乐",
        "retailPrice": "30",
        "price": "30",
        "qty": "1000",
        "total": "30000",
        "pTypeUserCode": "4577",
        "skuBarCode": "5566",
        "standard": "规格",
      },
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder:
            (context, index) => _createBillDetailRow(
              demoPTypeList[index],
              index,
              showPrice: showPrice,
            ),
        separatorBuilder: (context, index) => SizedBox(height: 8.h),
        itemCount: demoPTypeList.length,
      ),
    );
  }

  ///构建打印表体
  Widget _buildChangePrintContent({bool showPrice = true}) {
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 24.w),
          child: HaloLabel("换入明细："),
        ),
        Container(
          padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 24.w),
          child: const DottedLine(color: AppColors.dasherLineColor),
        ),
        _buildPrintContent(showPrice: showPrice),
        Container(
          margin: EdgeInsets.only(bottom: 16.h),
          padding: EdgeInsets.symmetric(vertical: 6.h),
          child: const DottedLine(),
        ),
        Container(
          padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 24.w),
          child: HaloLabel("换出明细："),
        ),
        Container(
          padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 24.w),
          child: const DottedLine(color: AppColors.dasherLineColor),
        ),
        _buildPrintContent(showPrice: showPrice),
      ],
    );
  }

  ///打印标题行
  Widget _createBillDetailRow(
    Map<String, dynamic> itemData,
    int index, {
    bool showPrice = true,
  }) {
    ///标题行打印名称
    if (index == 0) {
      return buildBillDetailItem(
        name: itemData["name"],
        price: itemData["price"],
        retailPrice: itemData["retailPrice"],
        qty: itemData["qty"],
        total: itemData["total"],
        showPrice: showPrice,
        titleRow: true,
      );
    } else {
      ///打印单据明细内容
      return buildContentItem(
        itemData,
        prefix: "${index.toString()}.",
        showPrice: showPrice,
      );
    }
  }

  HaloContainer buildContentItem(
    Map<String, dynamic> itemData, {
    String? prefix,
    bool showPrice = true,
    bool isComboDetail = false,
  }) {
    ///商品编码
    String pTypeCode = "";

    //商品编码 可展示sku条码或者商品编号
    if (PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      PrintFieldConfig.pTypeCode,
    )) {
      PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
        _printConfigMap[PrintFieldConfig.pTypeCode],
      );
      pTypeCode =
          printGeneralConfig.content == PTypeModeEnum.skuBarCode.index
              ? itemData["skuBarCode"] ?? ""
              : itemData["pTypeUserCode"] ?? "";
    }

    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ///隐藏也保留空间占位
        Visibility(
          visible: null != prefix,
          maintainState: true,
          child: Container(
            margin: EdgeInsets.only(top: 4.h),
            child: Text("$prefix", style: buildPrintViewStyle()),
          ),
        ),
        Expanded(
          child: HaloContainer(
            direction: Axis.vertical,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                buildPrintPtypeTitle(itemData),
                style: buildPrintViewStyle(),
              ),
              Visibility(
                visible:
                    getBatchInfo(
                      itemData["batchNo"],
                      itemData["protectDay"],
                    ).isNotEmpty,
                child: Container(
                  padding: EdgeInsets.only(bottom: 10.w),
                  child: HaloPosLabel(
                    "批次:${getBatchInfo(itemData["batchNo"], itemData["protectDay"])}",
                    textStyle: buildPrintViewStyle(),
                  ),
                ),
              ),
              Visibility(
                visible:
                    (PrintTool.isShowFieldName(
                      widget.billPrintConfig,
                      _printConfigMap,
                      PrintFieldConfig.pTypeSn,
                    )) &&
                    StringUtil.isNotEmpty(itemData["sn"]),
                child: Container(
                  padding: EdgeInsets.only(bottom: 10.w),
                  child: HaloPosLabel(
                    "${PrintTool.buildShowFieldName(_printConfigMap, PrintFieldConfig.pTypeSn, defaultShowName: "序列号")}:${itemData["sn"]}",
                    textStyle: buildPrintViewStyle(),
                  ),
                ),
              ),
              Visibility(
                visible:
                    (widget.systemConfigDto.sysGlobalEnabledTax &&
                        widget.systemConfigDto.sysGlobalEnabledSaleTax) &&
                    (PrintTool.isShowFieldName(
                          widget.billPrintConfig,
                          _printConfigMap,
                          PrintFieldConfig.taxRate,
                        ) ||
                        PrintTool.isShowFieldName(
                          widget.billPrintConfig,
                          _printConfigMap,
                          PrintFieldConfig.taxTotal,
                        )),
                child: Container(
                  padding: EdgeInsets.only(bottom: 10.w),
                  child: HaloPosLabel(
                    buildTaxInfo(itemData),
                    textStyle: buildPrintViewStyle(),
                  ),
                ),
              ),

              ///套餐明细 仅打印明细数量
              buildBillDetailItem(
                name: pTypeCode,
                price:
                    isComboDetail
                        ? ""
                        : DecimalDisplayHelper.getPriceFixed(
                          itemData["price"] ?? "",
                        ),
                retailPrice:
                    isComboDetail
                        ? ""
                        : DecimalDisplayHelper.getPriceFixed(
                          itemData["retailPrice"] ?? "",
                        ),
                qty: itemData["qty"] ?? "",
                total:
                    isComboDetail
                        ? ""
                        : DecimalDisplayHelper.getTotalFixed(
                          itemData["total"] ?? "",
                        ),
                showPrice: showPrice,
              ),
              buildComboDetail(itemData["isCombo"] ?? false, showPrice),
            ],
          ),
        ),
      ],
    );
  }

  String buildPrintPtypeTitle(Map<String, dynamic> itemData) {
    String name = "";

    if (StringUtil.isNotEmpty(itemData["propName1"])) {
      name = "$name:${itemData["propName1"]}";
    }
    if (StringUtil.isNotEmpty(itemData["propName2"])) {
      name = "$name:${itemData["propName2"]}";
    }

    if (PrintTool.isShowFieldName(
          widget.billPrintConfig,
          _printConfigMap,
          PrintFieldConfig.pTypeStandard,
        ) &&
        StringUtil.isNotEmpty(itemData["standard"])) {
      name = "$name:${itemData["standard"]}";
    }

    if (PrintTool.isShowFieldName(
          widget.billPrintConfig,
          _printConfigMap,
          PrintFieldConfig.pTypeType,
        ) &&
        StringUtil.isNotEmpty(itemData["type"])) {
      name = "$name:${itemData["type"]}";
    }
    if (PrintTool.isShowFieldName(
          widget.billPrintConfig,
          _printConfigMap,
          PrintFieldConfig.brand,
        ) &&
        StringUtil.isNotEmpty(itemData["brandName"])) {
      name = "$name:${itemData["brandName"]}";
    }

    name = StringUtil.trim(name, trimStr: ":");
    if (StringUtil.isNotEmpty(name)) {
      return "${itemData["name"]}($name)";
    }
    return itemData["name"] ?? "";
  }

  String buildTaxInfo(Map<String, dynamic> itemData) {
    String taxMsg = "";
    if (!(widget.systemConfigDto.sysGlobalEnabledTax &&
        widget.systemConfigDto.sysGlobalEnabledSaleTax)) {
      return taxMsg;
    }
    if (PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      PrintFieldConfig.taxRate,
    )) {
      taxMsg =
          "${PrintTool.buildShowFieldName(_printConfigMap, PrintFieldConfig.taxRate)}:${itemData["taxRate"] ?? "0"}%；";
    }
    if (PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      PrintFieldConfig.taxTotal,
    )) {
      taxMsg +=
          "${PrintTool.buildShowFieldName(_printConfigMap, PrintFieldConfig.taxTotal)}:￥${itemData["taxTotal"] ?? "0"};";
    }
    return taxMsg;
  }

  Widget buildBillDetailItem({
    String name = "",
    String retailPrice = "",
    String price = "",
    String qty = "",
    String total = "",
    bool showPrice = true,
    bool titleRow = false,
    Widget? child,
  }) {
    List<Widget> contentWidgets = [];

    contentWidgets.add(
      buildDetailItem(
        name,
        fieldKey: PrintFieldConfig.pFullName,
        flex: 2,
        textAlign: titleRow ? TextAlign.center : TextAlign.left,
        titleRow: titleRow,
      ),
    );
    if (showPrice &&
        PrintTool.isShowFieldName(
          widget.billPrintConfig,
          _printConfigMap,
          PrintFieldConfig.retailPrice,
        )) {
      contentWidgets.add(
        buildDetailItem(
          retailPrice,
          flex: 1,
          defaultValue: "0",
          fieldKey: PrintFieldConfig.retailPrice,
          titleRow: titleRow,
        ),
      );
    }
    if (showPrice) {
      contentWidgets.add(
        buildDetailItem(price, defaultValue: "0", titleRow: titleRow),
      );
    }

    contentWidgets.add(
      buildDetailItem(qty, defaultValue: "0", titleRow: titleRow),
    );
    if (showPrice) {
      contentWidgets.add(
        buildDetailItem(total, defaultValue: "0", titleRow: titleRow),
      );
    }
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: contentWidgets,
    );
  }

  ///拼接批次号信息
  String getBatchInfo(String? bathNo, String? productDay) {
    String bathNoStr = "";
    if ((bathNo?.isNotEmpty ?? false) &&
        PrintTool.isShowFieldName(
          widget.billPrintConfig,
          _printConfigMap,
          PrintFieldConfig.pTypeBatchNo,
        )) {
      bathNoStr += "${bathNo ?? ""};";
    }
    if ((productDay?.isNotEmpty ?? false) &&
        PrintTool.isShowFieldName(
          widget.billPrintConfig,
          _printConfigMap,
          PrintFieldConfig.pTypeProductDay,
        )) {
      bathNoStr += productDay ?? "";
    }
    return bathNoStr;
  }

  ///构建套餐明细
  Widget buildComboDetail(bool showComboDetail, bool isShowPrice) {
    ///非套餐或者仅展示套餐名称
    ///
    PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
      _printConfigMap[PrintFieldConfig.pFullName],
    );
    if (!showComboDetail ||
        printGeneralConfig.content == ComboModeEnum.name.index) {
      return const SizedBox();
    }

    ///预览商品数据
    List<Map<String, dynamic>> demoComboDetailList = [
      {
        "name": "可乐",
        "qty": "1",
        "batchNo": "x001",
        "protectDay": "2023-10-12",
        "pTypeUserCode": "2323",
        "skuBarCode": "3344",
      },
      {"name": "汉堡", "qty": "2", "pTypeUserCode": "123"},
      {"name": "薯条", "qty": "1", "pTypeUserCode": "4577", "skuBarCode": "5566"},
    ];

    return HaloContainer(
      direction: Axis.vertical,
      children: [
        buildContentItem(
          demoComboDetailList[0],
          prefix: "- ",
          showPrice: isShowPrice,
          isComboDetail: true,
        ),
        buildContentItem(
          demoComboDetailList[1],
          prefix: "- ",
          showPrice: isShowPrice,
          isComboDetail: true,
        ),
        buildContentItem(
          demoComboDetailList[2],
          prefix: "- ",
          showPrice: isShowPrice,
          isComboDetail: true,
        ),
      ],
    );
  }

  ///通用打印字体样式
  TextStyle buildPrintViewStyle() {
    return TextStyle(color: AppColors.normalTextColor, fontSize: 22.sp);
  }

  ///构建明细行的item
  Widget buildDetailItem(
    String? value, {
    String defaultValue = "",
    String? fieldKey,
    int flex = 1,
    textAlign = TextAlign.center,
    bool titleRow = false,
  }) {
    Widget child = Text(
      value ?? defaultValue,
      textAlign: textAlign,
      style: buildPrintViewStyle(),
    );

    ///标题行并选中字段时，需要增加选中虚框
    if (!StringUtil.isEmpty(fieldKey) &&
        widget.selectField == fieldKey &&
        titleRow) {
      child = buildViewItem(
        fieldKey: fieldKey!,
        padding: EdgeInsets.zero,
        child: Expanded(child: child),
      );
    }
    return Expanded(flex: flex, child: child);
  }

  ///构建打印合计以及费用信息
  Widget _buildPrintSumAndFeeInfo() {
    List<Widget> weights = [];
    _addRowWidget(
      widgets: weights,
      value: "121.05",
      fieldKey: PrintFieldConfig.sumQty,
    );
    _addRowWidget(
      widgets: weights,
      value: "4",
      fieldKey: PrintFieldConfig.tenderSumQty,
    );
    _addRowWidget(
      widgets: weights,
      value: "30064",
      fieldKey: PrintFieldConfig.sumTotal,
    );
    _addRowWidget(
      widgets: weights,
      value: "1.88",
      fieldKey: PrintFieldConfig.sumTaxTotal,
    );
    _addRowWidget(
      widgets: weights,
      value: "121.05",
      fieldKey: PrintFieldConfig.backQty,
    );
    _addRowWidget(
      widgets: weights,
      value: "1000.55",
      fieldKey: PrintFieldConfig.backTotal,
    );

    _addRowWidget(
      widgets: weights,
      value: "换入(4)；换出(4)",
      fieldKey: PrintFieldConfig.changeQty,
    );
    _addRowWidget(
      widgets: weights,
      value: "0",
      fieldKey: PrintFieldConfig.changeTotal,
    );

    _addRowWidget(
      widgets: weights,
      value: "10.12",
      fieldKey: PrintFieldConfig.wTotal,
    );
    _addRowWidget(
      widgets: weights,
      value: "3.55",
      fieldKey: PrintFieldConfig.pettyCash,
    );
    _addRowWidget(
      widgets: weights,
      value: "现金(50)",
      fieldKey: PrintFieldConfig.payment,
    );
    _addRowWidget(
      widgets: weights,
      value: "8",
      fieldKey: PrintFieldConfig.freightFee,
    );
    _addRowWidget(
      widgets: weights,
      value: "12",
      fieldKey: PrintFieldConfig.buyerFreight,
    );

    _addRowWidget(
      widgets: weights,
      value: "",
      fieldKey: PrintFieldConfig.signOut,
    );
    _addRowWidget(
      widgets: weights,
      value: "",
      fieldKey: PrintFieldConfig.signIn,
    );

    return buildSinglePartWidget(weights);
  }

  Widget buildSinglePartWidget(List<Widget> weights) {
    if (weights.isNotEmpty) {
      weights.add(_buildPrintDivider());
    }
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: weights,
    );
  }

  ///构建打印会员信息
  Widget _buildVipInfo() {
    List<Widget> weights = [];

    _addRowWidget(
      widgets: weights,
      value: "张三",
      fieldKey: PrintFieldConfig.vipName,
    );
    _addRowWidget(
      widgets: weights,
      value: "1380000000",
      fieldKey: PrintFieldConfig.vipPhone,
    );
    _addRowWidget(
      widgets: weights,
      value: "100",
      fieldKey: PrintFieldConfig.storeTotal,
    );
    _addRowWidget(
      widgets: weights,
      value: "100",
      fieldKey: PrintFieldConfig.saleScore,
    );
    _addRowWidget(
      widgets: weights,
      value: "1000",
      fieldKey: PrintFieldConfig.scoreTotal,
    );
    _addRowWidget(
      widgets: weights,
      value: "8",
      fieldKey: PrintFieldConfig.freightFee,
    );
    _addRowWidget(
      widgets: weights,
      value: "12",
      fieldKey: PrintFieldConfig.buyerFreight,
    );
    return buildSinglePartWidget(weights);
  }

  ///构建收货人信息
  Widget _buildReceiverInfo() {
    List<Widget> weights = [];

    _addRowWidget(
      widgets: weights,
      value: "张三",
      fieldKey: PrintFieldConfig.receiverName,
    );
    _addRowWidget(
      widgets: weights,
      value: "13888888888",
      fieldKey: PrintFieldConfig.receiverMobile,
    );
    _addRowWidget(
      widgets: weights,
      value: "成都市武侯区xxx小区",
      fieldKey: PrintFieldConfig.receiverAddress,
    );
    return buildSinglePartWidget(weights);
  }

  ///构建店铺信息
  Widget _buildShopInfo() {
    List<Widget> weights = [];
    _addRowWidget(
      widgets: weights,
      value: "13809090900",
      fieldKey: PrintFieldConfig.shopPhone,
    );
    _addRowWidget(
      widgets: weights,
      value: "成都市高新区天软件园件xxx",
      fieldKey: PrintFieldConfig.address,
    );
    _addRowWidget(
      widgets: weights,
      value: "门店搞活动",
      fieldKey: PrintFieldConfig.shopNotice,
    );
    _addShopScan(weights);

    _addRowWidget(
      widgets: weights,
      value: "xxxxxx",
      fieldKey: PrintFieldConfig.pickupCode,
    );
    _addInvoiceCode(weights);
    _addDecorateInfo(weights, fieldKey: PrintFieldConfig.decorateTail);

    return buildSinglePartWidget(weights);
  }

  ///构建门店二维码
  void _addShopScan(List<Widget> list) {
    if (!PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      PrintFieldConfig.shopScan,
    )) {
      return;
    }

    list.add(
      buildViewItem(
        fieldKey: PrintFieldConfig.shopScan,
        mainAxisAlignment: MainAxisAlignment.center,
        padding: EdgeInsets.zero,
        child: HaloContainer(
          direction: Axis.vertical,
          children: [
            PrintTool.buildImageView(
              widget.storeInfo.shopScanUrl,
              localFile: false,
            ),
            HaloPosLabel(
              "${widget.storeInfo.shopScanMemo}",
              textAlign: TextAlign.center,
              textStyle: buildPrintViewStyle(),
            ),
          ],
        ),
      ),
    );
  }

  ///构建票装饰
  void _addDecorateInfo(List<Widget> list, {required String fieldKey}) {
    if (!PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      fieldKey,
    )) {
      return;
    }
    PrintConfigInfo? printConfigInfo = _printConfigMap[fieldKey];
    PrintDecorateConfig printDecorateConfig = PrintDecorateConfig();
    if (null != printConfigInfo &&
        StringUtil.isNotEmpty(printConfigInfo.contentConfig)) {
      printDecorateConfig = PrintDecorateConfig.fromMap(
        jsonDecode(printConfigInfo.contentConfig),
      );
    }

    ///票图装饰为图片
    if (printDecorateConfig.printStyleMode == PrintStyleModeEnum.image.index) {
      list.add(
        buildViewItem(
          fieldKey: fieldKey,
          mainAxisAlignment: MainAxisAlignment.center,
          child: PrintTool.buildImageView(
            printDecorateConfig.imgUrl,
            localFile: false,
          ),
        ),
      );
      return;
    }

    list.add(
      buildViewItem(
        fieldKey: fieldKey,
        mainAxisAlignment: MainAxisAlignment.center,
        child: HaloPosLabel(
          printDecorateConfig.content,
          textAlign: TextAlign.center,
          textStyle: buildPrintViewStyle(),
          maxLines: 5,
        ),
      ),
    );
  }

  ///构建打印信息
  Widget _buildPrintInfo() {
    List<Widget> weights = [];
    _addRowWidget(
      widgets: weights,
      value: "2",
      fieldKey: PrintFieldConfig.tenderPrintCount,
    );
    PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
      _printConfigMap[PrintFieldConfig.printCount],
    );
    _addRowWidget(
      widgets: weights,
      name: "打印张数",
      value: "3/${printGeneralConfig.content}",
      fieldKey: PrintFieldConfig.printCount,
    );

    _addRowWidget(
      widgets: weights,
      value: "2021-08-31 18:20:30",
      fieldKey: PrintFieldConfig.printDate,
    );

    _addRowWidget(
      widgets: weights,
      value: "2021-08-31 18:20:30",
      fieldKey: PrintFieldConfig.tenderPrintDate,
    );

    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: weights,
    );
  }

  void _addRowWidget({
    required List<Widget> widgets,
    required String fieldKey,
    String? value,
    String? name,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
  }) {
    if (!PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      fieldKey,
    )) {
      return;
    }

    name ??= PrintTool.buildShowFieldName(_printConfigMap, fieldKey);
    widgets.add(
      buildViewItem(
        fieldKey: fieldKey,
        mainAxisAlignment: mainAxisAlignment,
        child: Expanded(
          child: HaloPosLabel(
            "$name${StringUtil.isEmpty(value) ? "" : "：$value"}",
            textStyle: buildPrintViewStyle(),
          ),
        ),
      ),
    );
  }

  ///构建发票打印二维码
  void _addInvoiceCode(List<Widget> widgets) {
    if (!PrintTool.isShowFieldName(
      widget.billPrintConfig,
      _printConfigMap,
      PrintFieldConfig.invoiceCode,
    )) {
      return;
    }
    PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
      _printConfigMap[PrintFieldConfig.invoiceCode],
    );
    widgets.add(
      buildViewItem(
        fieldKey: PrintFieldConfig.invoiceCode,
        mainAxisAlignment: MainAxisAlignment.center,
        padding: EdgeInsets.zero,
        child: HaloContainer(
          direction: Axis.vertical,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            QrImageView(
              data: buildInvoiceUrl(),
              version: QrVersions.auto,
              size: 180.0,
            ),
            HaloPosLabel(
              printGeneralConfig.text,
              textStyle: buildPrintViewStyle(),
            ),
          ],
        ),
      ),
    );
    //  ,
  }

  ///生成发票链接
  String buildInvoiceUrl() {
    LoginUserModel loginUserModel = LoginCenter.getLoginUser();
    return "${loginUserModel.requestUrl}/sale/OpenInvoice.html?vchcode=123&profileid=343343&sign=234234234";
  }
}

//endregion

//region 自定义配置

class _CustomPrintConfigPage extends StatefulWidget {
  final String selectFieldKey;
  final PrintConfigInfo? printConfigInfo;

  final Function(PrintConfigInfo printConfigInfo, {bool? syncOtherBill})
  onEditChangeBack;

  const _CustomPrintConfigPage(
    this.printConfigInfo, {
    Key? key,
    this.selectFieldKey = "",
    required this.onEditChangeBack,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _CustomPrintConfigPageState();
  }
}

class _CustomPrintConfigPageState extends State<_CustomPrintConfigPage> {
  TextEditingController txtCustomPrintNameController = TextEditingController();
  PrintConfigInfo? _printConfigInfo;

  @override
  Widget build(BuildContext context) {
    _printConfigInfo = widget.printConfigInfo;
    return HaloContainer(
      padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 24.w),
      color: Colors.white,
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Center(
          child: HaloLabel(
            "小票字段样式设置",
            textStyle: TextStyle(
              fontSize: AppPosSize.firstTitleFontSize.sp,
              color: AppColors.normalFontColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        SizedBox(height: 6.h),
        _buildCustomPrintWidget(),
      ],
    );
  }

  _buildCustomPrintWidget() {
    if (StringUtil.isEmpty(widget.selectFieldKey) ||
        null == _printConfigInfo ||
        !_printConfigInfo!.selected) {
      return HaloLabel("当前无选中字段");
    }
    txtCustomPrintNameController.text =
        widget.printConfigInfo?.customFieldName ?? "";

    List<Widget> widgetList = [];

    ///是否允许配置打印字段名称
    widgetList.add(
      HaloLabel(
        "打印字段：${PrintFieldConfig.printConfigNameStr[widget.selectFieldKey] ?? ''}",
        textStyle: TextStyle(
          fontSize: AppPosSize.secondaryTitleFontSize.sp,
          color: AppColors.normalFontColor,
        ),
      ),
    );

    ///添加自定义打印名称
    _addCustomPrintNameWidget(widgetList);

    ///添加打印位置
    _addPrintLocation(widgetList);

    _addPrintPTypeCode(widgetList);

    ///添加单据编号
    _addBillNumber(widgetList);

    ///添加发票二维码备注
    _addBillInvoiceMemo(widgetList);

    ///添加套餐打印模式
    _addPTypeName(widgetList);

    ///票头票尾
    _addDecorate(widgetList);

    ///打印张数
    _addPrintCount(widgetList);

    ///是否同步其他小票模版
    _addSyncOtherTemplate(widgetList);
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgetList,
    );
  }

  ///添加自定义打印名称
  void _addCustomPrintNameWidget(List<Widget> widgetList) {
    ///禁止配置打印字段名称
    if (PrintFieldConfig.unAllowCustomNameConfig.contains(
      widget.selectFieldKey,
    )) {
      return;
    }
    widgetList.add(
      buildItemRow(
        title: "自定义名称",
        child: HaloContainer(
          mainAxisSize: MainAxisSize.max,
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
          border: Border.all(color: AppColors.borderColor, width: 1.w),
          borderRadius: BorderRadius.all(Radius.circular(4.w)),
          children: [
            Expanded(
              child: HaloTextField(
                controller: txtCustomPrintNameController,
                fontSize: AppPosSize.secondaryTitleFontSize.sp,
                maxLength: 6,
                hintText: "可输入自定义打印名称",
                hintStyle: TextStyle(
                  fontSize: AppPosSize.secondaryTitleFontSize.sp,
                  color: AppColors.hintColor,
                ),
                contentPadding: 0,
                onChanged: (String text) {
                  if (null != _printConfigInfo) {
                    _printConfigInfo!.customFieldName = text;
                    widget.onEditChangeBack(_printConfigInfo!);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///添加打印位置
  void _addPrintLocation(List<Widget> widgetList) {
    ///配置允许打印位置的字段
    if (!PrintFieldConfig.allowPrintLocationConfig.contains(
      widget.selectFieldKey,
    )) {
      return;
    }

    widgetList.add(
      buildSelectWidget(
        "打印位置",
        data: PrintFieldConfig.printLocationEnumName.values.toList(),
        selectIndex: _printConfigInfo?.printLocation ?? 1,
        onSelectClick: (index) {
          if (null != _printConfigInfo) {
            setState(() {
              _printConfigInfo!.printLocation = index;
            });

            widget.onEditChangeBack(_printConfigInfo!);
          }
        },
      ),
    );
  }

  ///添加编码打印内容
  void _addPrintPTypeCode(List<Widget> widgetList) {
    if (widget.selectFieldKey != PrintFieldConfig.pTypeCode) {
      return;
    }
    PrintGeneralConfig printGeneralConfig = PrintGeneralConfig();
    if (null != _printConfigInfo &&
        StringUtil.isNotEmpty(_printConfigInfo!.contentConfig)) {
      printGeneralConfig = PrintGeneralConfig.fromMap(
        json.decode(_printConfigInfo!.contentConfig),
      );
    }

    widgetList.add(
      buildSelectWidget(
        "商品编码",
        data: PrintFieldConfig.pTypeModeEnumName.values.toList(),
        selectIndex: printGeneralConfig.content,
        onSelectClick: (index) {
          if (null != _printConfigInfo) {
            setState(() {
              printGeneralConfig.content = index;
              _printConfigInfo!.contentConfig = json.encode(
                printGeneralConfig.toJson(),
              );
            });
            widget.onEditChangeBack(_printConfigInfo!);
          }
        },
      ),
    );
  }

  ///添加是否同步到其他模版
  void _addSyncOtherTemplate(List<Widget> widgetList) {
    if (!PrintTool.isAllowSyncOtherTemplateConfig(widget.selectFieldKey)) {
      return;
    }

    widgetList.add(
      buildSelectWidget(
        "是否应用到其他小票模版",
        data: PrintFieldConfig.printSelected.values.toList(),
        selectIndex: _printConfigInfo?.syncOtherTemplate ?? false ? 1 : 0,
        onSelectClick: (index) {
          if (null != _printConfigInfo) {
            setState(() {
              _printConfigInfo!.syncOtherTemplate = index == 1;
            });
            widget.onEditChangeBack(_printConfigInfo!);
          }
        },
      ),
    );
  }

  ///构建匹配置行
  Widget buildItemRow({required String title, required Widget child}) {
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      margin: EdgeInsets.only(top: 20.h),
      children: [
        HaloPosLabel(
          title,
          textStyle: TextStyle(
            fontSize: AppPosSize.secondaryTitleFontSize.sp,
            color: AppColors.normalFontColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 12.h),
        child,
      ],
    );
  }

  //region 构建选择组件
  ///构建选择组件
  Widget buildSelectWidget(
    String title, {
    required List<String> data,
    int selectIndex = 0,
    int columnCount = 3,
    required Function(int index) onSelectClick,
  }) {
    return buildItemRow(
      title: title,
      child: GridListView(
        dataCount: data.length,
        columnCount: columnCount,
        itemBuilder: (context, index) {
          return buildSelectFieldItem(
            content: data[index],
            selectIndex: selectIndex,
            index: index,
            onSelected: onSelectClick,
          );
        },
      ),
    );
  }

  ///构建选择组件配置item
  Widget buildSelectFieldItem({
    required String content,
    required int selectIndex,
    required int index,
    required Function(int selectIndex) onSelected,
  }) {
    return GestureDetector(
      onTap: () {
        onSelected(index);
      },
      child: Container(
        alignment: Alignment.center,
        constraints: BoxConstraints(minHeight: 48.h),
        decoration: BoxDecoration(
          color:
              selectIndex == index
                  ? AppColors.selectbackGroundColor
                  : Colors.white,
          border: Border.all(
            color:
                selectIndex == index
                    ? AppColors.accentColor
                    : AppColors.unEnableBorderColor,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: Text(
          content,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            color:
                selectIndex == index
                    ? AppColors.accentColor
                    : AppColors.normalTextColor,
            fontSize: 20.sp,
          ),
        ),
      ),
    );
  }

  //endregion

  //region 构建票头票尾

  ///票装饰内容项（当前仅处理票头票尾）
  void _addDecorate(List<Widget> widgetList) {
    if (widget.selectFieldKey != PrintFieldConfig.decorateTop &&
        widget.selectFieldKey != PrintFieldConfig.decorateTail) {
      return;
    }
    PrintDecorateConfig printDecorateConfig = PrintDecorateConfig();
    if (null != _printConfigInfo &&
        StringUtil.isNotEmpty(_printConfigInfo!.contentConfig)) {
      printDecorateConfig = PrintDecorateConfig.fromMap(
        json.decode(_printConfigInfo!.contentConfig!),
      );
    }

    widgetList.add(
      HaloContainer(
        direction: Axis.vertical,
        children: [
          buildSelectWidget(
            PrintFieldConfig.printConfigNameStr[widget.selectFieldKey] ?? "",
            data: PrintFieldConfig.printStyleModeEnumName.values.toList(),
            selectIndex: printDecorateConfig.printStyleMode,
            onSelectClick: (index) {
              if (null != _printConfigInfo) {
                widget.onEditChangeBack(_printConfigInfo!);

                setState(() {
                  printDecorateConfig.printStyleMode = index;
                  _printConfigInfo!.contentConfig = json.encode(
                    printDecorateConfig.toJson(),
                  );
                });
              }
            },
          ),
          buildDecorateItem(printDecorateConfig),
        ],
      ),
    );
  }

  Widget buildDecorateItem(PrintDecorateConfig printDecorateConfig) {
    if (printDecorateConfig.printStyleMode == PrintStyleModeEnum.image.index) {
      return buildDecorateImage(printDecorateConfig);
    }
    return buildDecorateText(printDecorateConfig);
  }

  ///构建打印文本
  Widget buildDecorateText(PrintDecorateConfig printDecorateConfig) {
    TextEditingController textEditingController = TextEditingController(
      text: printDecorateConfig.content,
    );
    return HaloContainer(
      padding: EdgeInsets.all(8.w),
      mainAxisSize: MainAxisSize.max,
      margin: EdgeInsets.symmetric(vertical: 10.h),
      border: Border.all(color: AppColors.borderColor),
      borderRadius: const BorderRadius.all(Radius.circular(4)),
      children: [
        Expanded(
          child: HaloTextField(
            showCounter: true,
            controller: textEditingController,
            fontSize: 20.sp,
            maxLength: 500,
            hintText: "(选填)添加文本内容",
            contentPadding: 0,
            maxLines: 3,
            onChanged: (String text) {
              printDecorateConfig.content = text;
              _printConfigInfo!.contentConfig = json.encode(
                printDecorateConfig.toJson(),
              );
              widget.onEditChangeBack(_printConfigInfo!);
            },
          ),
        ),
      ],
    );
  }

  ///构建打印图片
  Widget buildDecorateImage(PrintDecorateConfig printDecorateConfig) {
    return GestureDetector(
      onTap: () {
        ImageTool.pickGallery(
          context,
          onSelectBack: (List<String>? selectBackList) async {
            if (null == selectBackList || selectBackList.isEmpty) {
              return;
            }
            File file = File(selectBackList.first);
            UploadQiniuDto uploadQiniuDto = await BaseInfoModel.uploadImages(
              context,
              file,
            );
            setState(() {
              printDecorateConfig.imgUrl = uploadQiniuDto.thumbnail ?? "";
              _printConfigInfo!.contentConfig = json.encode(
                printDecorateConfig.toJson(),
              );
            });
            widget.onEditChangeBack(_printConfigInfo!);
          },
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 10.h),
        child: DottedBorder(
          color: AppColors.borderColor,
          strokeWidth: 1,
          borderType: BorderType.RRect,
          child: HaloContainer(
            height: 150.h,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              HaloContainer(
                visible: StringUtil.isEmpty(printDecorateConfig.imgUrl),
                padding: EdgeInsets.only(
                  top: 24.h,
                  bottom: 16.h,
                  left: 8.w,
                  right: 8.w,
                ),
                direction: Axis.vertical,
                // border: Border.all(color: AppColors.borderColor),
                borderRadius: const BorderRadius.all(Radius.circular(4)),
                children: [
                  IconFont(IconNames.shangchuantupian, size: 26),
                  SizedBox(height: 4.h),
                  HaloPosLabel(
                    "点击上传图片",
                    textStyle: TextStyle(
                      color: AppColors.normalTextColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 22.sp,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  HaloPosLabel(
                    "图片支持JPG,JPEG,BMP,PNG格式，大小不超过2M",
                    textStyle: TextStyle(
                      color: AppColors.hintColor,
                      fontSize: 18.sp,
                    ),
                  ),
                ],
              ),
              Visibility(
                visible: !StringUtil.isEmpty(printDecorateConfig.imgUrl),
                child: PrintTool.buildImageView(
                  printDecorateConfig.imgUrl,
                  localFile: false,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  //
  // Widget _buildPrintStyleModelItem(
  //     String fieldKey, PrintStyleModeEnum modeEnum) {
  //   int index = widget.printConfig[fieldKey] ?? 0;
  //
  //   return GestureDetector(
  //       onTap: () {
  //         _printConfig[fieldKey] = modeEnum.index;
  //         widget.onEditPrintConfigCallBack(_printConfig, fieldKey);
  //       },
  //       child: Container(
  //         alignment: Alignment.center,
  //         constraints: BoxConstraints(minHeight: 48.h),
  //         decoration: BoxDecoration(
  //           color: index == modeEnum.index
  //               ? AppColors.selectbackGroundColor
  //               : Colors.white,
  //           border: Border.all(
  //               color: index == modeEnum.index
  //                   ? AppColors.accentColor
  //                   : AppColors.unEnableBorderColor),
  //           borderRadius: const BorderRadius.all(Radius.circular(4)),
  //         ),
  //         child: HaloPosLabel(
  //           PrintFieldConfig.printStyleModeEnumName[modeEnum] ?? '',
  //           textAlign: TextAlign.center,
  //           textStyle: TextStyle(
  //               color: index == modeEnum.index
  //                   ? AppColors.accentColor
  //                   : AppColors.normalTextColor,
  //               fontSize: 20.sp),
  //         ),
  //       ));
  // }
  //endregion

  ///打印张数
  void _addPrintCount(List<Widget> widgetList) {
    if (widget.selectFieldKey != PrintFieldConfig.printCount) {
      return;
    }
    PrintGeneralConfig printGeneralConfig = PrintGeneralConfig(defaultValue: 1);
    if (null != _printConfigInfo &&
        StringUtil.isNotEmpty(_printConfigInfo!.contentConfig)) {
      printGeneralConfig = PrintGeneralConfig.fromMap(
        json.decode(_printConfigInfo!.contentConfig),
      );
    }

    widgetList.add(
      buildSelectWidget(
        PrintFieldConfig.printConfigNameStr[widget.selectFieldKey] ?? "",
        data: PrintFieldConfig.printCountName.values.toList(),
        selectIndex: printGeneralConfig.content - 1,

        ///存储的张数的
        onSelectClick: (index) {
          if (null != _printConfigInfo) {
            setState(() {
              ///选择项从0开始，打印张数从1开始，顺位+1
              printGeneralConfig.content = index + 1;
              _printConfigInfo!.contentConfig = json.encode(
                printGeneralConfig.toJson(),
              );
            });
            widget.onEditChangeBack(_printConfigInfo!);
          }
        },
      ),
    );
  }

  ///商品名称
  void _addPTypeName(List<Widget> widgetList) {
    if (widget.selectFieldKey != PrintFieldConfig.pFullName) {
      return;
    }
    PrintGeneralConfig printGeneralConfig = PrintGeneralConfig();
    if (null != _printConfigInfo &&
        StringUtil.isNotEmpty(_printConfigInfo!.contentConfig)) {
      printGeneralConfig = PrintGeneralConfig.fromMap(
        json.decode(_printConfigInfo!.contentConfig!),
      );
    }

    widgetList.add(
      buildSelectWidget(
        "套餐打印模式",
        data: PrintFieldConfig.comboModeEnumName.values.toList(),
        selectIndex: printGeneralConfig.content,
        onSelectClick: (index) {
          if (null != _printConfigInfo) {
            setState(() {
              printGeneralConfig.content = index;
              _printConfigInfo!.contentConfig = json.encode(
                printGeneralConfig.toJson(),
              );
            });
            widget.onEditChangeBack(_printConfigInfo!);
          }
        },
      ),
    );
  }

  ///编号打印模式
  void _addBillNumber(List<Widget> widgetList) {
    if (widget.selectFieldKey != PrintFieldConfig.billNumber) {
      return;
    }
    PrintGeneralConfig printGeneralConfig = PrintGeneralConfig();
    if (null != _printConfigInfo &&
        StringUtil.isNotEmpty(_printConfigInfo!.contentConfig)) {
      printGeneralConfig = PrintGeneralConfig.fromMap(
        json.decode(_printConfigInfo!.contentConfig!),
      );
    }

    widgetList.add(
      buildSelectWidget(
        "打印模式",
        data: PrintFieldConfig.billNumberModeEnumName.values.toList(),
        selectIndex: printGeneralConfig.content,
        onSelectClick: (index) {
          if (null != _printConfigInfo) {
            setState(() {
              printGeneralConfig.content = index;
              _printConfigInfo!.contentConfig = json.encode(
                printGeneralConfig.toJson(),
              );
            });
            widget.onEditChangeBack(_printConfigInfo!);
          }
        },
      ),
    );
  }

  ///发票二维码说明
  void _addBillInvoiceMemo(List<Widget> widgetList) {
    if (widget.selectFieldKey != PrintFieldConfig.invoiceCode) {
      return;
    }

    PrintGeneralConfig printGeneralConfig = PrintGeneralConfig();
    if (null != _printConfigInfo &&
        StringUtil.isNotEmpty(_printConfigInfo!.contentConfig)) {
      printGeneralConfig = PrintGeneralConfig.fromMap(
        json.decode(_printConfigInfo!.contentConfig!),
      );
    }

    TextEditingController textEditingController = TextEditingController(
      text: printGeneralConfig.text,
    );
    widgetList.add(
      buildItemRow(
        title: "发票二维码说明",
        child: HaloContainer(
          padding: EdgeInsets.all(8.w),
          mainAxisSize: MainAxisSize.max,
          border: Border.all(color: AppColors.borderColor),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
          children: [
            Expanded(
              child: HaloTextField(
                showCounter: true,
                controller: textEditingController,
                fontSize: 20.sp,
                maxLength: 20,
                hintText: "(选填)添加文本内容",
                contentPadding: 0,
                maxLines: 2,
                onChanged: (String text) {
                  printGeneralConfig.text = text;
                  _printConfigInfo!.contentConfig = json.encode(
                    printGeneralConfig.toJson(),
                  );
                  widget.onEditChangeBack(_printConfigInfo!);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

//endregion

//region 网格列表
class GridListView extends StatefulWidget {
  final int dataCount;
  final int columnCount;
  final int columnSplitWidth; //item的行空隙
  final IndexedWidgetBuilder itemBuilder;

  const GridListView({
    Key? key,
    required this.dataCount,
    required this.columnCount,
    required this.itemBuilder,
    this.columnSplitWidth = 10,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return GridListViewsState();
  }
}

class GridListViewsState extends State<GridListView> {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (context1, index) => buildFieldShowConfig(index),
      separatorBuilder: (context, index) => SizedBox(height: 16.h),
      itemCount: (widget.dataCount / widget.columnCount).ceil(),
    );
  }

  ///构建行
  Widget buildFieldShowConfig(int index) {
    List<Widget> items = [];
    for (int i = 0; i < widget.columnCount; i++) {
      if (index * widget.columnCount + i > widget.dataCount - 1) {
        items.add(const Expanded(child: SizedBox()));
      } else {
        items.add(
          Expanded(
            child: widget.itemBuilder(context, index * widget.columnCount + i),
          ),
        );

        if (i != widget.columnCount - 1) {
          items.add(SizedBox(width: widget.columnSplitWidth.w));
        }
      }
    }
    return HaloContainer(mainAxisSize: MainAxisSize.max, children: items);
  }
}

//endregion
