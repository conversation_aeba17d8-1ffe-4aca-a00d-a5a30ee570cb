import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:scan_gun/input/text_input_focus_node.dart';

import '../../../iconfont/icon_font.dart';

///参考[https://juejin.cn/post/7186991958638723132]
///目的是使输入框获取焦点的同时，不弹出系统的软键盘
///使用[WidgetsFlutterBinding]拦截'SystemChannels.textInput.invokeMethod<void>("TextInput.show")'弹出软键盘的事件，
///通过[FocusManager.instance.primaryFocus]获取当前的焦点，如果是需要需要拦截的焦点，则不弹出键盘
class KeyboardHiddenFocusNode extends FocusNode {
  bool keyboardHidden;
  bool keyboardNeverShow = false;

  KeyboardHiddenFocusNode({
    this.keyboardHidden = true,
    this.keyboardNeverShow = false,
    String? debugLabel,
    FocusOnKeyCallback? onKey,
    FocusOnKeyEventCallback? onKeyEvent,
    bool skipTraversal = false,
    bool canRequestFocus = true,
    bool descendantsAreFocusable = true,
    bool descendantsAreTraversable = true,
  }) : super(
            debugLabel: debugLabel,
            onKey: onKey,
            onKeyEvent: onKeyEvent,
            skipTraversal: skipTraversal,
            descendantsAreFocusable: descendantsAreFocusable,
            descendantsAreTraversable: descendantsAreTraversable);
}

// 使用方法：在runApp()方法前初始化
class TextInputBinding extends WidgetsFlutterBinding
    with TextInputBindingMixin {}

mixin TextInputBindingMixin on WidgetsFlutterBinding {
  @override
  BinaryMessenger createBinaryMessenger() {
    return TextInputBinaryMessenger(super.createBinaryMessenger());
  }
}

///拦截软件盘弹出的时间，其他事件则使用系统自己创建的来处理
class TextInputBinaryMessenger extends BinaryMessenger {
  ///系统自己创建的原始BinaryMessenger
  final BinaryMessenger origin;

  TextInputBinaryMessenger(this.origin);

  /// Flutter 的 Framework 层发送信息到 Flutter 引擎，会走这个方法
  /// 通过此方法拦截软键盘弹出的事件
  @override
  Future<ByteData?>? send(String channel, ByteData? message) {
    //只处理textInput的channel
    if (channel == SystemChannels.textInput.name) {
      //解码方法二进制数据，拿到方法名称进行判断
      MethodCall methodCall =
          SystemChannels.textInput.codec.decodeMethodCall(message);
      if (methodCall.method == "TextInput.show") {
        //判断当前输入框焦点是否需要拦截
        FocusNode? primaryFocus = FocusManager.instance.primaryFocus;
        if (primaryFocus is KeyboardHiddenFocusNode) {
          if (primaryFocus.keyboardHidden) {
            return Future.value(
                SystemChannels.textInput.codec.encodeSuccessEnvelope(null));
          }
          //首先，Flutter通过 SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,overlays: []); 来展示全屏
          //这个函数到了Android层面调用的其实是:
          //activity.getWindow().getDecorView().setSystemUiVisibility(
          //  View.SYSTEM_UI_FLAG_LAYOUT_STABLE
          //  | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
          //  | View.SYSTEM_UI_FLAG_FULLSCREEN
          //  | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
          //  | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
          // );
          // 而在奇葩的商米D2上面，无效。。。
          // 所以用:
          //         android.requestWindowFeature(Window.FEATURE_NO_TITLE)
          //         android.window.setFlags(
          //             WindowManager.LayoutParams.FLAG_FULLSCREEN,
          //             WindowManager.LayoutParams.FLAG_FULLSCREEN
          //         )
          // 这个方法会强制全屏，
          // 但是，这之后软键盘弹出和收起时，flutter中 WidgetBinding 的 WidgetsBindingObserver.didChangeMetrics()回调在将不会再被调用？
          // 所以将无法监听软键盘弹出和收起，而我们的目的是弹出和收起软键盘后，将拦截软键盘自动弹出(直到手动点击输入框)
          // 所以，这里换一个方式，若当前获得焦点是需要拦截的焦点，则在其弹出软件盘之后，马上改变状态开始拦截
          primaryFocus.keyboardHidden = true;
        }
        if(primaryFocus is TextInputFocusNode){
          return Future.value(
              SystemChannels.textInput.codec.encodeSuccessEnvelope(null));
        }
      }
    }
    return origin.send(channel, message);
  }

  @override
  Future<void> handlePlatformMessage(String channel, ByteData? data,
      PlatformMessageResponseCallback? callback) {
    return origin.handlePlatformMessage(channel, data, callback);
  }

  // // Flutter 引擎 发送信息到 Flutter 的 Framework 层的回调，无需处理
  @override
  void setMessageHandler(String channel, MessageHandler? handler) {
    return origin.setMessageHandler(channel, handler);
  }
}

///通过WidgetsFlutterBinding拦截软键盘弹出的输入框，
///默认得到焦点时，拦截[SystemChannels.textInput.invokeMethod<void>("TextInput.show")]
///当点击输入框时，短暂取消拦截，弹出键盘
///监听系统软键盘弹出/收起，当触发时，开始拦截软键盘弹出，做到点击输入框时弹出软键盘，但是拦截后续弹出事件(如果需要弹出软键盘，则需要再次点击输入框)
class KeyboardHiddenTextField extends StatefulWidget {
  ///输入框的点击事件，在state中默认有一个实现
  final GestureTapCallback? onTap;

  ///焦点，通过修改[focusNode.keyboardHidden]来确定是否拦截软键盘弹出事件
  final KeyboardHiddenFocusNode? focusNode;

  ///提示文字
  final String? hint;

  ///文字样式
  final TextStyle? style;

  ///[TextField]的controller
  final TextEditingController? controller;

  ///[TextField]的回车键回调
  final ValueChanged<String>? onSubmitted;

  ///用于想要输入框点击弹出键盘时，进行其他操作
  ///同时若返回值为false，则state中[onTap]的默认实现中将不会弹出软键盘（被拦截）
  ///需要注意，若外部传入的onTap不为空，则此字段无效(只会在[onTap]默认实现中被使用)
  final ValueGetter<bool>? onTapBefore;

  ///当搜索时清空框内字符串，用来连续扫码
  final bool cleanTextWhenSearch;

  ///是否展示清除按钮
  final bool isShowClean;

  final TextInputType? keyboardType;

  final List<TextInputFormatter>? inputFormatters;

  final TextAlign textAlign;

  ///是否禁用
  final bool? enabled;

  ///是否自动获取焦点
  final bool autofocus;

  const KeyboardHiddenTextField(
      {Key? key,
      this.onTapBefore,
      this.onTap,
      this.focusNode,
      this.autofocus = true,
      this.hint,
      this.enabled,
      this.style,
      this.keyboardType,
      this.onSubmitted,
      this.controller,
      this.inputFormatters,
      this.textAlign = TextAlign.start,
      this.isShowClean = false,
      this.cleanTextWhenSearch = true})
      : super(key: key);

  @override
  State<KeyboardHiddenTextField> createState() =>
      _KeyboardHiddenTextFieldState();
}

class _KeyboardHiddenTextFieldState extends State<KeyboardHiddenTextField>
    with WidgetsBindingObserver {
  bool _hasFocus = false;
  bool _hasDeleteIcon = false;

  KeyboardHiddenFocusNode? _focusNode;

  TextEditingController? _controller;

  KeyboardHiddenFocusNode get focusNode =>
      widget.focusNode ?? (_focusNode ??= KeyboardHiddenFocusNode());

  TextEditingController get controller =>
      widget.controller ?? (_controller ??= TextEditingController());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    focusNode.addListener(_focusNodeListener);
  }

  Future<void> _focusNodeListener() async {
    if (focusNode.hasFocus) {
      setState(() {
        _hasFocus = true;
      });
    } else {
      setState(() {
        _hasFocus = false;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    focusNode.removeListener(_focusNodeListener);
    WidgetsBinding.instance.removeObserver(this);
  }

  ///应用程序尺寸发生变化会调用此回调，用来监听键盘弹出和收起
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    //键盘收起或弹出之后，开始拦截软键盘弹出
    focusNode.keyboardHidden = true;
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      textAlignVertical: TextAlignVertical.center,
      textAlign: widget.textAlign,
      maxLines: 1,
      enabled: widget.enabled ?? true,
      decoration: InputDecoration(
        isCollapsed: true,
        hintText: widget.hint,
        border: const OutlineInputBorder(
          borderSide: BorderSide.none,
        ),
        suffixIconConstraints: const BoxConstraints(maxHeight: 18),
        suffixIcon: (_hasDeleteIcon && _hasFocus && widget.isShowClean)
            ? GestureDetector(
                onTap: onCancel,
                child: IconFont(
                  IconNames.shanchu_2,
                  size: 18,
                ),
              )
            : null,
      ),
      textInputAction: TextInputAction.search,
      keyboardType: widget.keyboardType,
      style: widget.style,
      focusNode: focusNode,
      autofocus: widget.autofocus,
      controller: controller,
      inputFormatters: widget.inputFormatters,
      onSubmitted: (text) {
        widget.onSubmitted?.call(text);
        if (widget.cleanTextWhenSearch) {
          controller.text = "";
        }
      },
      onChanged: (str) {
        setState(() {
          _hasDeleteIcon = str.isNotEmpty;
        });
      },
      onTapOutside: Platform.isWindows ? (event) {} : null,
      onTap: widget.onTap ??
          () {
            if (widget.onTapBefore?.call() == false ||
                widget.focusNode?.keyboardNeverShow == true) {
              return;
            }
            //点击键盘后，停止拦截软键盘弹出，弹出键盘
            focusNode.keyboardHidden = false;
            SystemChannels.textInput.invokeMethod<void>("TextInput.show");
            focusNode.requestFocus();
          },
    );
  }

  onCancel() {
    // 保证在组件build的第一帧时才去触发取消清空内
    debugPrint("4mounted=$mounted");
    setState(() {
      controller.text = "";
      _hasDeleteIcon = controller.text.isNotEmpty;
    });
  }
}
