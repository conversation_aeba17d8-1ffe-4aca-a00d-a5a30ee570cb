import 'package:flutter/cupertino.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/widget/date_time_picker/date_time_picker.dart';
import 'package:haloui/widget/datepicker/pduration.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';

class DateTimePickUtil {
  // 开始日期选择框：
  // 最小日期限制：最小日期为2000年01-01  00:00：00
  // 最大日期：结束日期
  // 默认值：当前日期的前一个月
  static void showStartDateTimePickerView(BuildContext context,
      {required String currentTime,
      required String endTime,
      required HaloDateTimePickerCallback callback}) {
    DateTime minDateTime = DateUtil.getDateTime("2000-01-01 00:00:00")!;
    PDuration minYearDayDate = PDuration.parse(minDateTime);
    PDuration maxYearDayDate = PDuration.parse(DateUtil.getDateTime(endTime)!);
    DateTime currentDateTime = DateUtil.getDateTime(currentTime)!;

    HaloDateTimePickers.showDateTimePickerView(context,
        minYearDayDate: minYearDayDate,
        maxYearDayDate: maxYearDayDate,
        currentDateTime: currentDateTime,
        onSubmitted: callback);
  }

  // 最小值：不小于开始日期
  // 最大值：开始日期+1个月（最大不超过当天23:59:59）
  // 默认值为当天23:59:59
  static void showEndDateTimePickerView(BuildContext context,
      {required String currentTime,
      required String startTime,
      required HaloDateTimePickerCallback callback}) {
    DateTime minDateTime = DateUtil.getDateTime(startTime)!;
    PDuration minYearDayDate = PDuration.parse(minDateTime);
    DateTime endTimeDate =
        DateUtil.getDateTime(startTime)!.add(const Duration(days: 30));
    PDuration maxYearDayDate = PDuration.parse(endTimeDate);
    DateTime currentDateTime = DateUtil.getDateTime(currentTime)!;

    HaloDateTimePickers.showDateTimePickerView(context,
        minYearDayDate: minYearDayDate,
        maxYearDayDate: maxYearDayDate,
        currentDateTime: currentDateTime,
        onSubmitted: callback);
  }
  //
  // 若开始日期  <结束日期<开始日期+1个月： 则结束日期不变
  // 若结束日期<开始日期： 则 提示开始日期编辑失败还原（理论上通过结束日期限制后不存在这种情况）
  // 若结束日期>开始日期+一个月： 则结束日期= 开始日期+一个月
  static DateTime? resetDateTime(String startTime, String endTime) {
    ///开始日期
    DateTime startDateTime = DateUtil.getDateTime(startTime)!;
    ///结束日期
    DateTime endDateTime = DateUtil.getDateTime(endTime)!;
    ///开始日期+1个月
    DateTime maxEndDateTime =
    DateUtil.getDateTime(startTime)!.add(const Duration(days: 30));
    maxEndDateTime = DateTime(maxEndDateTime.year, maxEndDateTime.month, maxEndDateTime.day, 23, 59, 59);

    if (startDateTime.isBefore(endDateTime) && endDateTime.isBefore(maxEndDateTime)) {
      //不变
      return null;
    }else{
      return maxEndDateTime;
    }
  }
}
