import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/widget/halo_toast.dart';
import 'package:haloui/widget/photo/entity.dart';
import 'package:haloui/widget/photo/photo_picker.dart';
import 'package:haloui/widget/photo/ui/options.dart';
import 'package:path_provider/path_provider.dart';

///
///@ClassName: image_tool
///@Description: 图片视频操作工具
///@Author: tanglan
///@Date: 2024/8/14
class ImageTool {
  ///打开相册选择图片或视频
  ///图片最多5张
  static pickGallery(BuildContext context,
      {bool isVideo = false,
      int maxSelectCount = 1,
      Function(List<String>?)? onSelectBack,
      String? saveDir}) async {
    List<String>? mediaList;
    if (Platform.isWindows) {
      mediaList = await _pickWindows(context, isVideo);
    } else if (Platform.isAndroid) {
      mediaList =
          await _pickAndroid(context, isVideo, maxSelectCount: maxSelectCount);
    }
    if (null != onSelectBack) {
      onSelectBack(mediaList);
    }
  }

  static Future<List<String>> _pickWindows(
      BuildContext context, bool isVideo) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: isVideo ? FileType.video : FileType.image,
      allowMultiple: !isVideo,
      allowedExtensions: isVideo ? ['mp4'] : ['jpg', 'png'],
    );
    if (result != null) {
      if (isVideo) {
        if (result.files.single.size > 5 * 1024 * 1024) {
          if (context.mounted) {
            HaloToast.show(context, msg: "选取的视频过大");
          }
          return [];
        }
      }
      return result.paths
          .where((e) => !TextUtil.isEmpty(e))
          .cast<String>()
          .toList();
    }
    return [];
  }

  static Future<List<String>> _pickAndroid(BuildContext context, bool isVideo,
      {int maxSelectCount = 1, String? saveDir}) async {
    List<AssetEntity>? resultList = await PhotoPicker.pickAsset(
      context: context,
      maxSelected: maxSelectCount,
      rowCount: 5,
      pickType: isVideo ? PickType.onlyVideo : PickType.onlyImage,onPermissionFailed: ( permission) {
        HaloToast.show(context,msg: "请授予图库权限");
    }
    );
    if (null != resultList && resultList.isNotEmpty) {
      if (isVideo) {
        File file = await resultList.first.file;
        if ((await file.length()) > 5 * 1024 * 1024) {
          if (context.mounted) {
            HaloToast.show(context, msg: "选取的视频过大");
          }
          return [];
        }
      }

      List<String> mediaList = await Stream.fromFutures(resultList.map(
          (e) async =>
              await _copyFile((await e.file).path,
                  isVideo: isVideo, saveDir: saveDir) ??
              "")).where((event) => !TextUtil.isEmpty(event)).toList();
      return mediaList;
    }
    return [];
  }

  ///拷贝文件
  static Future<String?> _copyFile(String filePath,
      {isVideo = false, String? saveDir}) async {
    // "/storage/emulated/0/Android/data/packname/files"
    Directory? appDir = await getExternalStorageDirectory();
    if (appDir != null) {
      String path = appDir.path;
      if (StringUtil.isNotEmpty(saveDir)) {
        path = "$path/$saveDir";
      }
      String otypeId = SpTool.getStoreInfo()!.otypeId ?? "default";
      path = "$path/$otypeId/${isVideo ? "video" : "image"}";
      Directory dir = Directory(path);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
      File source = File(filePath);
      File result = await source.copy(dir.path +
          source.path
              .substring(source.path.lastIndexOf("/"), source.path.length));
      return result.path;
    }
    return null;
  }



}
