import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:rational/rational.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/bill/entity/bill_vip_assert_change.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:haloui/widget/halo_dialog.dart';

import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../enum/bill_type.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../entity/bill_sale_bill_detail_dto.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/order_bill_item_entity.dart';
import '../../entity/payment_dto.dart';
import '../../enums/assert_change_type.dart';
import '../../model/bill_model.dart';
import '../../settlement/back_bill_settlement.dart';
import '../../tool/bill_tool.dart';
import '../../tool/goods_tool.dart';
import 'bill_mixin.dart';
import 'select_vip_mixin.dart';

///退货业务mixin
mixin SaleBackMixin<T extends StatefulWidget> on BillMixin<T> {
  OrderBillItem get selectBillItem;

  ///退货原单和之前退货单记录
  late BillSaleBillDetailDto billDetailDto;

  ///资产记录
  VipAssertsBillDtoBean? vipAssertsBillDtoBean;

  @override
  bool get saleBack => true;

  ///当前单据金额和原单金额的比例
  ///这里的金额包含赠金
  ///当原单金额为0，则返回null
  Rational? get totalProportion {
    Decimal originalTotal = getPosBillTotal(billDetailDto.billDetail);
    //当原单金额为0，则无法计算比例
    if (originalTotal == Decimal.zero) return null;
    return (Decimal.tryParse(sumAllFinalTotal) ?? Decimal.zero) / originalTotal;
  }

  @override
  Future<void> loadData() async {
    //拉取一张销售单据,原单退货
    await super.loadData();
    billDetailDto = await getBillDetailDto(selectBillItem);
    if (billDetailDto.vipBillInfo != null) {
      vipInfo = await getVipData();
    }
    //赋值单据交易流水号
    goodsBillDto.payOutNo = billDetailDto.payOutNo;
    BillTool.setSaleBackGoodsBill(goodsBillDto, billDetailDto, selectBillItem);
    //退单需要把原单的业务员赋值
    goodsBillDto.efullname = billDetailDto.billDetail?.efullname;
    goodsBillDto.etypeId = billDetailDto.billDetail?.etypeId;
    setPayment();
    reCalcStatistic();
  }

  ///退货商品发生变动，重新计算
  void onBackGoodsListDataChange(GoodsBillDto goodsBill, bool resetFocus) {
    goodsBillDto = goodsBill;
    setState(() {
      reCalcStatistic();
    });
  }

  void setPayment();

  Future<VipWithLevelAssertsRightsCardDTO?> getVipData() {
    return SelectVipMixin.getVipById(
        context, billDetailDto.vipBillInfo!.vipId!);
  }

  Future<BillSaleBillDetailDto> getBillDetailDto(OrderBillItem bill) {
    return BillModel.getGoodsDetailsAndPreferential(
      context,
      bill.vchcode,
      BillTypeData[BillType.SaleBill]!,
    );
  }

  void calcVipStoreInfo(GoodsBillDto bill);

  ///处理成长值
  void handleGrowth() {
    if (vipAssertsBillDtoBean == null) return;
    //移除积分资产变动，其他方法单独处理
    vipAssertsBillDtoBean!.assertsBillDetailDtoList
        .removeWhere((element) => element.typed == "3");
    AssertsBillDetailDtoListBean? giveGrowth = billDetailDto
        .vipAssertsBillDto?.assertsBillDetailDtoList
        .firstWhereOrNull((element) => element.typed == "3");
    _handleAssertsWithGoodsList(originalAssertsChange: giveGrowth, typed: 3);
  }

  ///处理积分
  void handleScore({bool handleDiscountScore = true}) {
    if (vipAssertsBillDtoBean == null) return;
    //移除积分资产变动，其他方法单独处理
    vipAssertsBillDtoBean!.assertsBillDetailDtoList
        .removeWhere((element) => element.typed == "0");
    //找出原单中的积分变动明细
    //原单抵扣积分
    AssertsBillDetailDtoListBean? discountScore;
    //原单赠送积分
    AssertsBillDetailDtoListBean? giveScore;
    billDetailDto.vipAssertsBillDto?.assertsBillDetailDtoList
        .where((element) => element.typed == "0")
        .forEach((element) {
      num qty = num.tryParse(element.qty ?? "0") ?? 0;
      if (qty > 0) {
        giveScore ??= element;
      } else if (qty < 0) {
        discountScore ??= element;
      }
    });
    _handleGiveScore(giveScore);
    //换货单不返还抵扣积分
    if (handleDiscountScore) {
      _handleDiscountScore(discountScore);
    }
  }

  ///处理赠送积分
  void _handleGiveScore(AssertsBillDetailDtoListBean? giveScore) {
    _handleAssertsWithGoodsList(
      originalAssertsChange: giveScore,
      goodsList: goodsBillDto.inDetail
          .where((goods) => goods.giveVipScore)
          .toList(growable: false),
      originalGoodsList: billDetailDto.billDetail?.outDetail
          .where((goods) => goods.giveVipScore)
          .toList(growable: false),
      typed: 0,
    );
  }

  ///处理抵扣积分
  void _handleDiscountScore(AssertsBillDetailDtoListBean? discountScore) {
    if (StringUtil.isZeroOrEmpty(sumAllFinalTotal)) return;
    Rational scoreProportion = totalProportion ?? Decimal.zero.toRational();
    if (scoreProportion <= Rational.zero) return;
    _handleAssertByProportion(discountScore, scoreProportion, 0);
  }

  ///按照退回商品列表和原单商品列表计算比例，然后返还资产
  void _handleAssertsWithGoodsList({
    required AssertsBillDetailDtoListBean? originalAssertsChange,
    List<GoodsDetailDto>? goodsList,
    List<GoodsDetailDto>? originalGoodsList,
    required int typed,
  }) {
    if (originalAssertsChange == null) return;
    goodsList ??= goodsBillDto.inDetail;
    originalGoodsList ??= billDetailDto.billDetail?.outDetail;
    Decimal originalTotal = getGoodsListTotal(originalGoodsList ?? []);
    if (originalTotal <= Decimal.zero) return;
    Decimal backTotal = getGoodsListTotal(goodsList);
    if (backTotal <= Decimal.zero) return;
    Rational proportion = backTotal / originalTotal;

    _handleAssertByProportion(originalAssertsChange, proportion, typed);
  }

  ///按照比例返还/扣除原单资产
  void _handleAssertByProportion(
      AssertsBillDetailDtoListBean? original, Rational proportion, int typed) {
    if (vipAssertsBillDtoBean == null || original == null) return;
    AssertsBillDetailDtoListBean assertChange = AssertsBillDetailDtoListBean()
      ..typed = typed.toString();
    num originalQty = num.tryParse(original.qty ?? "0") ?? 0;
    bool negative = originalQty < 0;
    originalQty = originalQty.abs();
    int qty = (Decimal.parse(originalQty.toString()).toRational() * proportion)
        .toBigInt()
        .toInt();
    if (qty == 0) return;
    if (negative) {
      qty = -qty;
    }

    assertChange.qty = qty.toString();
    vipAssertsBillDtoBean!.assertsBillDetailDtoList.add(assertChange);
  }

  ///获取商品列表总价(不含套餐明细和数量小于等于0的商品)
  Decimal getGoodsListTotal(Iterable<GoodsDetailDto> goodsList) {
    return goodsList
        .where((goods) => goods.unitQty > 0 && !GoodsTool.isComboDetail(goods))
        .fold<Decimal>(
            Decimal.zero,
            (previousValue, goods) =>
                previousValue +
                MathUtil.addDec(goods.currencyDisedTaxedTotal,
                    goods.currencyGivePreferentialTotal));
  }

  //单据最后一次退，磨平误差
  void handleLastBack(
      PaymentDto? paymentDto,
      AssertsBillDetailDtoListBean storedValue,
      AssertsBillDetailDtoListBean giveStoredValue) {
    //已退本金
    num currencyAtypeTotal = 0;

    // TODO 此处验证有问题是否为换货单
    bool changeBill = false;

    //已退赠金
    num giftTotal = 0;
    for (var element in billDetailDto.backGoodsBillList!) {
      PaymentDto? finishPayment = element.goodsbill!.payment
          .firstWhereOrNull((element) => element.paywayType == 3);
      if (finishPayment != null) {
        currencyAtypeTotal = MathUtil.addDec(currencyAtypeTotal,
                num.parse(finishPayment.currencyAtypeTotal!))
            .toDouble();
      }

      //已退赠金汇总
      giftTotal = element.goodsbill!.currencyGivePreferentialTotal;
      if (element.goodsbill?.vchtype == BillTypeData[BillType.SaleChangeBill] ||
          element.goodsbill?.vchtype == "2200") {
        changeBill = true;
        break;
      }
    }

    //TODO tl 此处验证有问题
    if (!changeBill) {
      if (paymentDto == null) {
        storedValue.qty = "0";
        giveStoredValue.qty = "0";
      } else {
        //退货应扣减，故为负数
        giveStoredValue.qty = (-(SystemConfigTool.doubleSubtractionToDecimal(
                    goodsBillDto.originalCurrencyGivePreferentialTotal,
                    giftTotal,
                    BillDecimalType.TOTAL)
                .abs()))
            .toString();
        storedValue.qty = SystemConfigTool.doubleSubtractionToDecimal(
                num.parse(goodsBillDto.posCurrencyBillTotal),
                num.parse(giveStoredValue.qty ?? "0"),
                BillDecimalType.QTY)
            .toString();
      }
    }
  }

  @override
  Future showSettlementDialog({required GoodsBillDto bill}) {
    GoodsBillDto goodsBill = GoodsBillDto.fromMap(bill.toJson());
    calcVipStoreInfo(goodsBill);
    buildBackAssertsInfo(goodsBill);
    goodsBill.inDetail.removeWhere((element) => element.unitQty == 0);
    return HaloDialog(context,
        dismissOnTouchOutside: true,
        child: BackBillSettlementPage(
          billType: billType,
          goodsBillDto: goodsBill,
          vipInfo: vipInfo,
          vipAssertsBillDtoBean: vipAssertsBillDtoBean,
          backGoodsBillList: billDetailDto.backGoodsBillList,
          successCallback: onPaySuccess,
        )).show().then((value) {
      if (value is String) {
        goodsBillDto.tips = value;
      }
    });
  }

  ///处理退货时的积分变动
  ///不包含本金和赠金，在结算页面处理
  ///[scoreDiscount] 使用的积分数，抵扣积分数
  ///[usedCardList] 会员使用的优惠劵
  ///[giveScore] 赠送积分,退货单退回赠送积分用
  ///[growth] 成长值，退货单退回成长值
  void buildBackAssertsInfo(GoodsBillDto bill) {
    if (vipInfo == null) return;
    bool isRefund = billType == BillType.SaleBackBill ||
        (num.tryParse(sumAllFinalTotal) ?? 0) < 0;
    //处理退货资产变动
    bill.vipAsserts = [];
    for (var assertChange in (vipAssertsBillDtoBean?.assertsBillDetailDtoList ??
        <AssertsBillDetailDtoListBean>[])) {
      num qty = num.tryParse(assertChange.qty ?? "0") ?? 0;
      int typed = int.tryParse(assertChange.typed ?? "-1") ?? -1;
      if (qty == 0) continue;
      String? memo;
      int changeType;
      //处理积分，成长值，卡券
      //储值在结算页面处理
      switch (typed) {
        case 0:
          if (qty > 0) {
            memo = "赠送积分";
            changeType = 8;
          } else {
            memo = "抵扣积分";
            changeType = 7;
          }
          break;
        case 3:
          memo = "成长值";
          changeType = 8;
          break;
        case 4:
          if (!isRefund) continue;
          //fix 99974 pos开单，促销策略赠送了礼品券。但是进行整单退货，赠送的礼品券都没有退回，请确认是否处理
          //只有消费赠送的不退款 促销赠送的要退
          if (assertChange.cardType == 4 && assertChange.changeType == AssertChangeType.CONSUME_USE) continue;
          memo = "优惠券";
          changeType = 7;
          break;
        default:
          continue;
      }
      bill.vipAsserts!.add(BillVipAssertChange(
          qty: -qty,
          typed: typed,
          memo: memo,
          assertId: assertChange.assertId,
          changeType: changeType));
    }
  }

  @override
  void onPaySuccess() {
    Navigator.pop(context);
  }

  ///获取单据金额，包含赠金
  Decimal getPosBillTotal(GoodsBillDto? bill) {
    if (bill == null) return Decimal.zero;
    return MathUtil.add(
        bill.currencyBillTotal, bill.currencyGivePreferentialTotal.toString());
  }
}
