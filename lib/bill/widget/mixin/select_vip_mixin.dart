import 'package:flutter/material.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../../common/style/app_colors.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/performance_capture_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../offline/offline_tool.dart';
import '../../../plugin/secondary_screen_plugin.dart';

import '../../../vip/detail/vip_detail_page.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../vip/entity/page_response.dart';
import '../../../vip/entity/vip_dto.dart';
import '../../../vip/level/svip_select_page.dart';
import '../../../vip/model/vip_model.dart';
import '../../../vip/utils/svip_util.dart';
import '../../../vip/widget/search_vip_with_pagination_dialog.dart';
import '../../../vip/add/vip_add_page.dart';

///开单页面选择会员
mixin SelectVipMixin<T extends StatefulWidget> on State<T> {
  VipWithLevelAssertsRightsCardDTO? vipInfo;

  bool get saleBack => false;

  ///构建底部vip选择
  ///[showNewRights] 是否展示[查看单据促销赠送的优惠]的按钮
  ///[tapNewRights] 点击[查看促销赠送优惠券]按钮的回调
  Widget buildVipInfoView(
    BuildContext context, {
    bool showNewRights = false,
    VoidCallback? tapNewRights,
  }) {
    return HaloContainer(
      margin: EdgeInsets.only(left: 16.w),
      direction: Axis.horizontal,
      mainAxisSize: MainAxisSize.max,
      children: [
        buildAvatar(context),
        Expanded(
          child: GestureDetector(
            onTap: () => onVipClick(context),
            behavior: HitTestBehavior.opaque,
            child: buildVipInfo(
              context,
              showNewRights: showNewRights,
              tapNewRights: tapNewRights,
            ),
          ),
        ),
      ],
    );
  }

  ///头像
  Widget buildAvatar(BuildContext context) {
    return Container(
      height: 60.w,
      width: 60.w,
      margin: EdgeInsets.only(right: 16.w),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Color(vipInfo == null ? 0xFFB3CFFC : 0xFFFFB207),
      ),
      child: IconFont(IconNames.huiyuan, size: 36.w, color: "#ffffff"),
    );
  }

  ///[enableClearVip] 是否允许清除会员，结算界面不允许
  Widget buildVipInfo(
    BuildContext context, {
    bool enableClearVip = true,
    bool showNewRights = false,
    VoidCallback? tapNewRights,
  }) {
    ///无会员
    if (vipInfo == null) {
      return Text(
        saleBack ? "无会员" : "点击选择会员",
        style: TextStyle(
          color: AppColors.normalFontColor,
          fontSize: AppPosSize.firstTitleFontSize.sp,
        ),
      );
    }

    return HaloContainer(
      direction: Axis.vertical,
      children: [
        HaloContainer(
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: HaloContainer(
                children: [
                  Flexible(
                    child: Text(
                      (StringUtil.isNotEmpty(vipInfo?.vip?.name)
                          ? vipInfo?.vip?.name ?? ""
                          : "--"),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: AppPosSize.secondaryTitleFontSize.sp,
                        color: AppColors.normalFontColor,
                      ),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Container(
                    constraints: BoxConstraints(maxWidth: 80.w),
                    padding: EdgeInsets.symmetric(
                      horizontal: 6.w,
                      vertical: 2.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.levelBackgroundColor,
                      borderRadius: BorderRadius.circular(4.w),
                    ),
                    child: Text(
                      vipInfo?.level?.levelName ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: AppColors.levelFontColor,
                        fontSize: AppPosSize.describeFontSize.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (enableClearVip)
              GestureDetector(
                onTap: () => setState(() => cleanVip()),
                behavior: HitTestBehavior.opaque,
                child: Container(
                  width: 56.w,
                  height: 40.h,
                  alignment: Alignment.centerRight,
                  child: IconFont(IconNames.shanchu_1, size: 28.w),
                ),
              ),
          ],
        ),
        //余额和积分
        HaloContainer(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Text(
                "余额:￥${vipInfo?.asserts?.totalMoney}",
                style: TextStyle(
                  fontSize: AppPosSize.contentFontSize.sp,
                  color: AppColors.secondaryFontColor,
                ),
              ),
            ),
            Expanded(
              child: Text(
                "积分:${vipInfo?.asserts?.availableScore ?? 0}",
                style: TextStyle(
                  fontSize: AppPosSize.contentFontSize.sp,
                  color: AppColors.secondaryFontColor,
                ),
              ),
            ),
            Visibility(
              visible: showNewRights,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: tapNewRights,
                child: Container(
                  padding: EdgeInsets.all(4.w),
                  //avatar.width + padding
                  // decoration: BoxDecoration(
                  //   borderRadius: BorderRadius.circular(4.0),
                  //   color: ColorUtil.stringColor("#FFEED8"),
                  // ),
                  child: Text(
                    "新获卡券 >",
                    style: TextStyle(
                      color: ColorUtil.stringColor("#A75E1F"),
                      fontSize: AppPosSize.contentFontSize.sp,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void onVipClick(BuildContext context) async {
    if (OffLineTool().isOfflineLogin) {
      OffLineTool.showOffLineToast(context);
      return;
    }
    if (vipInfo == null) {
      if (saleBack) {
        return;
      }
      //选择会员
      await searchVip(context, jumpToAdd: true).then((vipInfo) {
        if (vipInfo != null) {
          setState(() {
            changeVip(vipInfo);
          });
        }
      });
    } else {
      //会员详情
      if (
          // SpTool.getPermission().memberVipView == true &&
          vipInfo!.vip?.id?.isNotEmpty == true) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => VipDetailPage(vipId: vipInfo!.vip!.id!),
          ),
        ).then((value) => refreshVipInfo(context));
      }
    }
  }

  ///刷新会员信息
  Future<void> refreshVipInfo(BuildContext context) async {
    if (vipInfo?.vip?.id?.isNotEmpty == true) {
      PerformanceCaptureUtil.start(PerformanceTimeName.vipDetail);
      VipWithLevelAssertsRightsCardDTO? vip = await getVipById(
        context,
        vipInfo!.vip!.id!,
      );
      PerformanceCaptureUtil.end(PerformanceTimeName.vipDetail);
      if (vip != null) {
        changeVip(vip);
      }
    }
  }

  ///根据会员id获取会员
  static Future<VipWithLevelAssertsRightsCardDTO?> getVipById(
    BuildContext context,
    String vipId,
  ) {
    return VipModel.getVipWithLevelScoreRightsCardById(
      context,
      vipId,
      SpTool.getStoreInfo()!.otypeId ?? "",
    );
  }

  ///根据手机号搜索会员
  ///[jumpToAdd] 当搜不到会员是否跳转到新增会员界面，默认不弹窗
  static Future<VipWithLevelAssertsRightsCardDTO?> searchVip(
    BuildContext context, {
    jumpToAdd = false,
    String? phone,
    bool fuzzyQuery = true, //是否模糊搜索
    bool showExpiredDialog = true, //是否弹出续费弹窗
  }) async {
    Vip? vip;

    // 如果传入了phone参数，直接根据手机号搜索
    if (phone != null && phone.isNotEmpty) {
      PerformanceCaptureUtil.start(PerformanceTimeName.searchVip);
      PageResponse<Vip>? pageResponse = await VipModel.getVipByCodeNewPage(
          context, phone,
          fuzzyQuery: fuzzyQuery);
      PerformanceCaptureUtil.end(PerformanceTimeName.searchVip);

      List<Vip>? vipList = pageResponse?.list;
      if (vipList != null && vipList.isNotEmpty) {
        if (vipList.length == 1) {
          // 只有一个会员，直接使用
          vip = vipList.first;
        } else {
          // 多个会员，需要用户选择 - 这种情况下还是需要弹窗
          // 但是可以预填搜索内容
          dynamic result = await showDialog(
            context: context,
            builder: (context) => const SearchVipWithPaginationDialog(),
          );

          // 检查是否是新增会员的请求
          if (result is Map && result['action'] == 'add_vip') {
            String searchText = result['searchText'] ?? '';
            if (context.mounted) {
              return await _handleAddVip(context, searchText);
            }
            return null;
          }

          vip = result is Vip ? result : null;
        }
      } else if (jumpToAdd) {
        // 搜索不到会员且允许跳转新增
        return await _handleAddVip(context, phone);
      }
    } else {
      // 没有传入phone参数，使用分页弹窗获取基础会员信息
      dynamic result = await showDialog(
        context: context,
        builder: (context) => const SearchVipWithPaginationDialog(),
      );

      // 检查是否是新增会员的请求
      if (result is Map && result['action'] == 'add_vip') {
        String searchText = result['searchText'] ?? '';
        if (context.mounted) {
          return await _handleAddVip(context, searchText);
        }
        return null;
      }

      vip = result is Vip ? result : null;
    }

    // 统一处理选中的会员
    if (vip != null) {
      // 使用统一的后续处理逻辑（过期检查、续费弹窗等）
      String? vipId = vip.id;
      VipWithLevelAssertsRightsCardDTO? vipAllInfo;
      if (vipId != null && context.mounted) {
        vipAllInfo = await getVipById(context, vipId);
      }
      vip = vipAllInfo?.vip;
      vipId = vip?.id;
      if (showExpiredDialog &&
          vipId?.isNotEmpty == true &&
          isVipExpired(
            vip?.validDate,
            vipType: vipAllInfo?.level?.vipType == true ? 1 : 0,
          )) {
        if (context.mounted) {
          final index = await DialogUtil.showConfirmDialog(
            context,
            title: "该会员已过期",
            content: "是否续费?",
            actionLabels: ["续费", "取消"],
          );
          if (index == 0 && context.mounted) {
            SecondaryScreenPlugin.hide();
            return NavigateUtil.navigateTo(
              context,
              SVipLevelSelectPage(vipId: vipId!),
            ).then((value) {
              SecondaryScreenPlugin.showSecondaryScreenImage();
              if (context.mounted) {
                return getVipById(context, vipId!);
              } else {
                return null;
              }
            });
          }
        }
      }
      return vipAllInfo;
    }
    return null;
  }

  ///清空了会员
  void cleanVip() {
    vipInfo = null;
  }

  ///选择了会员
  void changeVip(VipWithLevelAssertsRightsCardDTO vipInfo) {}

  /// 处理新增会员
  static Future<VipWithLevelAssertsRightsCardDTO?> _handleAddVip(
    BuildContext context,
    String searchText,
  ) async {
    if (!context.mounted) return null;

    bool isNumber = RegExp(r'^\d+$').hasMatch(searchText);
    String? vipId = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VipAddPage(
          phone: isNumber ? searchText : "",
          name: isNumber ? "" : searchText,
        ),
      ),
    );

    if (vipId != null && context.mounted) {
      VipWithLevelAssertsRightsCardDTO? vipInfo =
          await VipModel.getVipWithLevelScoreRightsCardById(
        context,
        vipId,
        SpTool.getStoreInfo()!.otypeId ?? "",
      );

      return vipInfo;
    }
    return null;
  }
}
