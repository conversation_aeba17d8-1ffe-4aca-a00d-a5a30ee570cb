import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';

import '../../bill/entity/base_table_content.dart';
import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/tool/bill_tool.dart';
import '../../bill/tool/decimal_display_helper.dart';
import '../../bill/widget/mixin/promotion_tips_mixin.dart';
import '../../bill/widget/ptype/goods_bind_sn_page.dart';
import '../../bill/widget/ptype/ptype_detail_page.dart';
import '../../common/style/app_color_helper.dart';
import '../../common/style/app_colors.dart';
import '../../common/tool/dialog_util.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/widget/ptype_note_richtext.dart';
import '../../entity/system/column_config.dart';
import '../../enum/bill_type.dart';
import '../../iconfont/icon_font.dart';
import '../../settting/widget/column_config_page.dart';
import '../../widgets/halo_pos_label.dart';
import '../entity/ptype/ptype_serial_no_dto.dart';
import '../model/ptype_model.dart';
import '../tool/bill_goods_util.dart';
import '../tool/goods_tool.dart';
import '../tool/index_scroll_controller.dart';
import '../tool/promotion/manual_price.dart';
import '../tool/promotion/price.dart';
import '../tool/promotion/promotion.dart';
import 'base_table_widget.dart';

///开单页面商品列表
class BillEditSaleTable extends StatefulWidget {
  ///单据
  final GoodsBillDto goodsBillDto;

  ///商品发生变更时的回调
  ///[resetFocus] 是否请求搜索框的焦点
  final void Function(GoodsBillDto goodsBill, bool resetFocus)
  dataChangeCallBack;

  ///选择促销赠品的回调
  final ValueSetter<String>? onSelectPromotionGift;

  ///单据类型，销售或退货
  final BillType billType;

  ///是否是按商品退货
  final bool backByGoods;

  final IndexScrollerController indexScrollerController;

  const BillEditSaleTable({
    Key? key,
    required this.billType,
    required this.dataChangeCallBack,
    required this.goodsBillDto,
    required this.indexScrollerController,
    this.onSelectPromotionGift,
    this.backByGoods = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return BillEditSaleTableState();
  }
}

class BillEditSaleTableState extends State<BillEditSaleTable>
    with PromotionTipsMixin {
  ///表格宽度
  double get tableWidth => ScreenUtil().screenWidth;

  late List<GoodsDetailDto> dataSource;
  List<ColumnConfig> showConfigColumns = [];
  late GoodsBillDto goodsBillDto;

  GoodsDetailDto? currentGoods;

  @override
  void initState() {
    super.initState();
    goodsBillDto = widget.goodsBillDto;
    dataSource = BillTool.getGoodsDetails(goodsBillDto, widget.billType);
    setShowConfigColumns();
  }

  @override
  void didUpdateWidget(covariant BillEditSaleTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    goodsBillDto = widget.goodsBillDto;
    dataSource = BillTool.getGoodsDetails(goodsBillDto, widget.billType);
    setShowConfigColumns();
  }

  void setShowConfigColumns() {
    switch (widget.billType) {
      case BillType.SaleBackBill:
        showConfigColumns =
            SpTool.getBillingColumnConfigSaleBack()!
                .where((element) => element.isShow && element.title != "全部")
                .toList();
        break;
      default:
        // 先获取所有可见的列
        showConfigColumns =
            SpTool.getBillingColumnConfigSale()!
                .where((element) => element.isShow && element.title != "全部")
                .toList();

        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget result = Container(
      alignment: Alignment.topLeft,
      child: BaseTableWidget(
        indexScrollerController: widget.indexScrollerController,
        titleWidgetList: buildTitleWidget(),
        columnWidthList: buildColumnWidth(),
        baseTableContentList: _buildAllContentWidget(),
        tapRowCallback: (index) {
          showItemDetail(index);
          setState(() => currentGoods = dataSource[index]);
        },
      ),
    );
    return result;
  }

  //region 构造外面UI
  ///标题行
  List<Widget> buildTitleWidget() {
    List<Widget> titleWidget = [];
    for (int i = 0; i < showConfigColumns.length; i++) {
      Widget child;
      ColumnConfig item = showConfigColumns[i];
      if (i == showConfigColumns.length - 1) {
        child = GestureDetector(
          onTap: () {
            DialogUtil.showAlertDialog(
              context,
              child: ColumnConfigPages(
                columnPagesType:
                    widget.billType == BillType.SaleBackBill
                        ? ColumnPagesType.ColumnPagesBillingSaleBack
                        : ColumnPagesType.ColumnPagesBillingSale,
                changed: (value) {
                  setState(() => setShowConfigColumns());
                },
              ),
            );
          },
          child: HaloContainer(
            children: [
              Expanded(
                child: Container(
                  alignment: BillTool.getTextAlignment(item),
                  child: HaloPosLabel(
                    item.title,
                    textStyle: TextStyle(
                      color: AppColorHelper(context).getTitleBoldTextColor(),
                      fontSize: 20.sp,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 10.w),
              Container(
                alignment: Alignment.center,
                child: IconFont(
                  IconNames.gengduo,
                  size: 28.w,
                  color: "#555555",
                ),
              ),
            ],
          ),
        );
      } else {
        child = HaloPosLabel(
          item.title,
          textStyle: TextStyle(
            color: AppColorHelper(context).getTitleBoldTextColor(),
            fontSize: 20.sp,
          ),
        );
      }
      titleWidget.add(
        Container(
          alignment: BillTool.getTextAlignment(item),
          height: 80.w,
          padding: EdgeInsets.only(left: 30.w, right: 20.w),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            border: Border(
              bottom: BorderSide(
                color: AppColors.dividerColor,
                style: BorderStyle.solid,
                width: 1.h,
              ),
            ), //边框
          ),
          child: child,
        ),
      );
    }

    return titleWidget;
  }

  ///计算列宽
  List<ColumnWidth> buildColumnWidth() {
    List<ColumnWidth> columnWidths = [];

    double sizeWith = tableWidth;
    num newScale = getSumColumnFlexTotal();
    for (ColumnConfig billColumn in showConfigColumns) {
      ColumnWidth columnWidth = ColumnWidth();
      columnWidth.isShow = true;
      columnWidth.width =
          (columnWidthScale[billColumn.type]! / newScale) * sizeWith;
      columnWidths.add(columnWidth);
    }
    return columnWidths;
  }

  ///表格
  List<BaseTableContent> _buildAllContentWidget() {
    return dataSource.map((e) => buildContentWidget(e)).toList();
  }

  ///计算每个列相对宽度之和
  num getSumColumnFlexTotal() {
    num sumNum = 0;
    for (ColumnConfig item in showConfigColumns) {
      sumNum += columnWidthScale[item.type]!;
    }
    return sumNum;
  }

  //endregion

  //region  构建数据行组件

  Widget buildComboDetailCell(
    GoodsDetailDto goods,
    ColumnConfig columnConfig,
    BaseTableContentConfig config,
  ) {
    Widget child;
    //构造配置列
    switch (columnConfig.type) {
      case ColumnType.orderNumber:
        // 套餐明细行不显示序号
        child = buildLabel("");
        break;
      case ColumnType.pName:
        child = buildName(goods, config);
        break;
      case ColumnType.number:
        child = buildCount(goods);
        break;
      case ColumnType.unit:
        child = buildLabel(goods.unitName);
        break;
      case ColumnType.price:
        child = buildLabel(goods.currencyPrice.toString());
        break;
      case ColumnType.taxRate:
        child = buildLabel(goods.taxRate.toString());
        break;
      case ColumnType.taxTotal:
        child = buildLabel(goods.currencyTaxTotal.toString());
        break;
      default:
        child = Container();
        break;
    }
    return child;
  }

  Widget buildGoodsCell(
    GoodsDetailDto goods,
    ColumnConfig columnConfig,
    BaseTableContentConfig config,
  ) {
    Widget child;
    //构造配置列
    switch (columnConfig.type) {
      case ColumnType.orderNumber:
        // 计算非套餐明细的主商品序号
        int orderNumber = 1;
        for (GoodsDetailDto item in dataSource) {
          // 只有当前商品之前的主商品行才计入序号
          if (item == goods) {
            break;
          }
          // 只计算非套餐明细的主商品行
          if (!GoodsTool.isComboDetail(item)) {
            orderNumber++;
          }
        }
        child = buildLabel(
          orderNumber.toString(),
          alignment: Alignment.bottomLeft,
        );
        break;
      case ColumnType.userCode:
        child = buildLabel(
          goods.pUserCode ?? "",
          alignment: Alignment.bottomLeft,
        );
        break;
      case ColumnType.barCode:
        child = buildBarCode(goods);
        break;
      case ColumnType.pName:
        child = buildName(goods, config);
        break;
      case ColumnType.number:
        child = buildCount(goods);
        break;
      case ColumnType.unit:
        child = buildLabel(goods.unitName);
        break;
      case ColumnType.price:
        child = buildLabel(goods.currencyPrice.toString());
        break;
      case ColumnType.stockQty:
        //库存显示条件
        String stockQty;
        if (!BillTool.checkPtypeDetailEnable(goods) ||
            goods.comboRow ||
            goods.pcategory != 0) {
          stockQty = "";
        } else {
          stockQty = BillTool.getStockQty(
            goods.stockQty,
            goods.unitRate,
            goods.pcategory,
          );
        }
        child = buildLabel(stockQty);
        break;
      case ColumnType.discount:
        child = buildDiscountCell(goods);
        break;
      case ColumnType.reducedPrice:
        child = buildReducePriceCell(goods);
        break;
      case ColumnType.currentPrice:
        child = buildCurrentPriceCell(goods);
        break;
      case ColumnType.subtotalPrice:
        child = buildTotalCell(goods);
        break;
      case ColumnType.taxRate:
        child = buildLabel(goods.comboRow ? "" : goods.taxRate.toString());
        break;
      case ColumnType.taxTotal:
        child = buildLabel(goods.currencyTaxTotal.toString());
        break;
      default:
        child = Container();
        break;
    }
    return child;
  }

  ///折扣列
  Widget buildDiscountCell(GoodsDetailDto goods) {
    String discount = DecimalDisplayHelper.getDiscountFixed(
      goods.discount.toString(),
    );
    return buildLabel(discount);
  }

  ///最终优惠列
  Widget buildReducePriceCell(GoodsDetailDto goods) =>
      buildLabel(goods.currencyPreferentialTotal.toString());

  ///现价列
  Widget buildCurrentPriceCell(GoodsDetailDto goods) =>
      buildLabel(goods.currencyDisedTaxedPrice.toString());

  ///小计列
  Widget buildTotalCell(GoodsDetailDto goods) => buildLabel(
    goods.currencyDisedTaxedTotal.toString(),
    color: const Color(0xFFF0642E),
  );

  ///单独行和配置
  BaseTableContent buildContentWidget(GoodsDetailDto goods) {
    BaseTableContent content = BaseTableContent();
    final bool isComboDetail = GoodsTool.isComboDetail(goods);
    if (isComboDetail) {
      content.config
        ..bottomLine = false
        ..rowHeight = goods.visible ? 60 : 0
        ..nameLeftPadding = goods.gift ? 4 : 40
        ..nameDetailLeftPadding = 40;
    }
    for (int i = 0; i < showConfigColumns.length; i++) {
      ColumnConfig columnConfig = showConfigColumns[i];
      Widget child =
          isComboDetail
              ? buildComboDetailCell(goods, columnConfig, content.config)
              : buildGoodsCell(goods, columnConfig, content.config);
      if (i == showConfigColumns.length - 1) {
        //满件赠满额赠赠品不允许直接删除
        // bool showDelete = !goodsDetailDto.promotionGift ||
        //     goodsDetailDto.promotionType == PromotionType.halfPrice.value;
        bool showDelete =
            !isComboDetail && !BillGoodsUtil.isPromotionGift(goods, true);
        Widget deleteWidget;
        if (showDelete) {
          deleteWidget = GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: Align(
              alignment: Alignment.bottomRight,
              child: IconFont(IconNames.shanchu_1, color: "#F0642E"),
            ),
            onTap: () => countChange(goods, 0),
          );
        } else {
          deleteWidget = Container();
        }
        deleteWidget = Container(
          width: 30.w,
          margin: EdgeInsets.only(bottom: 26.h),
          child: deleteWidget,
        );
        child = HaloContainer(
          children: [
            Expanded(child: child),
            SizedBox(width: 10.w),
            deleteWidget,
          ],
        );
      }
      content.contentWidgetList.add(
        Container(
          color: goods == currentGoods ? const Color(0x75C0D7FF) : null,
          alignment: BillTool.getTextAlignment(columnConfig),
          height: 80.w,
          padding: EdgeInsets.only(left: 30.w, right: 20.w),
          child: child,
        ),
      );
    }
    return content;
  }

  ///条码
  Widget buildBarCode(GoodsDetailDto goodsDetailDto) {
    return Container(
      alignment: Alignment.bottomCenter,
      padding: EdgeInsets.only(bottom: 26.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: HaloPosLabel(
              goodsDetailDto.fullbarcode,
              textAlign: TextAlign.left,
              textStyle: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 18.w,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///商品名称
  Widget buildName(
    GoodsDetailDto goodsDetailDto,
    BaseTableContentConfig config,
  ) {
    List<PromotionHints> goodsHints = goodsDetailDto.promotionHints ?? [];
    config.rowHeight = config.rowHeight + goodsHints.length * 30;

    // List<PromotionHints> billHints = [];
    // int index = dataSource.indexOf(goodsDetailDto);
    // if (index == 0) {
    //   billHints = goodsBillDto.promotionHints ?? [];
    //   config.rowHeight = config.rowHeight + billHints.length * 40;
    // }

    if (goodsDetailDto.comboRow) {
      ///套餐行
      return HaloContainer(
        direction: Axis.vertical,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // if (index == 0)
          //   _buildPromotionHints(billHints, goodsDetailDto, config),
          _buildPromotionHints(goodsHints, goodsDetailDto, config),
          _buildComboName(goodsDetailDto),
        ],
      );
    } else {
      ///商品明细和套餐明细行
      return HaloContainer(
        direction: Axis.vertical,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // if (index == 0)
          //   _buildPromotionHints(billHints, goodsDetailDto, config),
          _buildPromotionHints(goodsHints, goodsDetailDto, config),
          _buildGoodsName(goodsDetailDto, config),
          Flexible(child: _buildGoodsNameDetail(goodsDetailDto, config)),
        ],
      );
    }
  }

  Widget _buildPromotionHints(goodsHints, goodsDetailDto, config) {
    return buildPromotionHints(
      goodsHints,
      goodsDetailDto,
      config,
      leftPadding: 0,
    );
  }

  ///套餐行名称
  Widget _buildComboName(GoodsDetailDto goodsDetailDto) {
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Expanded(child: PtypeNoteRichText(goodsDetailDto: goodsDetailDto)),
            GestureDetector(
              child: HaloPosLabel(
                goodsDetailDto.visible ? "收起" : "展开",
                textStyle: TextStyle(
                  color: const Color(0xFF4679FC),
                  fontSize: 18.w,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                _showComboRowDetail(goodsDetailDto);
              },
            ),
          ],
        ),
        // if (detailChildren.length > 0) detailWidget
      ],
    );
  }

  ///商品行和套餐明细行
  Widget _buildGoodsName(
    GoodsDetailDto goodsDetailDto,
    BaseTableContentConfig config,
  ) {
    return Row(
      children: [
        Flexible(
          flex: 1,
          child: PtypeNoteRichText(
            goodsDetailDto: goodsDetailDto,
            showProp: true,
          ),
        ),
        //促销赠品编辑按钮
        Visibility(
          visible: BillTool.showGiftEditButton(goodsDetailDto, widget.billType),
          child: GestureDetector(
            child: Container(
              width: 28.w,
              height: 28.w,
              margin: EdgeInsets.only(left: 8.w, right: 8.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black, width: 1.w),
                color: Colors.white,
                borderRadius: const BorderRadius.all(Radius.circular(3)),
              ),
              child: Icon(Icons.edit, size: 16.sp, color: Colors.black),
            ),
            onTap: () {
              widget.onSelectPromotionGift?.call(goodsDetailDto.promotionId);
            },
          ),
        ),
      ],
    );
  }

  ///规格、型号、序列号、批次
  Widget _buildGoodsNameDetail(
    GoodsDetailDto goodsDetailDto,
    BaseTableContentConfig config,
  ) {
    String standard = goodsDetailDto.standard;
    String ptypetype = goodsDetailDto.ptypetype;
    String batchNo = goodsDetailDto.batchNo;
    String detailString = "";
    List<InlineSpan> detailChildren = [];
    if (standard.isNotEmpty) {
      detailString = "$detailString$standard;";
    }
    if (ptypetype.isNotEmpty) {
      detailString = "$detailString$ptypetype;";
    }
    if (goodsDetailDto.batchenabled) {
      detailString = "$detailString批:$batchNo;";
    }

    ///规格、型号、批次
    if (detailString.isNotEmpty) {
      detailChildren.add(
        TextSpan(
          text: detailString,
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColorHelper(context).getNormalTitleTextColor(),
          ),
        ),
      );
    }

    ///序列号
    if (BillTool.snDetailRow(goodsDetailDto)) {
      detailChildren.add(
        TextSpan(
          text: "序:",
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColorHelper(context).getNormalTitleTextColor(),
          ),
        ),
      );

      for (PtypeSerialNoDto serialNoDto in goodsDetailDto.serialNoList) {
        if (serialNoDto.isInTheStock ||
            widget.billType == BillType.SaleBackBill) {
          detailChildren.add(
            TextSpan(
              text: "${serialNoDto.snno!};",
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColorHelper(context).getNormalTitleTextColor(),
              ),
            ),
          );
        } else {
          detailChildren.add(
            TextSpan(
              text: "${serialNoDto.snno!};",
              style: TextStyle(fontSize: 16.sp, color: Colors.red),
            ),
          );
        }
      }
    }
    if (detailChildren.isNotEmpty) {
      return Container(
        margin: EdgeInsets.only(
          top: 8.w,
          left: config.nameDetailLeftPadding.w,
          bottom: 1.h,
        ),
        //这里底部给1，是因为周律君的实现方式，会覆盖分割线
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: RichText(
                text: TextSpan(children: detailChildren),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Visibility(
              visible: BillTool.snDetailRow(goodsDetailDto),
              child: GestureDetector(
                child: Icon(Icons.edit, size: 16.sp, color: Colors.grey),
                onTap: () {
                  _showSnDetail(goodsDetailDto);
                },
              ),
            ),
          ],
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  ///获取单个商品最大数量
  num getGoodsMaxCount(GoodsDetailDto goods) =>
      SpTool.getSystemConfig().sysGlobalDecimalMax;

  ///数量
  Widget buildCount(GoodsDetailDto goodsDetailDto) {
    //套餐明细商品和赠品无法调整数量
    if (!BillTool.checkPtypeDetailEnable(goodsDetailDto)) {
      return Container(
        padding: EdgeInsets.only(bottom: 26.h),
        alignment: Alignment.bottomCenter,
        child: HaloPosLabel(
          goodsDetailDto.unitQty.toString(),
          textStyle: const TextStyle(color: Color(0xFFDB0E0E)),
        ),
      );
    } else {
      num countMax = getGoodsMaxCount(goodsDetailDto);
      FocusNode focusNode = FocusNode();
      focusNode.addListener(() {
        if (!focusNode.hasFocus) {
          if (goodsDetailDto.unitQty <= 0) {
            doDelete(goodsDetailDto);
          }
        }
      });
      return Container(
        padding: EdgeInsets.only(bottom: 15.h),
        alignment: Alignment.bottomCenter,
        child: HaloCountStepper(
          focusNode: focusNode,
          decimalCount: SpTool.getSystemConfig().sysDigitalQty,
          notifier: HaloCountStepperNotifier(
            max: (countMax < 0 ? 0 : countMax).toDouble(),
            defaultValue: goodsDetailDto.unitQty,
          ),
          enable: true,
          inputBackgroundColor: Colors.white,
          textColor: Colors.black,
          inputFontWeight: FontWeight.w600,
          inputMinWidth: 90.w,
          inputFontSize: 22.sp,
          borderRadius: BorderRadius.circular(6.w),
          border: Border.all(color: AppColors.txtBorderColor, width: 1),
          textColorDiy: true,
          addIcon: Container(
            height: 40.w,
            width: 40.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.w),
              border: Border.all(color: AppColors.txtBorderColor, width: 1),
            ),
            child: Text(
              "+",
              style: TextStyle(color: const Color(0xFF606060), fontSize: 24.sp),
            ),
          ),
          subIcon: Container(
            height: 40.w,
            width: 40.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.w),
              border: Border.all(color: AppColors.txtBorderColor, width: 1),
            ),
            child: Text(
              "-",
              style: TextStyle(color: const Color(0xFF606060), fontSize: 30.sp),
            ),
          ),
          onAfterSubClick: (value) {
            countChange(goodsDetailDto, value, delete: true);
          },
          onAfterAddClick: (value) {
            countChange(goodsDetailDto, value, delete: false);
          },
          onChanged: (value) {
            countChange(
              goodsDetailDto,
              value,
              delete: false,
              requestSearchBarFocus: false,
            );
          },
          onCompleted: (value) {
            countChange(goodsDetailDto, value, requestSearchBarFocus: true);
          },
        ),
      );
    }
  }

  buildLabel(String textString, {Color? color, Alignment? alignment}) {
    return Container(
      padding: EdgeInsets.only(bottom: 26.h),
      alignment: alignment ?? Alignment.bottomRight,
      child: HaloPosLabel(
        textString,
        textStyle: TextStyle(
          color: color ?? AppColorHelper(context).getTitleBoldTextColor(),
          fontSize: 18.w,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  //endregion

  //region action

  _showSnDetail(GoodsDetailDto goodsDetailDto) {
    List<String> existSN = [];
    //如果是批次序列号，需要先校验是否录入了批次号
    if (goodsDetailDto.batchenabled) {
      if (StringUtil.isEmpty(goodsDetailDto.batchNo)) {
        HaloToast.show(context, msg: "请先选择批次号");
        return;
      }
    }
    for (GoodsDetailDto goods in dataSource) {
      if (BillTool.snDetailRow(goods)) {
        for (PtypeSerialNoDto serialNoDto in goods.serialNoList) {
          existSN.add(serialNoDto.snno!);
        }
      }
    }
    showDialog<GoodsDetailDto>(
          context: context,
          builder:
              (context) => GoodsBindSnPage(
                goods: GoodsDetailDto.fromMap(goodsDetailDto.toJson()),
                existSN: existSN,
                limitMaxCount: !BillTool.checkPtypeDetailEnable(goodsDetailDto),
                billType: widget.billType,
              ),
        )
        .then((goods) {
          if (goods == null) {
            return;
          }
          //序列号列表为空，不修改数量，但是清空序列号
          if (goods.serialNoList.isEmpty != false) {
            goodsDetailDto.serialNoList = [];
            countChange(goodsDetailDto, goodsDetailDto.unitQty);
          } else {
            goodsDetailDto.serialNoList = goods.serialNoList;
            if (goodsDetailDto.unitQty < goods.serialNoList.length) {
              goodsDetailDto.unitQty = goodsDetailDto.serialNoList.length;
            }
            countChange(goodsDetailDto, goodsDetailDto.unitQty);
          }
        })
        .onError((error, stackTrace) => null);
  }

  _showComboRowDetail(GoodsDetailDto goodsDetailDto) {
    setState(() {
      BillTool.showComboRowDetail(goodsDetailDto, dataSource);
    });
  }

  ///弹出商品详情界面
  void showItemDetail(int index) {
    GoodsDetailDto goods = dataSource[index];
    HaloDialog(context,
        dismissOnTouchOutside: true,
        barrierColor: Colors.transparent,
        child: PtypeDetailPage(
          goods,
          goodsBillDto,
          billType: widget.billType,
          backByGoods: widget.backByGoods,
          maxCount: getGoodsMaxCount(goods),
          enable: BillTool.checkPtypeDetailEnable(goods),
          valueChanged: (newGoods, editRetailPrice) {
            //零售价发生了改变
            if (editRetailPrice) {
              newGoods.memo = "手动修改零售价";
              DialogUtil.showConfirmDialog(context,
                  content: "是否想修改后的零售价同步到零售店铺价格本？",
                  actionLabels: ["取消", "确定"],
                  confirmCallback: () =>
                      PtypeModel.addOrUpdateOtypePrice(context, newGoods));
            } else {
              if (newGoods.memo == "手动修改零售价") {
                newGoods.memo = "";
              }
            }
            dataSource[index] = newGoods;
            if (newGoods.unitQty == 0) {
              doDelete(newGoods);
            } else {
              //手工改价商品，重新计算手工改价优惠
              if ((goods.manualPrice || newGoods.manualPrice) &&
                  !GoodsTool.isComboDetail(newGoods)) {
                ManualPriceUtil.setManualPrice(newGoods,
                    comboDetails: GoodsTool.getComboDetailsFromGoodsList(
                        dataSource, newGoods));
              }
            }
            widget.dataChangeCallBack(goodsBillDto, true);
          },
        )).show();
  }

  ///数量发生变化
  ///[delete] 当商品数量为0时是否删除，目前当商品数量编辑框发生改变时，不删除，直到焦点发生改变
  void countChange(
    GoodsDetailDto goods,
    num value, {
    bool delete = true,
    bool requestSearchBarFocus = true,
  }) {
    //因为0会删除
    if (BillTool.snDetailRow(goods) &&
        value != 0 &&
        goods.serialNoList.length > value) {
      HaloToast.show(context, msg: "商品数量不能小于序列号数量");
      value = goods.serialNoList.length;
    }
    goods.unitQty = value;
    if (!GoodsTool.isComboDetail(goods) && value > 0) {
      //手工改价商品，重新计算手工改价优惠
      if (goods.manualPrice) {
        ManualPriceUtil.onQtyChange(
          goods,
          value,
          comboDetails: GoodsTool.getComboDetailsFromGoodsList(
            dataSource,
            goods,
          ),
        );
      } else {
        GoodsQtyUtil.onQtyChange(goods, value);
      }
    }
    if (goods.unitQty <= 0 && delete) {
      doDelete(goods);
    } else {
      widget.dataChangeCallBack(goodsBillDto, requestSearchBarFocus);
    }
  }

  ///删除单行
  void doDelete(GoodsDetailDto goods) {
    BillTool.deleteGoods(goods, dataSource);
    widget.dataChangeCallBack(goodsBillDto, true);
  }

  //endregion
}
