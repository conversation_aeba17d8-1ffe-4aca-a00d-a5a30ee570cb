import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/bill/bill/goods_back.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_pos/offline/offline_tool.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/datepicker/pduration.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';

import '../../../bill/entity/bill_list_requset_dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/order_bill_item_entity.dart';
import '../../../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/tool/scan_tool.dart';
import '../../../bill/widget/mixin/select_vip_mixin.dart';
import '../../../bill/widget/ptype/goods_and_combo_select_list_page.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/style/app_pos_size.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../common/widget/datetime_filter.dart';
import '../../../enum/bill_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../tool/promotion/card.dart';

///退货选择单据弹窗
class SaleBillToSaleBackBillDialog extends StatefulWidget {
  final BillType billType;
  final Function(OrderBillItem vchcode)? submitCallback;

  const SaleBillToSaleBackBillDialog(
      {Key? key, this.billType = BillType.SaleBackBill, this.submitCallback})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SaleBillToSaleBackBillDialogState();
  }
}

class _SaleBillToSaleBackBillDialogState
    extends State<SaleBillToSaleBackBillDialog> with DateTimeFilterMixin {
  //记录进入的当前时间 避免每次获取当前时间 导致限制最大最小时间不准确
  DateTime beforeTime = DateTime.now().add(const Duration(days: -30));
  DateTime afterTime = DateTime.now();

  List<OrderBillItem> dataSource = [];

  int pageIndex = 1;
  int pageSize = 50;
  bool loadMore = true;
  ScrollController scrollController = ScrollController();

  TextEditingController textBillNumberController = TextEditingController();

  VipWithLevelAssertsRightsCardDTO? vipInfo;
  GoodsDetailDto? goodsDetailDto;

  ///按商品退货权限
  bool backGoodsPermission =
      SpTool.getPermission().shopsalesalesettingbackGoods ?? false;

  //region init data
  @override
  void initState() {
    super.initState();
    loadData();
  }

  Future loadData() {
    return BillModel.orderBillAllList(context, getRequestParams())
        .then((value) {
      setState(() {
        dataSource.addAll(value);
        pageIndex++;
        if (value.length < 50) {
          loadMore = false;
        }
      });
    });
  }

  Map<String, dynamic> getRequestParams() {
    BillCoreListRequestDto queryParams = BillCoreListRequestDto()
      ..startTime = formatDateStringToUtc(textStartTimeController.text)
      ..endTime = formatDateStringToUtc(textEndTimeController.text);
    Map<String, dynamic> data = {};
    data["pageIndex"] = pageIndex;
    data["pageSize"] = pageSize;
    queryParams.billNumber = textBillNumberController.text;
    queryParams.postState = 8;
    queryParams.saleOrbuy = 2;
    queryParams.paymentType = 0;
    queryParams.payState = 1;
    queryParams.excludeBackCompletedBill = true;
    queryParams.invoiceType = 0;
    queryParams.phone = vipInfo?.vip?.phone;
    queryParams.goodsId = goodsDetailDto?.comboRow == true
        ? goodsDetailDto?.comboId
        : goodsDetailDto?.skuId;
    queryParams.orderBillModel = OrderBillModelData[OrderBillModel.STORE_SALE];
    queryParams.vchtypes = [BillCodeData[BillType.SaleBill]];
    queryParams.businessTypeList = [
      BillBusinessTypeData[BillBusinessType.SaleNormal]
    ];
    queryParams.otypeId = SpTool.getStoreInfo()!.otypeId;
    queryParams.businessType =
        BillBusinessTypeString[BillBusinessType.SaleNormal];
    //收银员
    data["queryParams"] = queryParams.toJson();
    return data;
  }

  //endregion
  //region view
  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
        child: Container(
            clipBehavior: Clip.hardEdge,
            alignment: Alignment.center,
            height: 914.h,
            width: 1220.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.sp)),
              color: Colors.white,
            ),
            child: Scaffold(
              resizeToAvoidBottomInset: false,
              backgroundColor: Colors.white,
              body: Column(
                children: [
                  _buildTopTitle(),
                  _buildDivider(),
                  _buildMidTimeAndBillNumber(),
                  _buildMidListView(),
                  _buildBottomButton(),
                ],
              ),
            )));
  }

  Widget _buildTopTitle() {
    return Container(
        padding: EdgeInsets.only(left: 28.w),
        height: 70.h,
        child: Row(
          children: [
            Expanded(
              child: HaloPosLabel(
                "选择单据",
                textStyle:
                    TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold),
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: Container(
                alignment: Alignment.center,
                width: 80.w,
                height: double.infinity,
                child: IconFont(
                  IconNames.close,
                  color: "#000000",
                  size: 28.h,
                ),
              ),
              onTap: () => _doClose(),
            )
          ],
        ));
  }

  Widget _buildDivider() {
    return Container(
      height: 1.h,
      width: double.infinity,
      color: const Color(0xFFDBDBDB),
    );
  }

  Widget _buildMidTimeAndBillNumber() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 36.sp),
      alignment: Alignment.topLeft,
      height: 160.h,
      width: double.infinity,
      child: Column(
        children: [
          _buildSaleBillTimeQuery(),
          _buildSaleBillNumberQuery(),
        ],
      ),
    );
  }

  Widget _buildSaleBillTimeQuery() {
    return Container(
        margin: EdgeInsets.only(top: 17.sp),
        alignment: Alignment.topLeft,
        child: Row(
          children: [
            SizedBox(
              width: 120.w,
              child: HaloPosLabel(
                "录单时间:",
                textStyle: TextStyle(
                  fontSize: 24.sp,
                ),
              ),
            ),
            buildDateTimeQuery(context,
                border: Border.all(color: const Color(0xFFD9D9D9), width: 1)),
            Padding(
              padding: EdgeInsets.only(left: 12.w),
              child: SizedBox(
                width: 60.w,
                child: HaloPosLabel(
                  "会员",
                  textStyle: TextStyle(
                    fontSize: 24.sp,
                  ),
                ),
              ),
            ),
            _buildChoseView(
                "${vipInfo?.vip?.name ?? ""} ${vipInfo?.vip?.phone ?? ""}",
                onTap: _doChoseVip,
                onClear: _doClearVip)
          ],
        ));
  }

  Widget _buildChoseView(
    String text, {
    Function()? onTap,
    Function()? onClear,
  }) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            alignment: Alignment.centerLeft,
            clipBehavior: Clip.hardEdge,
            width: 220.w,
            height: 56.h,
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFD9D9D9), width: 1),
              borderRadius: BorderRadius.all(Radius.circular(4.sp)),
            ),
            child: Row(
              children: [
                Expanded(
                    child: HaloPosLabel(
                  text.isEmpty ? "" : text,
                  textStyle: TextStyle(
                    fontSize: 24.sp,
                  ),
                  textAlign: TextAlign.left,
                  showFullText: false,
                )),
                GestureDetector(
                  onTap: onClear,
                  child: Container(
                    margin: EdgeInsets.only(left: 10.w),
                    width: 40.w,
                    height: 40.h,
                    alignment: Alignment.center,
                    color: Colors.transparent,
                    child: IconFont(
                      IconNames.shanchu_1,
                      size: 26.sp,
                    ),
                  ),
                )
              ],
            )));
  }

  Widget _buildSaleBillNumberQuery() {
    return Container(
        margin: EdgeInsets.only(top: 17.sp),
        alignment: Alignment.topLeft,
        child: Row(
          children: [
            SizedBox(
              width: 120.w,
              child: HaloPosLabel(
                "单据编号:",
                textStyle: TextStyle(
                    fontSize: 24.sp,
                    color: AppColorHelper(context).getTitleBoldTextColor()),
              ),
            ),
            HaloContainer(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              color: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 12.h),
              width: 340.w,
              height: 56.h,
              border: Border.all(color: const Color(0xFFD9D9D9), width: 1),
              borderRadius: BorderRadius.all(Radius.circular(4.sp)),
              children: [
                Expanded(
                  child: HaloTextField(
                    controller: textBillNumberController,
                    fontSize: 24.sp,
                    contentPadding: 4.h,
                    maxLines: 1,
                    hintText: "输入单据编号查询",
                    onSubmitted: (String text) => _doSearch(),
                  ),
                )
              ],
            ),
            Padding(
              padding: EdgeInsets.only(left: 12.w),
              child: SizedBox(
                width: 60.w,
                child: HaloPosLabel(
                  "商品",
                  textStyle: TextStyle(
                    fontSize: 24.sp,
                  ),
                ),
              ),
            ),
            _buildChoseView(goodsDetailDto?.pFullName ?? "",
                onTap: _doChoseGoodsDetail, onClear: _doClearGoods),
            Padding(
              padding: EdgeInsets.only(left: 12.w),
              child: SizedBox(
                height: 56.h,
                child: HaloButton(
                  buttonType: HaloButtonType.outlinedButton,
                  borderRadius: 6.sp,
                  outLineWidth: 1.sp,
                  text: "查询",
                  fontSize: 25.sp,
                  borderColor: AppColors.accentColor,
                  backgroundColor: Colors.white,
                  textColor: AppColors.accentColor,
                  onPressed: () => _doSearch(),
                ),
              ),
            ),
          ],
        ));
  }

  _buildMidListView() {
    return Expanded(
      child: Column(
        children: [
          _buildSingleListRow(
              billNumber: "单据编号",
              total: "单据金额",
              billDate: "录单时间",
              etypeName: "收银员",
              vipAndPhone: "会员",
              hexColor: "666666",
              height: 60,
              color: ColorUtil.stringColor("F5F5F5"),
              visibleDivider: false),
          Flexible(
            child: HaloList(
              dataSource,
              scrollController: scrollController,
              buildItemContent: (BuildContext context, int index) {
                OrderBillItem item = dataSource[index];
                return _buildSingleListRow(
                    billNumber: item.billNumber ?? "",
                    billNumberDetail: item.memo,
                    total: item.billTotal.toString(),
                    billDate: formatDateStringToLocal(item.billDate),
                    etypeName: item.createEfullname ?? "",
                    vipAndPhone: "${item.vipName ?? ""} ${item.vipPhone ?? ""}",
                    hexColor: "666666",
                    item: item);
              },
              hasMoreData: loadMore,
              onLoadMore: () async {
                await loadData();
              },
            ),
          )
        ],
      ),
    );
  }

  _buildSingleListRow(
      {String billNumber = "",
      String? billNumberDetail,
      String total = "",
      String billDate = "",
      String etypeName = "",
      String vipAndPhone = "",
      String hexColor = "333333",
      double height = 76,
      Color color = Colors.white,
      bool visibleDivider = true,
      OrderBillItem? item}) {
    if (item != null && item.select) {
      color = ColorUtil.stringColor("F4F7FF");
    }
    return GestureDetector(
      child: Container(
          color: color,
          width: double.infinity,
          height: height.h,
          child: Column(
            children: [
              Expanded(
                child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 32.w),
                    child: Row(
                      children: [
                        _buildSingleLabel(billNumber, hexColor,
                            detail: billNumberDetail, width: 370.w, item: item),
                        _buildSingleLabel(total, hexColor, width: 180.w),
                        _buildSingleLabel(billDate, hexColor, width: 250.w),
                        _buildSingleLabel(etypeName, hexColor, width: 130.w),
                        Expanded(
                            child: _buildSingleLabel(vipAndPhone, hexColor))
                      ],
                    )),
              ),
              if (visibleDivider) _buildDivider()
            ],
          )),
      onTap: () {
        _doItem(item);
      },
    );
  }

  _buildSingleLabel(String text, String hexColor,
      {double width = 0, String? detail, OrderBillItem? item}) {
    return HaloContainer(
      width: width,
      children: [
        HaloPosLabel(
          text,
          textStyle: TextStyle(
            color: ColorUtil.stringColor(hexColor),
            fontSize: 22.sp,
          ),
          maxLines: 1,
        ),
        Visibility(
            visible: (item?.orderBillMarkList.any((element) =>
                    element?.baseOrderMarkEnum != BillMarkEnum.NORMAL_BILL)) ??
                false,
            child: HaloContainer(
                margin: EdgeInsets.only(left: 8.w),
                children: getMarkTitle(item?.orderBillMarkList ?? [])))
      ],
    );
  }

  getMarkTitle(List<OrderBillMark?> orderBillMark) {
    //生成标签文本
    List<Widget> list = [];
    for (OrderBillMark? element in orderBillMark) {
      if (null != element) {
        list.add(_buildMarkTitle(element));
      }
    }
    return list;
  }

  _buildMarkTitle(OrderBillMark mark) {
    return Container(
      height: 32.h,
      alignment: Alignment.center,
      padding: EdgeInsets.only(left: 6.w, right: 6.w, top: 4.h, bottom: 6.h),
      decoration: BoxDecoration(
          color: StringUtil.isNotEmpty(mark.backgroundColor)
              ? ColorUtil.stringColor(mark.backgroundColor!).withOpacity(0.2)
              : AppColors.billMarkBgColor,
          borderRadius: BorderRadius.all(Radius.circular(4.sp)),border: Border.all(color: StringUtil.isNotEmpty(mark.borderColor) ? ColorUtil.stringColor(mark.borderColor!) : AppColors.billMarkBgColor,width: 1.w)),
      child: Text(
        mark.markShowName ?? "",
        style: TextStyle(
            color: StringUtil.isNotEmpty(mark.fontColor)
                ? ColorUtil.stringColor(mark.fontColor!)
                : AppColors.billMarkFontColor,
            fontSize: AppPosSize.describeFontSize.sp),
      ),
    );
  }

  _buildBottomButton() {
    return SizedBox(
      width: double.infinity,
      height: 100.h,
      child: Column(
        children: [
          _buildDivider(),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left: 32.w),
                    child: HaloPosLabel("匹配到${dataSource.length}条记录"),
                  ),
                ),
                if (widget.billType == BillType.SaleBackBill &&
                    backGoodsPermission &&
                    !OffLineTool().isOfflineLogin)
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                          style: TextStyle(
                              fontSize: 22.sp,
                              color: AppColorHelper(context)
                                  .getTitleBoldTextColor()),
                          children: [
                            const TextSpan(text: "没有单据？试试"),
                            TextSpan(
                                text: "按商品退货",
                                style:
                                    const TextStyle(color: Color(0xFF2769FF)),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Navigator.pushReplacement(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                const BillBackGoodsPage()));
                                  })
                          ]),
                    ),
                  ),
                HaloButton(
                  width: 196.w,
                  height: 66.h,
                  buttonType: HaloButtonType.outlinedButton,
                  outLineWidth: 2.w,
                  borderColor: ColorUtil.stringColor("999999"),
                  textColor: ColorUtil.stringColor("333333"),
                  text: "取消",
                  fontSize: 26.sp,
                  fontWeight: FontWeight.normal,
                  onPressed: () => _doClose(),
                ),
                Container(
                  padding: EdgeInsets.only(right: 20.w, left: 30.w),
                  child: HaloButton(
                    width: 196.w,
                    height: 66.h,
                    text: "确定",
                    fontSize: 26.sp,
                    fontWeight: FontWeight.normal,
                    onPressed: () => _doSubmit(),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  //endregion
  //region action
  _doClearGoods() {
    setState(() {
      goodsDetailDto = null;
    });
  }

  _doChoseGoodsDetail() {
    showDialog(
            context: context,
            builder: (context) => const GoodsAndComboSelectListPage())
        .then((scanResult) {
      if (scanResult == null) {
        return null;
      } else if (scanResult is PtypeListModel) {
        //套餐行
        if (scanResult.comboId?.isNotEmpty == true) {
          assert(scanResult.comboRow, "数据异常，请勿单独处理套餐明细行");
          List<GoodsDetailDto> comboGoodsList =
              ScanTool.transformComboToGoodsList(scanResult, BillType.SaleBill);
          for (var element in comboGoodsList) {
            if (element.comboRow) {
              goodsDetailDto = element;
              break;
            }
          }
        }
        //普通商品
        else {
          goodsDetailDto =
              scanResult.changeModel(vchtype: BillTypeData[BillType.SaleBill]);
        }
      }
      //当扫码结果为商品时，需要判断该商品是否关联批次，是否关联序列号
      else if (scanResult is GoodsDetailDto) {
        goodsDetailDto = scanResult;
      }
      setState(() {});
    });
  }

  _doChoseVip() {
    SelectVipMixin.searchVip(context,
            jumpToAdd: false, showExpiredDialog: false)
        .then((vipInfo) {
      if (vipInfo != null) {
        setState(() {
          this.vipInfo = vipInfo;
        });
      }
    });
  }

  _doClearVip() {
    setState(() {
      vipInfo = null;
    });
  }

  _doItem(OrderBillItem? item) {
    if (item == null) {
      return;
    }
    for (OrderBillItem data in dataSource) {
      data.select = false;
    }
    setState(() {
      item.select = true;
    });
  }

  void _doSubmit() {
    OrderBillItem? selectItem;
    for (OrderBillItem item in dataSource) {
      if (item.select) {
        selectItem = item;
        break;
      }
    }
    String string = widget.billType == BillType.SaleBackBill ? "退货" : "换货";
    if (selectItem == null) {
      HaloToast.showInfo(context, msg: "没有选中单据!");
      return;
    }
    if ((selectItem.memo?.contains(creditsExchange) ?? false) ||
        ((selectItem.orderBillMarkList.any((element) =>
            element?.baseOrderMarkEnum == BillMarkEnum.INTEGRAL_EXCHANGE)))) {
      HaloToast.showInfo(context, msg: "积分兑换单据不支持$string!");
      return;
    }
    if (selectItem.orderBillMarkList.any((element) =>
        element?.baseOrderMarkEnum == BillMarkEnum.RECHARGE_GIFTS)) {
      HaloToast.showInfo(context, msg: "充值赠送商品不支持$string!");
      return;
    }
    // else if (selectItem.memo?.contains(CouponTool.discountZero) ?? false) {
    //   HaloToast.showInfo(context, msg: "仅礼品券单据不支持$string!");
    //   return;
    // }
    if (widget.submitCallback != null) {
      Navigator.pop(context);
      widget.submitCallback!(selectItem);
    }
  }

  _doClose() {
    Navigator.pop(context);
  }

  _doSearch() {
    dataSource.clear();
    pageIndex = 1;
    loadMore = true;
    loadData();
  }

  _doStartDate() {
    HaloDateTimePickers.showDateTimePickerView(context,
        minYearDayDate:
            PDuration.parse(afterTime.add(const Duration(days: -30))),
        maxYearDayDate: PDuration.parse(
            DateUtil.getDateTime(textEndTimeController.text) ?? DateTime.now()),
        currentDateTime: DateUtil.getDateTime(textStartTimeController.text),
        onSubmitted: (selectedDateTime) {
      if (selectedDateTime != null) {
        if (_checkStartTime(selectedDateTime)) {
          textStartTimeController.text =
              DateUtil.getDateStrByDateTime(selectedDateTime) ?? "";
        }
      }
    });
  }

  _doEndDate() {
    HaloDateTimePickers.showDateTimePickerView(context,
        minYearDayDate: PDuration.parse(
            DateUtil.getDateTime(textStartTimeController.text) ??
                DateTime.now()),
        maxYearDayDate: PDuration.parse(afterTime),
        currentDateTime: DateUtil.getDateTime(textEndTimeController.text),
        onSubmitted: (selectedDateTime) {
      if (selectedDateTime != null) {
        if (_checkEndTime(selectedDateTime)) {
          textEndTimeController.text =
              DateUtil.getDateStrByDateTime(selectedDateTime) ?? "";
        }
      }
    });
  }

  bool _checkStartTime(DateTime dateTime) {
    if (dateTime.millisecondsSinceEpoch -
            DateUtil.getDateTime(textEndTimeController.text)!
                .millisecondsSinceEpoch >
        0) {
      HaloToast.showError(context, msg: "开始时间不可以大于结束时间，请重新选择!");
      return false;
    }
    return true;
  }

  bool _checkEndTime(DateTime dateTime) {
    if (dateTime.millisecondsSinceEpoch -
            DateUtil.getDateTime(textStartTimeController.text)!
                .millisecondsSinceEpoch <
        0) {
      HaloToast.showError(context, msg: "结束时间不可以小于开始时间，请重新选择!");
      return false;
    }
    return true;
  }

//endregion
}
