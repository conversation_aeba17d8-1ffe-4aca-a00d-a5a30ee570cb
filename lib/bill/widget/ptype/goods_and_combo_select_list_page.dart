import 'dart:io';

import 'package:flutter/material.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../../../bill/entity/ptype/ptype_prop_dto.dart';
import '../../../bill/entity/ptype_suit_model.dart';
import '../../../bill/tool/props_helper.dart';
import '../../../bill/widget/mixin/select_goods_and_combo_mixin.dart';
import '../../../bill/widget/ptype/base_goods_dialog.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../common/widget/ptype_note_richtext.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';

import '../../tool/bill_tool.dart';

///选择商品和套餐页面
///返回一个`List<GoodsDetailDto>`,需要注意，单据页面(销售出库单和退货单)中，该实体类需要添加一个`vchType`的枚举字符串，否则开单时，会报错
class GoodsAndComboSelectListPage extends StatefulWidget {
  const GoodsAndComboSelectListPage({Key? key}) : super(key: key);

  @override
  State createState() => _GoodsAndComboSelectListPageState();
}

///State
class _GoodsAndComboSelectListPageState
    extends BaseGoodsDialogState<GoodsAndComboSelectListPage>
    with ListAndListTitleMixin, SearchMixin, SelectGoodsAndComboMixin {
  @override
  double get height => 838.h;

  @override
  double get width => 1200.w;

  @override
  double get searchBarHeight => 80.h;

  @override
  String get title => "选择商品";

  @override
  String get searchHint => "请输入商品名称/编号/条码/序列号查询";

  @override
  int get itemCount => goodsAndComboList.length;

  ///表头各列标题
  @override
  List<String> get listTitles {
    if (showStock) {
      return ["编号/名称", "条码", "单位", "单价", "库存"];
    }
    return ["编号/名称", "条码", "单位", "单价"];
  }

  ///列表各列比例
  @override
  List<int> get listColumnFlex {
    if (showStock) {
      return [4, 2, 1, 2, 2];
    }
    return [4, 2, 1, 2];
  }

  ///列表内边距
  @override
  EdgeInsets get listPadding => EdgeInsets.only(left: 40.w, right: 48.w);

  ///是否展示库存
  final bool showStock = SpTool.getSetting().openStock;

  ///构建商品属性拼接字符串
  String _buildGoodsPropertyString(PtypeListModel item) {
    List<PtypePropDto> prop = PropsHelper.getPropListBySkuProps(item.sku);
    StringBuffer property = StringBuffer();
    for (int i = 0; i < prop.length; i++) {
      if (i != 0) {
        property.write(":");
      }
      property.write(prop[i].propvalueName ?? "");
    }
    return property.toString();
  }

  @override
  Widget buildList() {
    return buildRefreshWidget();
  }

  @override
  Widget buildGoodsList() {
    return super.buildList();
  }

  @override
  Widget buildListTitleColumn(int index, String title) {
    //这里第1、2列名称编号是左对齐，其他是右对齐
    if (index > 1) {
      return Expanded(
        flex: listColumnFlex[index],
        child: Text(
          title,
          style: listTitleTextStyle,
          textAlign: TextAlign.end,
          maxLines: 1,
        ),
      );
    }
    return super.buildListTitleColumn(index, title);
  }

  @override
  Widget buildListItem(BuildContext context, int index) {
    final style = TextStyle(color: const Color(0xff333333), fontSize: 22.sp);
    final item = goodsAndComboList[index];
    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => _onItemClick(context, item),
          child: Container(
            height: 110.h,
            padding: listPadding,
            child: Row(
              children: [
                //商品名称和编号
                _buildName(item, style),
                //条码
                Expanded(
                  flex: listColumnFlex[1],
                  child: Text(
                    item.fullbarcode,
                    style: style.copyWith(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.start,
                  ),
                ),
                //单位
                Expanded(
                  flex: listColumnFlex[2],
                  child: Text(
                    item.unit?.unitName ?? "-",
                    style: style,
                    textAlign: TextAlign.end,
                  ),
                ),
                //单价
                Expanded(
                  flex: listColumnFlex[3],
                  child: Text(
                    "￥${item.currencyPrice ?? 0}",
                    style: TextStyle(
                      color: const Color(0xFFE42C2D),
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
                //库存
                if (showStock && listColumnFlex.length >= 5)
                  Expanded(
                    flex: listColumnFlex[3],
                    child: Text(
                      BillTool.getStockQty(
                        item.stockQty,
                        item.unit?.unitRate,
                        item.pcategory,
                      ),
                      style: style,
                      textAlign: TextAlign.end,
                    ),
                  ),
              ],
            ),
          ),
        ),
        //分割线
        divider,
      ],
    );
  }

  ///套字logo
  Widget _buildComboLogo() {
    const color = Color(0XFFCA12BC);
    return Container(
      decoration: BoxDecoration(
        color: Colors.transparent,
        //设置四周圆角 角度
        borderRadius: BorderRadius.all(Radius.circular(4.sp)),
        border: Border.all(color: color, width: 2.w),
      ),
      margin: EdgeInsets.only(right: 4.w),
      alignment: Alignment.center,
      width: 28.w,
      height: 28.w,
      child: HaloPosLabel(
        "套",
        textAlign: TextAlign.center,
        textStyle: TextStyle(fontSize: 16.sp, color: color),
      ),
    );
  }

  Widget _buildName(PtypeListModel item, TextStyle style) {
    StringBuffer name = StringBuffer(item.fullname);
    if (!item.comboRow) {
      name.write(" ${_buildGoodsPropertyString(item)}");
    }
    return Expanded(
      flex: listColumnFlex[0],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            item.usercode ?? "",
            style: style.copyWith(fontWeight: FontWeight.bold),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          Row(
            children: [
              Expanded(
                child: PtypeNoteRichText(
                  goodsDetailDto: conversionGoodsDetail(item),
                  textStyle: style,
                  showProp: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  ///点击item，将商品和套餐返回给上一个页面
  ///返回:
  ///[GoodsDetailDto]商品
  ///[PtypeSuitModel]套餐
  void _onItemClick(BuildContext context, dynamic item) async {
    Navigator.pop(context, item);
  }

  @override
  void onSearched(BuildContext context) => refresh();

  conversionGoodsDetail(PtypeListModel ptypeListModel) {
    GoodsDetailDto goodsDetailDto = GoodsDetailDto();
    goodsDetailDto.pFullName = ptypeListModel.fullname;
    goodsDetailDto.comboRow = ptypeListModel.comboRow;
    goodsDetailDto.batchenabled = ptypeListModel.batchenabled ?? false;
    goodsDetailDto.snenabled = ptypeListModel.snenabled ?? 0;
    goodsDetailDto.propenabled = ptypeListModel.propenabled ?? false;
    goodsDetailDto.pcategory = ptypeListModel.pcategory ?? 0;
    List<PtypePropDto> prop = PropsHelper.getPropListBySkuProps(
      ptypeListModel.sku,
    );
    goodsDetailDto.prop = prop;
    return goodsDetailDto;
  }
}
