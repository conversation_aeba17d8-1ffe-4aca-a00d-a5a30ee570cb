import 'package:collection/collection.dart';
import 'package:halo_utils/utils/String_util.dart';

import '../../common/tool/sp_tool.dart';
import '../../common/tool/system_config_tool.dart';
import '../../entity/system/system_config_dto.dart';
import '../../enum/bill_decimal_type.dart';
import '../../login/entity/store/store_info.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/goods_detail_dto.dart';
import '../entity/preferential_dto.dart';
import 'decimal_display_helper.dart';
import 'goods_tool.dart';
import 'promotion/manual_price.dart';
import 'promotion/preferential.dart';
import 'promotion/price.dart';
import 'promotion/promotion.dart';

class BillGoodsUtil {
  BillGoodsUtil._();

  ///是否是促销商品
  static bool isPromotionGoods(GoodsDetailDto goods) {
    return goods.currencyPtypePreferentialTotal > 0 || goods.promotionGift;
  }

  ///是否是折扣商品
  static bool isDiscountGoods(GoodsDetailDto goods, {bool markGift = true}) {
    return (goods.discount != 1 ||
            (goods.discountPrice > 0 &&
                goods.discountPrice != goods.currencyPrice)) &&
        (markGift || !goods.gift) &&
        !GoodsTool.isComboDetail(goods) &&
        goods.unitQty > 0;
  }

  ///是否是提货券赠品
  static bool isCouponGift(GoodsDetailDto goods) {
    return goods.gift &&
        !goods.manualPrice &&
        !goods.promotionGift &&
        goods.preferentialHelp.containsKey(Preferential.giftCoupon.name);
  }

  ///是否是满件赠/满额赠赠品
  ///[fixedGift] 固定赠品(买赠同品，指定赠品)
  static bool isPromotionGift(GoodsDetailDto goods, [bool? fixedGift]) {
    if (goods.gift &&
        goods.promotionGift &&
        goods.promotionType != PromotionType.halfPrice.value &&
        goods.promotionType != PromotionType.combinationBuy.value) {
      if (fixedGift != null &&
          ((goods.promotionGiftScope != PromotionGiftScope.chooseGoods.value) !=
              fixedGift)) {
        return false;
      }
      return true;
    }
    return false;
  }

  ///是否是第二件半价赠品
  static bool isPromotionHalfPriceGift(GoodsDetailDto goods) {
    return goods.promotionGift &&
        goods.promotionType == PromotionType.halfPrice.value;
  }

  ///单据组bug，有税率，没税额，也表示开启了税率。
  ///商品基础信息可以填税率。因此没启用税率，让pos把商品的税率清除掉。
  static setTaxRate(GoodsDetailDto goods) {
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
    if (systemConfigDto.sysGlobalEnabledTax) {
      bool ptypePriceHasTax = systemConfigDto.sysGlobalEnabledSaleTax; //商品是否含税
      if (ptypePriceHasTax) {
        goods.taxRate = goods.taxRate;
      } else {
        //管税，商品价格不含税
        goods.taxRate = 0;
      }
    } else {
      goods.taxRate = 0;
    }
  }

  static void initGoodsInfo(GoodsDetailDto goods, GoodsBillDto bill) {
    //设置税率
    setTaxRate(goods);
    //设置单据信息
    goods.vchcode = bill.vchcode;
    goods.vchtype = bill.vchtype;
    //商品添加仓库信息
    StoreInfo? shopConfigEntity = SpTool.getStoreInfo();
    if (StringUtil.isNotZeroOrEmpty(shopConfigEntity!.ktypeId)) {
      goods.ktypeId = shopConfigEntity.ktypeId;
      goods.kfullname = shopConfigEntity.ktypeName;
    }
    goods.costPrice = num.parse(
        DecimalDisplayHelper.getPriceFixed(goods.costPrice.toString()));
    goods.currencyPrice = num.parse(
        DecimalDisplayHelper.getPriceFixed(goods.currencyPrice.toString()));
  }

  ///判断两个商品是否可以合并（对于套餐，还需判断明细行是否都一样）
  ///[mergeHalfPriceGoods] 是否合并第二件半价商品，现在执行打折和促销之前，应该先将第二件半价商品合并(组合购也是)
  ///避免出现之前触发第二件半价，而再次执行的时候不触发，导致出现两个相同的商品行出现，而在这之后再合并的话，可能会导致金额和优惠有精度问题
  static bool compareGoods(GoodsDetailDto goods1, GoodsDetailDto goods2,
      {bool mergeHalfPriceGoods = false}) {
    if (goods1 == goods2) {
      return true;
    }
    bool isComboDetail = GoodsTool.isComboDetail(goods1);
    //赠品：1.手工改价 2.促销 3.提货券
    //促销赠品：1.第二件半价 2.满件赠/满额赠（又细分：1.买赠同品 2.指定赠品）
    //提货券：优惠券id是否一致
    if (goods1.manualPrice != goods2.manualPrice) {
      return false;
    }
    if (goods1.gift != goods2.gift) {
      return false;
    }
    if (goods1.promotionGift != goods2.promotionGift) {
      if (!mergeHalfPriceGoods) {
        return false;
      }
      //必须是第二件半价或组合购促销内的商品
      if (isPromotionGift(goods1) && isPromotionGift(goods2)) {
        return false;
      }
    }
    //unitSku是否一致
    if (!GoodsTool.compareUnitSku(goods1, goods2,
        comparePrice: !isComboDetail)) {
      return false;
    }
    //对于手工改价，需要判断他们的折扣是否一致
    if (goods1.manualPrice &&
        !isComboDetail && //套餐明细行只比较比例
        (goods1.discount != goods2.discount ||
            goods1.discountPrice != goods2.discountPrice)) {
      return false;
    }
    //是否都是/不是套餐，且套餐id一致
    if (!GoodsTool.compareComboRow(goods1, goods2, isCompareUnitSku: false)) {
      return false;
    }
    //是否都是/不是套餐明细
    if (!GoodsTool.compareComboDetail(goods1, goods2,
        isCompareUnitSku: false)) {
      return false;
    }
    //是否是同一个批次
    if (!GoodsTool.compareBatch(goods1, goods2, isCompareUnitSku: false)) {
      return false;
    }
    //促销赠品:
    //1.判断promotionType和id是否一致
    //2.对于满件赠/满额赠，判断是买赠同品还是指定赠品
    if (goods1.promotionGift) {
      if (goods1.promotionType != goods2.promotionType ||
          goods1.promotionId != goods2.promotionId) {
        return false;
      }
      if (goods1.promotionType == PromotionType.fullAmountGift.value ||
          goods1.promotionType == PromotionType.fullCountGift.value) {
        if (goods1.promotionGiftScope != goods2.promotionGiftScope) {
          return false;
        }
      }
    }
    //对于提货券赠品，判断优惠券id是否相同
    else {
      if (goods1.gift) {
        final oldCouponId =
            goods1.preferentialHelp[Preferential.giftCoupon.name]?.helpId;
        final newCouponId =
            goods1.preferentialHelp[Preferential.giftCoupon.name]?.helpId;
        if (oldCouponId != null || newCouponId != null) {
          if (oldCouponId != newCouponId) {
            return false;
          }
        }
      }
    }
    return true;
  }

  ///比价两个套餐是否相同
  ///[mergeHalfPriceGoods] 是否合并第二件半价商品，现在执行打折和促销之前，应该先将第二件半价商品合并，(组合购也是)
  ///避免出现之前触发第二件半价，而再次执行的时候不触发，导致出现两个相同的商品行出现，而在这之后再合并的话，可能会导致金额和优惠有精度问题
  static bool compareCombo({
    required GoodsDetailDto combo1,
    required GoodsDetailDto combo2,
    required List<GoodsDetailDto> comboDetails1,
    required List<GoodsDetailDto> comboDetails2,
    bool mergeHalfPriceGoods = false,
  }) {
    if (!compareGoods(combo1, combo2,
            mergeHalfPriceGoods: mergeHalfPriceGoods) ||
        comboDetails1.length != comboDetails2.length) {
      return false;
    }
    for (var value in comboDetails1) {
      if (!comboDetails2.any((element) => compareGoods(value, element,
          mergeHalfPriceGoods: mergeHalfPriceGoods))) {
        return false;
      }
    }
    return true;
  }

  ///合并商品
  ///[mergeHalfPriceGoods] 是否合并第二件半价商品，现在执行打折和促销之前，应该先将第二件半价商品合并，(组合购也是)
  ///避免出现之前触发第二件半价，而再次执行的时候不触发，导致出现两个相同的商品行出现，而在这之后再合并的话，可能会导致金额和优惠有精度问题
  static GoodsDetailDto? addOrMergeGoods(
      List<GoodsDetailDto> dataSource, List<GoodsDetailDto> goodsList,
      {List<GoodsDetailDto>? existGoodsList,
      bool mergeHalfPriceGoods = false}) {
    GoodsDetailDto? result;
    Set<GoodsDetailDto> loopedSet = {};
    Set<GoodsDetailDto> sameGoodsSet = {};
    existGoodsList ??= dataSource;
    for (var goods in goodsList) {
      if (loopedSet.add(goods)) {
        List<GoodsDetailDto> sameGoodsList = existGoodsList
            .where((element) => compareGoods(element, goods,
                mergeHalfPriceGoods: mergeHalfPriceGoods))
            .toList();
        loopedSet.addAll(sameGoodsList);
        //未找到则添加
        if (sameGoodsList.isEmpty) {
          existGoodsList.add(goods);
          result ??= goods;
        } else {
          final first = sameGoodsList.first;
          for (var element in sameGoodsList) {
            if (first != element) {
              mergeGoods(
                  existGoods: first,
                  goods: element,
                  mergeHalfPriceGoods: mergeHalfPriceGoods);
              sameGoodsSet.add(element);
            }
          }
          if (first != goods) {
            mergeGoods(
                existGoods: first,
                goods: goods,
                mergeHalfPriceGoods: mergeHalfPriceGoods);
            sameGoodsSet.add(goods);
            result ??= first;
          } else {
            result ??= goods;
          }
        }
      }
    }
    if (sameGoodsSet.isNotEmpty) {
      dataSource.removeWhere((element) => sameGoodsSet.contains(element));
    }
    return result;
  }

  ///合并套餐
  ///[mergeHalfPriceGoods] 是否合并第二件半价商品，现在执行打折和促销之前，应该先将第二件半价商品合并，(组合购也是)
  ///避免出现之前触发第二件半价，而再次执行的时候不触发，导致出现两个相同的商品行出现，而在这之后再合并的话，可能会导致金额和优惠有精度问题
  static GoodsDetailDto? addOrMergeCombo(
    List<GoodsDetailDto> dataSource,
    Map<String, GoodsDetailDto> comboMap,
    Map<String, List<GoodsDetailDto>> comboDetailsMap, {
    Map<String, GoodsDetailDto>? existComboMap,
    Map<String, List<GoodsDetailDto>>? existComboDetailsMap,
    bool mergeHalfPriceGoods = false,
  }) {
    GoodsDetailDto? result;
    if (existComboMap == null || existComboDetailsMap == null) {
      existComboMap = {};
      existComboDetailsMap = {};
      GoodsTool.filterGoodsListAndGetCombo(dataSource,
          comboMap: existComboMap, comboDetailsMap: existComboDetailsMap);
    }
    Set<GoodsDetailDto> loopedSet = {};
    Set<GoodsDetailDto> sameGoodsSet = {};
    for (var combo in comboMap.values) {
      if (!loopedSet.add(combo)) {
        continue;
      }
      // //修改商品会出现本身存在，所以跳过
      // if (combo == existComboMap[combo.comboRowId]) {
      //   continue;
      // }
      final comboDetails = comboDetailsMap[combo.comboRowId];
      if (comboDetails == null || comboDetails.isEmpty) {
        continue;
      }
      List<GoodsDetailDto> sameComboList = existComboMap.values
          .where((element) => compareCombo(
                combo1: combo,
                combo2: element,
                comboDetails1: comboDetails,
                comboDetails2: existComboDetailsMap![element.comboRowId] ?? [],
                mergeHalfPriceGoods: mergeHalfPriceGoods,
              ))
          .toList();
      loopedSet.addAll(sameComboList);
      //合并套餐
      if (sameComboList.isNotEmpty) {
        final first = sameComboList.first;
        final firstExistComboDetails =
            existComboDetailsMap[first.comboRowId] ?? [];
        for (var value in sameComboList) {
          final valueComboDetails =
              existComboDetailsMap[value.comboRowId] ?? [];
          if (value != first) {
            mergeGoods(
                existGoods: first,
                goods: value,
                mergeHalfPriceGoods: mergeHalfPriceGoods,
                existComboDetails: firstExistComboDetails,
                newComboDetails: valueComboDetails);
            sameGoodsSet.add(value);
            sameGoodsSet.addAll(valueComboDetails);
          }
        }
        if (first != combo) {
          result ??= combo;
          mergeGoods(
              existGoods: first,
              goods: combo,
              mergeHalfPriceGoods: mergeHalfPriceGoods,
              existComboDetails: firstExistComboDetails,
              newComboDetails: comboDetails);
          sameGoodsSet.add(combo);
          sameGoodsSet.addAll(comboDetails);
        } else {
          result ??= combo;
        }
      }
      //新增套餐
      else {
        result ??= combo;
        dataSource.add(combo);
        dataSource.addAll(comboDetails);
      }
    }
    if (sameGoodsSet.isNotEmpty) {
      dataSource.removeWhere((element) => sameGoodsSet.contains(element));
    }
    return result;
  }

  ///合并商品，即修改原商品数量
  ///1.添加商品后合并，之后会执行优惠流程
  ///2.修改商品后合并，之后会执行优惠流程
  ///3.促销赠品选择后合并，折扣为1，促销后价格为0，需要重新计算和汇总优惠辅助信息
  ///4.提货券选择赠品后合并，折扣为0，需要重新计算和汇总优惠辅助信息
  ///
  ///[mergeHalfPriceGoods] 是否合并第二件半价商品，现在执行打折和促销之前，应该先将第二件半价商品合并，(组合购也是)
  ///避免出现之前触发第二件半价，而再次执行的时候不触发，导致出现两个相同的商品行出现，而在这之后再合并的话，可能会导致金额和优惠有精度问题
  static void mergeGoods({
    required GoodsDetailDto existGoods,
    required GoodsDetailDto goods,
    List<GoodsDetailDto> existComboDetails = const [],
    List<GoodsDetailDto> newComboDetails = const [],
    bool mergeHalfPriceGoods = false,
  }) {
    if (existGoods == goods) return;
    existGoods.comboRow = GoodsTool.isComboRow(existGoods);
    existGoods.unitQty = SystemConfigTool.doubleAddToDecimal(
        existGoods.unitQty, goods.unitQty, BillDecimalType.QTY);
    GoodsQtyUtil.onQtyChange(existGoods, existGoods.unitQty);
    _mergeGoods(existGoods, goods);
    if (existGoods.comboRow) {
      Set<GoodsDetailDto> newComboDetailsSet = newComboDetails.toSet();
      //重新计算数量
      ComboPreferentialTool.handleComboDetail(existGoods, existComboDetails,
          calculateQty: true,
          totalGetter: (g) => g.currencyTotal,
          totalSetter: (g, total) {
            GoodsTotalUtil.onCurrencyTotalChange(g, total);
            GoodsDetailDto? newComboDetail;
            //序列号商品，需要找出要合并的套餐明细，合并序列号
            if (GoodsTool.isSerialGoods(g)) {
              newComboDetail = newComboDetailsSet.firstWhereOrNull((element) =>
                  compareGoods(g, element,
                      mergeHalfPriceGoods: mergeHalfPriceGoods));
              //这里找到了就移除，避免套餐内两个商品一模一样，每次找到同一个套餐明细去合并
              newComboDetailsSet.remove(newComboDetail);
            }
            _mergeGoods(g, newComboDetail);
          });
    }
    //手工改价不在优惠执行流程中，而是每次手动触发，所以手工改价的合并需要在这里计算优惠金额
    if (existGoods.manualPrice) {
      ManualPriceUtil.onQtyChange(existGoods, existGoods.unitQty,
          comboDetails: existComboDetails);
    }
  }

  ///合并商品，即修改原商品数量
  ///1.添加商品后合并，之后会执行优惠流程
  ///2.修改商品后合并，之后会执行优惠流程
  ///3.促销赠品选择后合并，折扣为1，促销后价格为0，需要重新计算和汇总优惠辅助信息
  ///4.提货券选择赠品后合并，折扣为0，需要重新计算和汇总优惠辅助信息
  ///对于序列号商品，还需合并序列号
  static void _mergeGoods(GoodsDetailDto existGoods,
      [GoodsDetailDto? newGoods]) {
    //合并序列号商品
    if (GoodsTool.isSerialGoods(existGoods) && newGoods != null) {
      existGoods.serialNoList.addAll(newGoods.serialNoList);
    }
    if (existGoods.gift) {
      if (existGoods.promotionGift) {
        //促销后金额为0
        GoodsTotalUtil.onPromotedTotalChange(existGoods, 0);
        if (!GoodsTool.isComboDetail(existGoods)) {
          existGoods.preferentialHelp[Preferential.goodsPromotion.name]?.total =
              existGoods.currencyPreferentialTotal;
        }
      } else if (existGoods.preferentialHelp[Preferential.giftCoupon.name] !=
          null) {
        //折扣为0
        GoodsPriceUtil.onDiscountChange(existGoods, 0);
        if (!GoodsTool.isComboRow(existGoods)) {
          existGoods.preferentialHelp[Preferential.giftCoupon.name]?.total =
              existGoods.currencyPreferentialTotal;
        }
      }
    }
  }
}
