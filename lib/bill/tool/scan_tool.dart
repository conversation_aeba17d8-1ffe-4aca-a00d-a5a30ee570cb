import 'package:barcode_scan2/model/android_options.dart';
import 'package:barcode_scan2/model/scan_options.dart';
import 'package:barcode_scan2/platform_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/common/tool/dialog_util.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../../bill/entity/ptype/ptype_batch_dto.dart';
import '../../bill/entity/ptype/ptype_batch_param_qty_detail_dto.dart';
import '../../bill/entity/ptype/ptype_serial_no_dto.dart';
import '../../bill/entity/ptype_suit_detail_model.dart';
import '../../bill/entity/ptype_suit_model.dart';
import '../../bill/model/bill_model.dart';
import '../../bill/model/ptype_model.dart';
import '../../bill/widget/ptype/back_goods_bind_batch_page.dart';
import '../../bill/widget/ptype/goods_bind_batch_page.dart';
import '../../bill/widget/ptype/goods_bind_sn_page.dart';
import '../../common/tool/performance_capture_util.dart';
import '../../common/tool/sp_tool.dart';
import '../../enum/bill_ptype_pop_value_change_type.dart';
import '../../enum/bill_type.dart';
import '../../login/entity/store/store_info.dart';
import '../../plugin/notify_voice_plugin.dart';
import '../../widgets/selector/ptype_selector.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

import '../../common/tool/sp_tool.dart';
import '../../enum/bill_ptype_pop_value_change_type.dart';
import '../../enum/bill_type.dart';
import '../../login/entity/store/store_info.dart';
import '../../plugin/notify_voice_plugin.dart';
import '../../widgets/selector/ptype_selector.dart';
import '../entity/bill_ptype_request.dart';
import '../entity/goods_detail_dto.dart';
import '../entity/ptype/ptype_and_sku_dto.dart';
import '../entity/ptype/ptype_batch_dto.dart';
import '../entity/ptype/ptype_batch_param_qty_detail_dto.dart';
import '../entity/ptype_suit_detail_model.dart';
import '../entity/ptype_suit_model.dart';
import '../model/bill_model.dart';
import '../model/ptype_model.dart';
import '../widget/ptype/back_goods_bind_batch_page.dart';
import '../widget/ptype/goods_bind_batch_page.dart';
import '../widget/ptype/goods_bind_sn_page.dart';
import '../widget/ptype/multi_goods_bind_batch.dart';
import '../widget/ptype/multi_goods_bind_sn.dart';
import 'bill_price_compute_helper.dart';
import 'goods_tool.dart';

///
///@ClassName: scan_tool
///@Description: 扫码类
///@Author: tanglan
///@Date: 8/10/21 11:38 AM

class ScanTool {
  ///通过扫码添加商品到单据
  ///注意：退货单不支持序列号扫码
  static Future<List<GoodsDetailDto>?> scan(
      {required BuildContext context,
      required BillType billType,
      required String scanCode,
      required String billDate,
      //开单页面已有的商品list
      required List<GoodsDetailDto> list,
      //是否处理批次
      bool isCheckBatch = true,
      String skuBarCode = ""}) async {
    //若条码为空，直接返回
    if (StringUtil.isEmpty(scanCode)) {
      HaloToast.show(context, msg: "请扫描条码");
      return null;
    }
    //退货单不支持扫描序列号
    if (billType != BillType.SaleBackBill) {
      //若扫描的序列号已经存在，则直接返回
      if (_isSNExist(list, scanCode)) {
        HaloToast.show(context, msg: "该商品已经被添加");
        return null;
      }
    }
    PerformanceCaptureUtil.start(PerformanceTimeName.scanPType);
    //从接口获取扫码结果,优先级 序列号>商品sku>套餐
    final scanResult = await _scan(
        context: context,
        billType: billType,
        scanCode: scanCode,
        billDate: billDate,
        skuBarCode: skuBarCode);
    PerformanceCaptureUtil.end(PerformanceTimeName.scanPType);
    if (scanResult == null) {
      return null;
    }
    //处理新增的商品/套餐,绑定批次号/序列号
    List<GoodsDetailDto>? result = await handleScanResult(
        context, scanResult, list, billType,
        handleBatch: isCheckBatch);
    return result;
  }

  /// 扫码优先顺序
  /// 获取优先级为:序列号>sku条码>套餐条码
  /// 注意：退货单不支持序列号扫码
  /// 可能返回:
  /// *[null] 没有查询到任何商品或套餐
  /// *[GoodsDetailDto] 商品
  /// *[PtypeSuitModel] 套餐
  static Future<PtypeListModel?> _scan(
      {required BuildContext context,
      required BillType billType,
      required String scanCode,
      required String billDate,
      String skuBarCode = ""}) async {
    StoreInfo storeInfo = SpTool.getStoreInfo()!;

    BillPtypeRequest paramRequest = BillPtypeRequest();
    paramRequest.btypeId = storeInfo.btypeId;
    paramRequest.otypeId = storeInfo.otypeId;
    paramRequest.ktypeId = storeInfo.ktypeId;
    paramRequest.fullBarCode = true;
    //退货单不支持序列号扫码
    paramRequest.searchBySn = billType != BillType.SaleBackBill;
    paramRequest.filterValue = scanCode;
    paramRequest.skuBarCode = skuBarCode;
    List<PtypeListModel> ptypeList = await PtypeModel.selectPtypeAndCombo(
        context,
        queryParam: paramRequest,
        pageIndex: 1,
        pageSize: 40);
    if (ptypeList.isNotEmpty) {
      NotifyVoicePlugin.success();
      if (ptypeList.length == 1) {
        return ptypeList.first;
      } else {
        if (context.mounted) {
          return await HaloDialog(context,
              child: PtypeSelector(
                sourceList: ptypeList,
                barCode: scanCode,
                pageSize: 40,
                onItemClick: (buildContext, selectGoods) async {
                  NavigateUtil.pop(context, selectGoods);
                },
              )).show();
        }
      }
    } else {
      if (context.mounted) {
        DialogUtil.showAlertDialog(context, content: "未找到对应商品条码/序列号");
      }
      NotifyVoicePlugin.failure();
    }
    return null;
  }

  ///将套餐整合到已有的单据商品列表中
  static addComboToGoodsList(
      PtypeSuitModel model, List<GoodsDetailDto> list, BillType billType) {
    //找出在开单列表中，和扫码得到的同款套餐
    GoodsDetailDto? comboRow = list.firstWhere(
        (element) => element.comboRow && element.comboId == model.id,
        orElse: () {
      return GoodsDetailDto();
    });
    //如果找到了同款套餐，则将两者数据合并
    if (comboRow != null) {
      //数量+1
      comboRow.unitQty = comboRow.unitQty + 1;
      //找到单据列表中已有套餐下的商品
      for (GoodsDetailDto element in list) {
        if (element.comboRowParId != comboRow.comboRowId) {
          continue;
        }
        //qty是单个套餐所含该商品的实际单位数量，unitQty为总实际单位数量
        num qty = element.comboQtyRate;
        element.unitQty =
            num.tryParse(MathUtil.addDec(qty, element.unitQty).toString()) ?? 0;
        BillPriceComputeHelper().reCalculate(
            element, PtypePopValueChangeType.Qty, element.unitQty.toString());
      }
    }
    //若没有找到同款套餐，则直接将数据添加至已有列表
    else {
      list.addAll(transformComboToGoodsList(model, billType));
    }
  }

  ///处理套餐,将其转换为[GoodsDetailDto],
  ///不仅将**套餐**转换为商品实体类，同样将**套餐内的商品**也添加为实体类
  ///返回了包含套餐和套餐下商品的列表
  static List<GoodsDetailDto> transformComboToGoodsList(
      dynamic model, BillType billType) {
    List<GoodsDetailDto> result = [];
    if (model is PtypeSuitModel || model is PtypeListModel) {
      model.count ??= 1;
      final shopConfig = SpTool.getStoreInfo()!;
      String kFullName = shopConfig.ktypeName!;
      String ktypeId = shopConfig.ktypeId!;
      String onlyNum = (DateTime.now().millisecondsSinceEpoch).toString();
      //将套餐转化为商品实体类
      GoodsDetailDto suitGoodsDetail;
      if (model is PtypeSuitModel) {
        suitGoodsDetail = model.changeModel(ktypeId, "");
      }
      // (model is PtypeListModel)
      else {
        suitGoodsDetail = model.changeModel(vchtype: BillTypeData[billType]);
      }
      suitGoodsDetail.comboRowId = onlyNum;
      suitGoodsDetail.kfullname = kFullName;
      suitGoodsDetail.ktypeId = ktypeId;
      suitGoodsDetail.pcategory = 2;
      result.add(suitGoodsDetail);
      //遍历套餐下的商品，将其也转化为商品实体类
      if ((model.count ?? 0) > 0) {
        List comboDetails =
            model is PtypeSuitModel ? model.suitDetails : model.comboDetails;
        for (int j = 0; j < comboDetails.length; j++) {
          dynamic detailModel = comboDetails[j];
          GoodsDetailDto comboDetail = detailModel is PtypeSuitDetailModel
              ? detailModel.changeModel(model.count)
              : detailModel.changeModel(vchtype: BillTypeData[billType]);
          //todo 处理套餐数量对应的明细商品数量
          comboDetail.unitQty = MathUtil.multiplication(
                  comboDetail.comboQtyRate.toString(), model.count.toString())
              .toDouble();
          comboDetail.costTotal = MathUtil.multiplication(
                  detailModel.total.toString(), model.count.toString())
              .toDouble();
          comboDetail.currencyTotal = MathUtil.multiplication(
                  detailModel.total.toString(), model.count.toString())
              .toDouble();
          comboDetail.currencyDisedTaxedTotal = MathUtil.multiplication(
                  detailModel.total.toString(), model.count.toString())
              .toDouble();
          comboDetail.currencyDisedTotal = MathUtil.multiplication(
                  detailModel.total.toString(), model.count.toString())
              .toDouble();
          BillPriceComputeHelper().reCalculate(comboDetail,
              PtypePopValueChangeType.Qty, comboDetail.unitQty.toString());
          comboDetail.comboRowParId = onlyNum;
          comboDetail.comboId = model.id;
          comboDetail.ktypeId = ktypeId;
          comboDetail.kfullname = kFullName;
          comboDetail.comboRowId = "$onlyNum$j";
          result.add(comboDetail);
        }
      }
    }
    return result;
  }

  ///刷新价格、库存和条码
  ///todo 此方法已经作废
  @Deprecated("现在价格和库存已经和jxc那边逻辑不一样，此方法已经作废")
  static Future refreshPriceStockAndBarcode(
      BuildContext context, GoodsDetailDto goods, String billDate) {
    return PtypeModel.getPtypePriceStockAndBarcode(context,
            ptypeId: goods.ptypeId,
            ktypeId: SpTool.getStoreInfo()!.ktypeId,
            skuId: goods.comboRow ? goods.ptypeId : goods.skuId,
            unitId: goods.unitId,
            skuPrice: goods.skuPrice,
            otypeId: SpTool.getStoreInfo()?.otypeId)
        .then((value) {
      if (value != null) {
        goods.currencyPrice = value["price"] ?? 0;
        goods.discount = 1;
        goods.costPrice = goods.currencyPrice;
        goods.stockQty = value["stock"];
        if (StringUtil.isNotEmpty(value["barcode"])) {
          goods.fullbarcode = value["barcode"];
        }
      }
    });
  }

  ///验证扫描的序列号是否已经存在单据中
  static bool _isSNExist(List<GoodsDetailDto> list, String scanCode) {
    //便利单据中已有的所有商品，确定是否已有扫描的序列号
    return list?.any((element) =>
            null != element.serialNoList &&
            element.serialNoList.any((sn) => sn.snno == scanCode)) ??
        false;
  }

  ///处理新增商品,判断其是否关联批次号和序列号
  ///主要用到两个地方：
  ///1.开单时，扫码得到一个商品、套餐
  ///2.开单时，通过添加商品弹窗，搜索商品和套餐，并选择一个商品或套餐
  ///[scanResult]为要添加的商品/套餐，[PtypeSuitModel][GoodsDetailDto]
  ///返回新增的商品(处理过后的),套餐会转换成一个[GoodsDetailDto]列表,包含该套餐和套餐下的商品
  ///4.6新增了接口同时搜索套餐和商品，会返回[PtypeListModel],包含套餐和商品信息
  ///[snLimitGoodsCount] 是否限制序列号数量
  ///   序列号数量一定小于等于商品数量
  ///   当为true时，此时序列号数量最多为商品数量，
  ///   当为false时，则不限制序列号数量，若序列号数量大于商品数量，则调整商品数量为序列号数量
  ///[handleBatch] 是否处理批次和序列号
  static Future<List<GoodsDetailDto>?> handleScanResult(BuildContext context,
      dynamic scanResult, List<GoodsDetailDto> list, BillType billType,
      {snLimitGoodsCount = false, bool handleBatch = true}) async {
    //搜索商品和套餐接口的结果
    if (scanResult is PtypeListModel) {
      //套餐行
      if (scanResult.comboId?.isNotEmpty == true) {
        assert(scanResult.comboRow, "数据异常，请勿单独处理套餐明细行");
        List<GoodsDetailDto> comboGoodsList =
            transformComboToGoodsList(scanResult, billType);
        if (handleBatch) {
          await handleCombo(context, comboGoodsList, list);
        }
        return comboGoodsList;
      }
      //普通商品
      else {
        scanResult = scanResult.changeModel(vchtype: BillTypeData[billType]);
      }
    }
    // //当扫码结果为套餐时,将套餐转换成商品列表
    // if (scanResult is PtypeSuitModel) {
    //   List<GoodsDetailDto> comboGoodsList =
    //       transformComboToGoodsList(scanResult, billType);
    //   //当开启了自动带出批次
    //   await getPtypeAutoBatch(comboGoodsList, context);
    //   return comboGoodsList;
    // }
    //当扫码结果为商品时，需要判断该商品是否关联批次，是否关联序列号
    if (scanResult is GoodsDetailDto) {
      if (!handleBatch) {
        return [scanResult];
      }
      //先检查是否绑定批次号
      if (context.mounted) {
        scanResult =
            await _checkGoodsIsBindWithBatch(context, scanResult, billType);
      }
      if (null == scanResult) {
        return null;
      }
      //启用了批次号且批次号未选择,直接返回前端进行处理
      if (scanResult.batchenabled &&
          !GoodsTool.isGoodsBindWithBatch(scanResult)) {
        return [scanResult];
      }
      //再检查序列号
      if (context.mounted) {
        return _checkGoodsIsBindWithSn(context,
                goods: scanResult,
                billType: billType,
                existSN: list
                    .expand<PtypeSerialNoDto>((element) =>
                        (element.snenabled != 0) ? (element.serialNoList) : [])
                    .map<String>((e) => e.snno ?? "")
                    .toList(),
                snLimitGoodsCount: snLimitGoodsCount)
            .then((value) {
          if (value != null) {
            //这里商品数量至少为1
            if (value.unitQty <= 0) {
              value.unitQty = 1;
            }
            value.skuPrice = value.skuPrice ?? 0;
            return [value];
          }
          return null;
        });
      }
    }
    //没有扫描到任何商品，直接返回
    return null;
  }

  ///处理套餐
  static Future<void> handleCombo(BuildContext context,
      List<GoodsDetailDto> comboGoodsList, List<GoodsDetailDto> list) async {
    GoodsDetailDto combo =
        comboGoodsList.firstWhere((element) => element.comboRow);
    //自动带出批次
    await getPtypeAutoBatch(comboGoodsList, context);
    //检查是否需要绑定批次号
    if (comboGoodsList.any((element) =>
        !element.comboRow &&
        element.batchenabled &&
        !GoodsTool.isGoodsBindWithBatch(element))) {
      //手动选择批次号
      if (context.mounted) {
        await showDialog(
            context: context,
            builder: (context) => ComboBindBatchPage(comboGoodsList, combo));
      }
    }
    //检查是否需要绑定序列号
    if (comboGoodsList.any((element) =>
        !element.comboRow &&
        element.snenabled != 0 &&
        element.serialNoList.length != element.unitQty)) {
      if (context.mounted) {
        await showDialog(
            context: context,
            builder: (context) => ComboBindSnPage(
                comboGoodsList,
                list
                    .expand<PtypeSerialNoDto>((element) =>
                        (element.snenabled != 0) ? (element.serialNoList) : [])
                    .map<String>((e) => e.snno ?? "")
                    .toList(),
                combo));
      }
    }
  }

  ///检查商品是否关联批次
  ///若该商品支持批次号，并且未绑定批次号，则弹出批次号选择列表，进行批次号绑定
  ///若弹窗后，用户绑定了批次号，则返回[goods]
  ///若未绑定批次号直接关闭弹窗null
  static Future<GoodsDetailDto?> _checkGoodsIsBindWithBatch(
      BuildContext context, GoodsDetailDto goods, BillType billType) async {
    //自动带出批次号
    await getPtypeAutoBatch([goods], context);
    //若该商品开启了批次号
    if (goods.batchenabled && !GoodsTool.isGoodsBindWithBatch(goods)) {
      if (context.mounted) {
        return showDialog<GoodsDetailDto>(
          context: context,
          builder: (context) => (billType == BillType.SaleBill ||
                  billType == BillType.SaleChangeBill)
              ? GoodsBindBatchPage(goods: goods)
              : BackGoodsBindBatchPage(goods: goods),
        ).then((GoodsDetailDto? value) {
          if (value != null) {
            //批次号为空，说明没有选择批次号，直接关闭了窗口
            // if (!isGoodsBindWithBatch(value)) {
            //   return null;
            // }
            return value;
          }
        }).onError((error, stackTrace) => null);
      }
    }
    return goods;
  }

  ///检查商品是否关联序列号
  ///[existGoods] 为单据中已经有的该类型商品，用来校验输入的序列号是否重复,并不会在弹窗中展示
  ///[goods] 为需要编辑序列号的商品
  ///[snLimitGoodsCount] 是否限制序列号数量
  ///   序列号数量一定小于等于商品数量
  ///   当为true时，此时序列号数量最多为商品数量，
  ///   当为false时，则不限制序列号数量，若序列号数量大于商品数量，则调整商品数量为序列号数量
  static Future<GoodsDetailDto?> _checkGoodsIsBindWithSn(BuildContext context,
      {required GoodsDetailDto goods,
      List<String> existSN = const [],
      BillType billType = BillType.SaleBill,
      bool snLimitGoodsCount = false}) async {
    //商品支持序列号
    if (goods.snenabled != 0) {
      if ((goods.serialNoList.isEmpty != false)) {
        return showDialog<GoodsDetailDto>(
          context: context,
          builder: (context) => GoodsBindSnPage(
              goods: goods,
              existSN: existSN,
              billType: billType,
              limitMaxCount: snLimitGoodsCount),
        ).then((goods) {
          //序列号列表为空，说明没有选择序列号/严格序列号也允许继续开单，单据状态保留在待出入库即可
          return goods;
        }).onError((error, stackTrace) => null);
      } else {
        //新的开单页面由于通过序列号搜索出来的商品可以被重复点击选择，所以需要判断是否和单据中已有的序列号重复
        if (existSN
            .where((snno) => snno.isNotEmpty)
            .any((snno) => goods.serialNoList.any((sn) => sn.snno == snno))) {
          HaloToast.show(context, msg: "该商品序列号已被使用");
          return null;
        }
      }
    }
    return goods;
  }

  ///判断两个商品批次是否一致，再调用此方法前，最好判断两个商品是否是同一个商品，否则可能造成异常
  ///对于批次判断，分为两种情况，
  ///1.批次号为空，此时判断生产日期，保质期，过期日期，对于个别计价批次还需要判断批次成本是否相等
  ///2.批次号不为空，则直接判断批次号
  static bool isSameBatch(GoodsDetailDto goods1, GoodsDetailDto goods2) {
    if (goods1.batchenabled || goods2.batchenabled) {
      if (TextUtil.isEmpty(goods1.batchNo) &&
          TextUtil.isEmpty(goods2.batchNo)) {
        //生产日期不同,由于保质期挂在挂在商品表中，意思就是同一个商品保质期必定一样，所以只需要判断生产日期
        if (!StringUtil.equal(
            goods1.produceDate ?? "", goods2.produceDate ?? "")) {
          return false;
        }
        //若商品为个别计价商品，还需要判断batchPrice批次单价(成本)
        if (goods1.costMode == 1 || goods2.costMode == 1) {
          return goods1.batchPrice == goods2.batchPrice;
        }
      } else if (!StringUtil.equal(goods1.batchNo, goods2.batchNo)) {
        return false;
      }
    }
    //若两个商品均不是批次商品，则直接返回true
    return true;
  }

  ///多个商品自动带出批次号
  ///注意，个别计价商品无法自动带出批次
  static Future<bool> getPtypeAutoBatch(
      List<GoodsDetailDto> goodsList, BuildContext context) async {
    if (SpTool.getSystemConfig().enabledPtypeBatchSort == true) {
      Map<String, GoodsDetailDto> map = {};
      int comboRowId = DateTime.now().millisecondsSinceEpoch;
      List<PtypeBatchParamQtyDetailDto> list = goodsList
          .where((element) =>
              //排除套餐行
              !element.comboRow &&
              //排除非批次商品
              element.batchenabled &&
              //排除已经绑定批次的商品
              !GoodsTool.isGoodsBindWithBatch(element))
          .map((e) {
        comboRowId++;
        //这里获取批次的接口由于信息不全，需要手动赋值comboRowId，用来区分商品
        map[comboRowId.toString()] = e;
        return PtypeBatchParamQtyDetailDto(
            comboRowId: comboRowId.toString(),
            ptypeId: e.ptypeId,
            count: 1,
            skuId: e.skuId,
            protectDays: e.protectDays,
            costMode: e.costMode);
      }).toList();
      if (list.isNotEmpty) {
        List<PtypeBatchDto> batchList = await BillModel.getPtypeBatch(
                context, SpTool.getStoreInfo()!.ktypeId!, list)
            .onError((error, stackTrace) => []);
        for (var batchPtype in batchList) {
          // GoodsDetailDto goods = goodsList.firstWhere(
          //     (element) =>
          //         StringUtil.equal(element.ptypeId, batchPtype.ptypeId ?? "") &&
          //         StringUtil.equal(element.skuId ?? "", batchPtype.skuId ?? "") &&
          //         element.batchenabled &&
          //         !element.comboRow &&
          //         element.comboRowId == batchPtype.comboRowId,
          //     orElse: () => GoodsDetailDto());
          GoodsDetailDto? goods = map[batchPtype.comboRowId ?? ""];
          if (goods != null) {
            goods.batchNo = batchPtype.batchNo ?? "";
            goods.batchId = batchPtype.batchId;
            goods.batchPrice = batchPtype.batchPrice ?? 0;
            goods.costId = batchPtype.costId;
            goods.produceDate = batchPtype.produceDate;
            goods.expireDate = batchPtype.expireDate;
          }
        }
        return true;
      }
    }
    return false;
  }

  static Future<String> openQRScanPage() async {
    var options = const ScanOptions(
        android: AndroidOptions(aspectTolerance: 0.5, useAutoFocus: true),
        //(默认已配)添加Android自动对焦
        autoEnableFlash: false,
        //true打开闪光灯, false关闭闪光灯
        strings: {
          'cancel': '取消',
          'flash_on': '开闪光灯',
          'flash_off': '关闪光灯'
        } //标题栏添加闪光灯按钮、退出按钮
        );
    var result = await BarcodeScanner.scan(options: options);
    String qrcode = result.rawContent; //扫描到的条形码或二维码内容
    return Future(() => qrcode);
  }
}
