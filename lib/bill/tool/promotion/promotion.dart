import 'dart:math';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:halo_pos/bill/tool/bill_goods_util.dart';
import 'package:halo_pos/bill/tool/promotion/preferential.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../common/tool/sp_tool.dart';
import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../vip/utils/svip_util.dart';
import '../../../vip/utils/vip_util.dart';
import '../../entity/bill_promotion_info_dto.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/preferential_dto.dart';
import '../goods_tool.dart';
import 'price.dart';
import 'promotion_base.dart';
import 'rule_model.dart';

///是否自动选择促销赠品
bool autoChoosePromotionGift = false;
//region 枚举

//region 促销类型
///促销类型
///0 商品特价 1订单满减 2第二件半价 3满件赠 4积分兑换 5满额赠
enum PromotionType {
  ///商品特价
  specialPrice,

  ///订单满减
  orderFullReduction,

  ///第二件半价
  halfPrice,

  ///满件赠
  fullCountGift,

  ///积分兑换
  scoreExchange,

  ///满额赠
  fullAmountGift,

  ///组合购
  combinationBuy,
}

extension PromotionTypeExtension on PromotionType {
  String get name1 {
    switch (this) {
      case PromotionType.specialPrice:
        return "商品特价";
      case PromotionType.orderFullReduction:
        return "订单满减";
      case PromotionType.halfPrice:
        return "第二件半价";
      case PromotionType.fullCountGift:
        return "满件赠";
      case PromotionType.scoreExchange:
        return "积分兑换";
      case PromotionType.fullAmountGift:
        return "满额赠";
      case PromotionType.combinationBuy:
        return "组合购";
    }
  }

  int get value {
    switch (this) {
      case PromotionType.specialPrice:
        return 0;
      case PromotionType.orderFullReduction:
        return 1;
      case PromotionType.halfPrice:
        return 2;
      case PromotionType.fullCountGift:
        return 3;
      case PromotionType.scoreExchange:
        return 4;
      case PromotionType.fullAmountGift:
        return 5;
      case PromotionType.combinationBuy:
        return 6;
    }
  }

  ///优先级，用于排序
  ///优先级：0 组合购 1 商品特价 2 第二件半价 3满额赠 4满件赠 5订单满减
  ///积分兑换，不在促销流程中，不需要
  num get priority {
    switch (this) {
      case PromotionType.combinationBuy:
        return 0;
      case PromotionType.specialPrice:
        return 1;
      case PromotionType.halfPrice:
        return 2;
      case PromotionType.fullAmountGift:
        return 3;
      case PromotionType.fullCountGift:
        return 4;
      case PromotionType.orderFullReduction:
        return 5;
      case PromotionType.scoreExchange:
        return double.maxFinite;
    }
  }
}
//endregion 促销类型

//region 促销适配顾客类型
///促销适配顾客类型
///1 全部 2仅会员 3会员等级
enum PromotionRangType {
  ///全部
  all,

  ///仅会员
  onlyVip,

  ///会员等级
  vipLevel
}

extension PromotionRangTypeExtension on PromotionRangType {
  String get name1 {
    switch (this) {
      case PromotionRangType.all:
        return "全部";
      case PromotionRangType.onlyVip:
        return "仅会员";
      case PromotionRangType.vipLevel:
        return "会员等级";
    }
  }

  int get value {
    switch (this) {
      case PromotionRangType.all:
        return 1;
      case PromotionRangType.onlyVip:
        return 2;
      case PromotionRangType.vipLevel:
        return 3;
    }
  }
}
//endregion 促销适配顾客类型

//region 促销策略类型
///促销下策略类型,0=促销商品 1=促销方式  2=促销条件
enum PromotionStrategyType {
  ///促销商品，即参加促销的商品范围,参见[PromotionGoodsScope]
  promotionGoods,

  ///促销方式
  ///当促销类型[BillPromotionInfoDto.promotionType]为商品特价[PromotionType.specialPrice]：
  ///代表特价类型,参见[SpecialPriceType]
  ///
  ///当促销类型[BillPromotionInfoDto.promotionType]为满件赠[PromotionType.fullCountGift]和满额赠[PromotionType.fullAmountGift]：
  ///代表赠品类型,参见[PromotionGiftType]
  promotionWay,

  ///促销条件
  ///当促销类型[BillPromotionInfoDto.promotionType]为满件赠[PromotionType.fullCountGift]和满额赠[PromotionType.fullAmountGift]：
  ///促销计算方式
  promotionCondition
}

extension PromotionStrategyTypeExtension on PromotionStrategyType {
  String get name1 {
    switch (this) {
      case PromotionStrategyType.promotionGoods:
        return "促销商品";
      case PromotionStrategyType.promotionWay:
        return "促销方式";
      case PromotionStrategyType.promotionCondition:
        return "促销条件";
    }
  }

  int get value {
    switch (this) {
      case PromotionStrategyType.promotionGoods:
        return 0;
      case PromotionStrategyType.promotionWay:
        return 1;
      case PromotionStrategyType.promotionCondition:
        return 2;
    }
  }
}
//endregion 促销策略类型

//region 参加促销的商品范围
///参加促销的商品范围，对应[RuleModel.type]字段
///当促销策略类型[StrategyBean.strategyType]为促销商品[PromotionStrategyType.promotionGoods]时
enum PromotionGoodsScope {
  ///商品范围-全部商品
  allGoods,

  ///商品范围-部分商品
  partGoods,

  ///商品范围-商品分类
  goodsCategory,

  ///商品范围-排除商品
  excludeGoods,

  ///商品范围-权益卡
  rightsCard,

  ///商品范围-优惠券
  coupon,

  ///商品范围-储值
  storeValue,
}

extension PromotionGoodsScopeExtension on PromotionGoodsScope {
  String get name1 {
    switch (this) {
      case PromotionGoodsScope.allGoods:
        return "全部商品";
      case PromotionGoodsScope.partGoods:
        return "部分商品";
      case PromotionGoodsScope.goodsCategory:
        return "商品分类";
      case PromotionGoodsScope.excludeGoods:
        return "排除商品";
      case PromotionGoodsScope.rightsCard:
        return "权益卡";
      case PromotionGoodsScope.coupon:
        return "优惠券";
      case PromotionGoodsScope.storeValue:
        return "储值";
    }
  }

  int get value {
    switch (this) {
    //商品范围-全部商品
      case PromotionGoodsScope.allGoods:
        return 0;
    //商品范围-部分商品
      case PromotionGoodsScope.partGoods:
        return 1;
    //商品范围-商品分类
      case PromotionGoodsScope.goodsCategory:
        return 2;
    //商品范围-排除商品
      case PromotionGoodsScope.excludeGoods:
        return 3;
    //商品范围-权益卡
      case PromotionGoodsScope.rightsCard:
        return 4;
    //商品范围-优惠券
      case PromotionGoodsScope.coupon:
        return 5;
    //商品范围-储值
      case PromotionGoodsScope.storeValue:
        return 6;
    }
  }
}
//endregion 参加促销的商品范围

//region 特价类型
///特价类型,对应[RuleModel.type]字段
///当促销类型[BillPromotionInfoDto.promotionType]为商品特价[PromotionType.specialPrice]
///并且，当促销策略类型[StrategyBean.strategyType]为促销方式[PromotionStrategyType.promotionWay]时
enum SpecialPriceType {
  ///特价商品-促销价
  price,

  ///特价商品-促销折扣
  discount,
}

extension SpecialPriceTypeExtension on SpecialPriceType {
  int get value {
    switch (this) {
    //特价商品-促销价
      case SpecialPriceType.price:
        return 0;
    //特价商品-促销折扣
      case SpecialPriceType.discount:
        return 1;
    }
  }
}
//endregion 特价类型

//region 满件赠/满额赠枚举
//region 参加促销的赠品类型
///参加促销的赠品类型，对应[RuleModel.type]字段，如商品或优惠券
///当促销类型[BillPromotionInfoDto.promotionType]为满件赠[PromotionType.fullCountGift]和满额赠[PromotionType.fullAmountGift]
///并且当促销策略类型[StrategyBean.strategyType]为促销商品[PromotionStrategyType.promotionWay]时
enum PromotionGiftType {
  ///商品
  goods,

  ///优惠券
  coupon,
}

extension PromotionGiftTypeExtension on PromotionGiftType {
  String get name1 {
    switch (this) {
      case PromotionGiftType.goods:
        return "商品";
      case PromotionGiftType.coupon:
        return "优惠券";
    }
  }

  int get value {
    switch (this) {
    //商品
      case PromotionGiftType.goods:
        return 1;
    //优惠券
      case PromotionGiftType.coupon:
        return 6;
    }
  }
}
//endregion 参加促销的赠品类型

//region 促销赠送的赠品范围
///促销赠送的赠品范围，对应[RuleModel.type]字段 分为买赠同品(赠品类型必须为商品)，自选赠品和指定赠品(固定赠品)
///当促销策略类型[StrategyBean.strategyType]为促销商品[PromotionStrategyType.promotionCondition]时
enum PromotionGiftScope {
  ///买赠同品
  ///只有赠品类型[PromotionGiftType]为商品[PromotionGiftType.goods]时，才支持买赠同品
  ///只有商品计数方式[PromotionGoodsCountType]为按促销内单个商品计数[PromotionGoodsCountType.singleGoods]时，才支持买赠同品
  sameGoods,

  ///选择赠品
  chooseGoods,

  ///指定赠品(固定赠品)
  specifiedGoods,
}

extension PromotionGiftScopeExtension on PromotionGiftScope {
  String get name1 {
    switch (this) {
      case PromotionGiftScope.sameGoods:
        return "买赠同品";
      case PromotionGiftScope.chooseGoods:
        return "自选赠品";
      case PromotionGiftScope.specifiedGoods:
        return "指定赠品";
    }
  }

  String get key => value.toString();

  int get value {
    switch (this) {
    //买赠同品
      case PromotionGiftScope.sameGoods:
        return 0;
    //自选赠品
      case PromotionGiftScope.chooseGoods:
        return 1;
    //指定赠品
      case PromotionGiftScope.specifiedGoods:
        return 2;
    }
  }
}
//endregion 促销赠送的赠品范围

//region 满件赠/满额赠等商品计数方式

///满件/满额优惠方式中商品计数的方式,对应[RuleModel.typeWay]
///分为按单个商品计算和按促按活动商品总数量计算
///当促销策略类型[StrategyBean.strategyType]为促销商品[PromotionStrategyType.promotionCondition]时
enum PromotionGoodsCountType {
  ///按单个商品计数
  singleGoods,

  ///按促销内多个商品计数
  ///在这种条件下，赠品范围[PromotionGiftScope]将不支持[PromotionGiftScope.sameGoods]买赠同品
  multipleGoods,
}

extension PromotionGoodsCountTypeExtension on PromotionGoodsCountType {
  String get name1 {
    switch (this) {
      case PromotionGoodsCountType.singleGoods:
        return "按单个商品计算";
      case PromotionGoodsCountType.multipleGoods:
        return "按活动商品总数量计算";
    }
  }

  int get value {
    switch (this) {
    //按单个商品计数
      case PromotionGoodsCountType.singleGoods:
        return 1;
    //按活动商品总数量计算
      case PromotionGoodsCountType.multipleGoods:
        return 0;
    }
  }
}

//endregion 满件赠/满额赠等商品计数方式
//endregion 满件赠/满额赠枚举

//region 订单满减枚举
//region 订单满减计数方式
///订单满减计数方式
///分为:满金额/满件
enum BillPromotionCountType {
  ///满金额
  amount,

  ///满件
  qty,
}

extension BillPromotionCountTypeExtension on BillPromotionCountType {
  int get value {
    switch (this) {
    //满金额
      case BillPromotionCountType.amount:
        return 0;
    //满件
      case BillPromotionCountType.qty:
        return 1;
    }
  }
}

//endregion 订单满减计数方式

//region 订单满减优惠类型
///订单满减优惠类型
///分为：金额/折扣
enum BillPromotionPreferentialType {
  ///金额
  total,

  ///折扣
  discount,
}

extension BillPromotionPreferentialTypeExtension
on BillPromotionPreferentialType {
  int get value {
    switch (this) {
    //金额
      case BillPromotionPreferentialType.total:
        return 0;
    //折扣
      case BillPromotionPreferentialType.discount:
        return 1;
    }
  }
}
//endregion 订单满减优惠类型
//endregion 订单满减枚举

//endregion 枚举

//region 工具类
///促销工具类
class PromotionUtil {
  PromotionUtil._();

  static bool checkPromotionGoodsScoreVerify(GoodsDetailDto goods) {
    if (goods.gift) return false;
    //只统计套餐行，排除套餐明细行
    if (GoodsTool.isComboDetail(goods)) return false;
    if (!goods.calIntegral) {
      return false;
    }
    //此处验证无效，屏蔽掉 未执行促销，则无需验证促销排除累计积分
    // if (!goods.preferentialHelp.containsKey(Preferential.goodsPromotion.name) &&
    //     (goods.preferentialHelp[Preferential.billPromotion.name]?.total ?? 0) <=
    //         0) {
    //   return true;
    // }
    return true;
  }

  ///清除商品上的促销信息
  ///[cleanHints] 是否清除促销提示，当第二件半价赠品不再满足第二件半价时，应该保留其促销提示
  static void cleanGoodsPromotion(GoodsDetailDto goods,
      {bool cleanHints = true}) {
    //移除促销id
    goods.promotionId = "";
    goods.promotionType = -1;
    goods.calIntegral = true;
    goods.vipRights = true;
    goods.joinOrder = true;
    goods.promotionGift = false;
    goods.promotionGiftScope = PromotionGiftScope.chooseGoods.value;
    //移除促销标记
    if (cleanHints) {
      goods.promotionHints = null;
    }
    //重置优惠辅助
    goods.preferentialHelp.remove(Preferential.goodsPromotion.name);
    goods.preferentialHelp.remove(Preferential.billPromotion.name);
  }

  ///清除单据中的促销信息
  ///[cleanGoods] 是否清除商品上的促销信息
  ///[cleanRecord] 是否清除促销记录
  static void cleanBillPromotion(GoodsBillDto billDto,
      {bool cleanGoods = true,
        bool cleanRecord = true,
        bool cleanCouponGift = true}) {
    billDto.preferentialHelp.remove(Preferential.billPromotion.name);
    billDto.preferentialHelp.remove(Preferential.goodsPromotion.name);
    billDto.promotionHints = null;
    if (cleanRecord) {
      billDto.promotionGiftRecord = null;
    }
    if (cleanCouponGift) {
      billDto.giftCouponList.clear();
    }
    if (cleanGoods && billDto.outDetail.isNotEmpty) {
      billDto.outDetail.removeWhere((goods) {
        //保留第二件半价赠品
        if (goods.gift && goods.promotionGift) {
          return true;
        }
        cleanGoodsPromotion(goods);
        return false;
      });
    }
  }

  ///处理商品级别的促销
  static void startGoodsPromotion({
    required Map<int, List<BillPromotionInfoDto>> promotionMap,
    required List<GoodsDetailDto> goodsList,
    required VipWithLevelAssertsRightsCardDTO? vipInfo,
    required GoodsBillDto billDto,
    bool? isVip,
  }) {
    isVip ??= isValidVip(vipInfo);
    //源商品列表
    final originalGoodsList = goodsList;
    billDto.preferentialHelp.remove(Preferential.goodsPromotion.name);
    //促销赠品，key为促销类型
    Map<int, List<GoodsDetailDto>> promotionGiftMap = {};
    //套餐明细行,key为comboRowParId
    Map<String, List<GoodsDetailDto>> comboDetailMap = {};
    //经过处理的商品列表，排除不需要执行促销的商品标记
    goodsList = goodsList.where((element) {
      bool isComboDetail = GoodsTool.isComboDetail(element);
      //排除套餐明细行，并记录
      if (isComboDetail) {
        comboDetailMap
            .putIfAbsent(element.comboRowParId, () => [])
            .add(element);
      }
      //排除手工改价
      if (element.manualPrice) {
        return false;
      }
      //排除促销赠品，并记录
      if (element.promotionGift) {
        if (!isComboDetail) {
          promotionGiftMap
              .putIfAbsent(element.promotionType, () => [])
              .add(element);
        }
        //对于第二件半价的半价赠品，应该参与促销
        //对于参加组合购的商品，应该参加促销
        if (element.promotionType != PromotionType.halfPrice.value &&
            element.promotionType != PromotionType.combinationBuy.value) {
          return false;
        }
      }
      //排除其他赠品
      if (element.gift) {
        return false;
      }
      //除了第二件半价赠品，其他的清除促销数据
      // if (!element.promotionGift) {
      cleanGoodsPromotion(element);
      // }
      return !isComboDetail;
    }).toList();
    //可以参加促销的商品为空，清空促销
    if (goodsList.isEmpty) {
      cleanBillPromotion(billDto);
    }
    //除了订单满减和积分兑换，依次创建其他类型的促销处理类
    List<PromotionHandler> promotionHandlerList = PromotionType.values
        .where((type) =>
    type != PromotionType.orderFullReduction &&
        type != PromotionType.scoreExchange)
        .map((e) {
      final handler = PromotionHandler.create(
          promotionList: promotionMap[e.value] ?? [],
          promotionType: e,
          isVip: isVip!,
          billDto: billDto,
          originalGoodsList: originalGoodsList,
          comboDetailsMap: comboDetailMap);
      if (handler is PromotionGoodsGiftHandlerMixin) {
        handler.currentPromotionTypeGoodsGiftList =
            promotionGiftMap[handler.promotionType.value] ?? [];
      }
      return handler;
    }).toList()
    //按照优先级排序
      ..sort((a, b) =>
          a.promotionType.priority.compareTo(b.promotionType.priority));
    //遍历促销处理类，依次处理
    for (var handler in promotionHandlerList) {
      goodsList = handler.handle(goodsList);
    }
  }

  ///处理订单满减
  static void startBillPromotion({
    required List<BillPromotionInfoDto> promotionList,
    required List<GoodsDetailDto> goodsList,
    required VipWithLevelAssertsRightsCardDTO? vipInfo,
    required GoodsBillDto billDto,
    bool? isVip,
  }) {
    isVip ??= isValidVip(vipInfo);
    //源商品列表
    final originalGoodsList = goodsList;
    cleanBillPromotion(billDto,
        cleanGoods: false, cleanRecord: false, cleanCouponGift: false);
    //套餐明细行,key为comboRowParId
    Map<String, List<GoodsDetailDto>> comboDetailMap = {};
    //经过处理的商品列表，排除不需要执行促销的商品标记
    goodsList = goodsList.where((element) {
      //重置优惠辅助
      element.preferentialHelp.remove(Preferential.billPromotion.name);
      //排除手工改价
      if (element.manualPrice) {
        return false;
      }
      //排除套餐明细行，并记录
      if (StringUtil.isNotZeroOrEmpty(element.comboRowParId)) {
        comboDetailMap
            .putIfAbsent(element.comboRowParId, () => [])
            .add(element);
        return false;
      }
      //排除促销赠品
      if (BillGoodsUtil.isPromotionGift(element)) {
        return false;
      }
      //排除其他赠品
      if (element.gift) {
        return false;
      }
      //排除已经执行促销，且不与订单满减叠加的商品
      if (!element.joinOrder) {
        return false;
      }
      return true;
    }).toList();
    PromotionHandler.create(
        promotionList: promotionList,
        promotionType: PromotionType.orderFullReduction,
        isVip: isVip,
        billDto: billDto,
        originalGoodsList: originalGoodsList,
        comboDetailsMap: comboDetailMap)
        .handle(goodsList);
  }

  ///开始促销
  static void startPromotion(
      GoodsBillDto billDto, VipWithLevelAssertsRightsCardDTO? vipInfo,
      {bool? isVip}) {
    if (billDto.outDetail.isEmpty) {
      cleanBillPromotion(billDto);
      return;
    }
    isVip ??= isValidVip(vipInfo);
    List<BillPromotionInfoDto> promotionList = SpTool.getPromotionList();
    // if (promotionList.isEmpty) {
    //   return;
    // }
    //根据会员信息和当前日期来筛选促销
    _filterPromotionByVipAndExpiredDate(vipInfo, promotionList);
    // if (promotionList.isEmpty) {
    //   return;
    // }
    //对促销进行分类
    Map<int, List<BillPromotionInfoDto>> promotionMap = {};
    for (var promotion in promotionList) {
      promotionMap
          .putIfAbsent(promotion.promotionType, () => [])
          .add(promotion);
    }
    //处理商品级别促销
    startGoodsPromotion(
        promotionMap: promotionMap,
        goodsList: billDto.outDetail,
        vipInfo: vipInfo,
        billDto: billDto,
        isVip: isVip);
    //再处理订单满减
    startBillPromotion(
        promotionList:
        promotionMap[PromotionType.orderFullReduction.value] ?? [],
        goodsList: billDto.outDetail,
        vipInfo: vipInfo,
        billDto: billDto,
        isVip: isVip);
  }

  ///根据会员信息，筛选促销
  ///排除过期的促销
  ///[vipInfo] 会员信息
  ///[promotionList] 促销列表
  static void _filterPromotionByVipAndExpiredDate(
      VipWithLevelAssertsRightsCardDTO? vipInfo,
      List<BillPromotionInfoDto> promotionList) {
    //是否是会员
    bool isVip = vipInfo?.vip?.id?.isNotEmpty ?? false;
    //会员等级id
    String? levelId;
    //会员类型 0 免费会员，1 付费会员
    int? vipType;
    //付费会员是否过期
    bool expired = false;
    if (isVip) {
      levelId = vipInfo?.level?.levelId;
      vipType = vipInfo?.level?.vipType == true ? 1 : 0;
      expired = isVipExpired(vipInfo?.vip?.validDate, vipType: vipType);
    }
    DateTime now = DateTime.now();
    promotionList.removeWhere((promotion) =>
    !_isPromotionActive(promotion, now) ||
        !_isPromotionCanUseByUser(
          isVip: isVip,
          levelId: levelId,
          vipType: vipType,
          expired: expired,
          promotion: promotion,
        ));
  }

  ///促销是否可用
  ///当过期或未开始，不可用
  ///当不在活动时间内，不可用
  static bool _isPromotionActive(BillPromotionInfoDto promotion,
      [DateTime? now]) {
    now ??= DateTime.now();
    DateTime? startDate = DateTime.tryParse(promotion.startDate);
    DateTime? endDate = DateTime.tryParse(promotion.endDate);
    if (startDate == null || endDate == null) {
      return false;
    }
    if (now.isBefore(startDate) || now.isAfter(endDate)) {
      return false;
    }
    if (promotion.startTime?.isNotEmpty != true ||
        promotion.endTime?.isNotEmpty != true) {
      return false;
    }
    DateTime? startTime = _getPromotionActivityTime(promotion.startTime!, now);
    DateTime? endTime = _getPromotionActivityTime(promotion.endTime!, now);
    if (startTime == null || endTime == null) {
      return false;
    }
    return !now.isBefore(startTime) && !now.isAfter(endTime);
  }

  ///获取促销活动时间，因为返回的只有时分秒没有年月日，所以需要和当前日期组合一下
  static DateTime? _getPromotionActivityTime(String time, [DateTime? dateNow]) {
    dateNow ??= DateTime.now();
    return DateTime.tryParse("${dateNow.year}"
        "-${dateNow.month.toString().padLeft(2, "0")}"
        "-${dateNow.day.toString().padLeft(2, "0")}"
        " $time");
  }

  ///判断当前顾客是否可以使用该促销
  ///[isVip] 是否是会员
  ///[levelId] 会员等级id
  ///[vipType] 会员类型 0 免费会员，1 付费会员
  ///[expired] 付费会员是否过期
  ///[promotion] 促销
  static bool _isPromotionCanUseByUser({
    required bool isVip,
    required String? levelId,
    required int? vipType,
    required bool expired,
    required BillPromotionInfoDto promotion,
  }) {
    //排除积分兑换
    if (promotion.promotionType == PromotionType.scoreExchange.value) {
      return false;
    }
    //促销会员限定
    if (promotion.rangType == PromotionRangType.onlyVip.value ||
        promotion.rangType == PromotionRangType.vipLevel.value) {
      if (!isVip || expired) return false;
      //对于会员限定，分为限定会员类型，和限定会员等级两种
      if (promotion.rangValueList?.isNotEmpty != true) {
        return false;
      }
      //按会员类型判断
      if (promotion.rangType == PromotionRangType.onlyVip.value) {
        //这里记录的type 是会员类型 0为免费，1为付费。value是是否启用
        return promotion.rangValueList
            ?.any((element) => element.vipType == vipType) ==
            true;
      }
      //按会员等级判断
      else if (promotion.rangType == PromotionRangType.vipLevel.value) {
        return promotion.rangValueList
        //判断当前会员等级是否在规则中
            ?.any((rangValue) => rangValue.rangId == levelId) ==
            true;
      }
    }
    return true;
  }

  static void _cleanBatchInfo(GoodsDetailDto goods) {
    goods.batchNo = "";
    goods.expireDate = null;
    goods.produceDate = null;
    goods.protectDays = null;
    goods.costId = null;
    goods.serialNoList = [];
  }

  ///生成一个赠品
  ///[isGift] 是否是赠品
  ///[calculateTotal] 是否重新计算金额,对于套餐明细行，金额是由套餐行*明细行比例得到，所以无需计算
  ///[promotionGiftScope] 赠品范围
  static GoodsDetailDto generateGiftFromGoods(GoodsDetailDto goods,
      {required BillPromotionInfoDto promotion,
        num unitQty = 1,
        bool isGift = true,
        bool calculateTotal = true,
        bool cleanBatchInfo = true,
        int? promotionGiftScope}) {
    promotionGiftScope ??= PromotionGiftScope.chooseGoods.value;
    GoodsDetailDto gift = GoodsDetailDto.fromMap(goods.toJson());
    gift.unitQty = unitQty;
    gift.gift = isGift;
    //对于促销的赠品，都是折扣为10折，优惠金额记到促销中
    gift.discount = 1;
    gift.promotionGift = true;
    gift.promotionHints = null;
    gift.detailId = null;
    gift.manualPrice = false;
    gift.preferentialHelp.clear();
    //赠品不含批次
    if (cleanBatchInfo) {
      _cleanBatchInfo(gift);
    }
    gift.promotionGiftScope = promotionGiftScope;
    gift.serialNoList.clear();
    setGoodsPromotion(goods: gift, promotion: promotion);
    //重新计算金额
    if (calculateTotal) {
      GoodsQtyUtil.onQtyChange(gift, unitQty);
    }
    return gift;
  }

  ///生成赠品套餐明细
  ///[promotionGiftScope] 赠品范围
  static List<GoodsDetailDto> generateGiftComboDetailsFromGoods(
      GoodsDetailDto comboGift,
      List<GoodsDetailDto> comboDetails,
      BillPromotionInfoDto promotion,
      {bool cleanBatchInfo = true,
        void Function(GoodsDetailDto goods, GoodsDetailDto gift)?
        onGenerateGift}) {
    comboGift.comboRowId = DateTime.now().millisecondsSinceEpoch.toString();
    List<GoodsDetailDto> result = [];

    num comboTotal = comboGift.currencyTotal;
    Decimal detailTotalSum = Decimal.zero;

    comboDetails = comboDetails.toList()
      ..sort((a, b) => a.comboShareScale.compareTo(b.comboShareScale));
    for (var i = 0; i < comboDetails.length; i++) {
      final comboDetail = comboDetails[i];
      num unitQty = SystemConfigTool.doubleMultipleToDecimal(
          comboGift.unitQty, comboDetail.comboQtyRate, BillDecimalType.QTY);

      final giftComboDetail = generateGiftFromGoods(comboDetail,
          promotion: promotion,
          isGift: comboGift.gift,
          unitQty: unitQty,
          calculateTotal: false,
          promotionGiftScope: comboGift.promotionGiftScope,
          cleanBatchInfo: cleanBatchInfo)
        ..comboId = comboGift.comboId
        ..comboRowParId = comboGift.comboRowId
        ..comboRowId = "${comboGift.comboRowId}$i";
      onGenerateGift?.call(comboDetail, giftComboDetail);
      //计算套餐明细行金额和价格
      num comboDetailTotal;
      //非套餐中最后一个商品，直接按 套餐金额x比例 换算
      if (i < comboDetails.length - 1) {
        num scale =
        MathUtil.divideDec(giftComboDetail.comboShareScale, 100).toDouble();
        comboDetailTotal = SystemConfigTool.doubleMultipleToDecimal(
            comboTotal, scale, BillDecimalType.TOTAL);
        detailTotalSum += Decimal.parse(comboDetailTotal.toString());
      } else {
        comboDetailTotal = SystemConfigTool.doubleSubtractionToDecimal(
            comboTotal, detailTotalSum.toDouble(), BillDecimalType.TOTAL);
      }
      giftComboDetail.currencyTotal = comboDetailTotal;
      GoodsTotalUtil.onDiscountTotalChange(
          giftComboDetail, giftComboDetail.currencyTotal);
      result.add(giftComboDetail);
    }
    return result;
  }

  ///检查已有赠品是否是赠品列表中的赠品（本地赠品和服务端返回赠品的实现方式不同）
  static bool compareGift(
      PromotionGift gift1, PromotionPtype gift2, int giftType) {
    if (giftType == PromotionGiftType.goods.value) {
      return gift1 is GoodsDetailDto &&
          gift1.ptypeId == gift2.ptypeId &&
          gift1.skuId == gift2.skuId &&
          gift1.unitId == gift2.unitId;
    } else if (giftType == PromotionGiftType.coupon.value) {
      return gift1 is CouponGift && gift1.pid == gift2.pid;
    }
    return false;
  }

  ///通过赠品找到促销中对应的赠品对象
  static PromotionPtype? findGiftListBeanByGift(
      PromotionGift gift, BillPromotionInfoDto promotion, int giftType) {
    if (gift.promotionId != promotion.id) return null;
    if (gift.promotionGiftType != giftType) return null;
    return promotion.giftPtypeList
        .firstWhereOrNull((element) => compareGift(gift, element, giftType));
  }

  ///校验赠品是否在促销中
  ///买赠同品无需校验
  static bool isGiftInPromotion(
      PromotionGift gift, BillPromotionInfoDto promotion, int giftType) {
    return findGiftListBeanByGift(gift, promotion, giftType) != null;
  }

  ///将赠品添加到促销商品后面
  /// [isAdd] 是否是新增的赠品
  /// [giftList] 赠品
  /// [index] 要插入的位置
  static void insertGiftAfterIndex(
      {required bool isAdd,
        required List<GoodsDetailDto> originalGoodsList,
        required List<GoodsDetailDto> giftList,
        required int index}) {
    if (!isAdd) {
      final giftSet = giftList.toSet();
      originalGoodsList.removeWhere((e) => giftSet.contains(e));
    }
    index = index + 1;
    if (index > originalGoodsList.length) index = originalGoodsList.length;
    originalGoodsList.insertAll(index, giftList);
  }

  ///记录促销优惠
  static PreferentialDto setPromotionPreferential(
      {required GoodsDetailDto goods,
        required String promotionId,
        required num total,
        Preferential preferential = Preferential.goodsPromotion}) {
    //记录商品优惠辅助信息
    return PreferentialHelper.setGoodsPreferential(goods, preferential, total,
        typeId: promotionId);
  }

  ///记录优惠辅助信息
  static void setPreferential(GoodsDetailDto goods,
      BillPromotionInfoDto promotion, num preferentialTotal,
      {Preferential preferential = Preferential.goodsPromotion,
        List<GoodsDetailDto>? comboDetails}) {
    //套餐不记录优惠信息
    if (goods.comboRow) {
      ComboPreferentialTool.handleComboDetail(goods, comboDetails ?? [],
          typeId: promotion.id,
          totalGetter: (g) => g.promotedTotal,
          preferential: preferential,
          totalSetter: (g, total) {
            GoodsTotalUtil.onPromotedTotalChange(g, total);
            PromotionUtil.setGoodsPromotion(goods: goods, promotion: promotion);
          });
    } else {
      //优惠辅助表记录
      setPromotionPreferential(
          goods: goods, promotionId: promotion.id, total: preferentialTotal);
    }
  }

  ///执行促销后，重新计算商品，并记录优惠辅助信息
  ///对于套餐，需要找到对应的套餐明细行，计算明细行的优惠金额，并记录优惠辅助
  static void setTotalAndSetPreferential(
      GoodsDetailDto goods, BillPromotionInfoDto promotion, num promotedTotal,
      {List<GoodsDetailDto>? comboDetails}) {
    //商品关联当前促销
    PromotionUtil.setGoodsPromotion(goods: goods, promotion: promotion);
    num lastPromotedTotal = goods.promotedTotal;
    //计算优惠前价格，优惠分摊，最终价格
    GoodsTotalUtil.onPromotedTotalChange(goods, promotedTotal);
    //计算优惠金额
    num preferentialTotal = SystemConfigTool.doubleSubtractionToDecimal(
        lastPromotedTotal, goods.promotedTotal, BillDecimalType.TOTAL);
    //记录优惠辅助
    setPreferential(goods, promotion, preferentialTotal,
        comboDetails: comboDetails);
  }

  ///执行促销后，重新计算商品，并记录优惠辅助信息
  ///对于套餐，需要找到对应的套餐明细行，计算明细行的优惠金额，并记录优惠辅助
  static void setPriceAndSetPreferential(
      GoodsDetailDto goods, BillPromotionInfoDto promotion, num promotedPrice,
      {List<GoodsDetailDto>? comboDetails}) {
    //商品关联当前促销
    PromotionUtil.setGoodsPromotion(goods: goods, promotion: promotion);
    num lastPromotedTotal = goods.promotedTotal;
    //计算优惠前价格，优惠分摊，最终价格
    GoodsPriceUtil.onPromotedPriceChange(goods, promotedPrice);
    //计算优惠金额
    num preferentialTotal = SystemConfigTool.doubleSubtractionToDecimal(
        lastPromotedTotal, goods.promotedTotal, BillDecimalType.TOTAL);
    //记录优惠辅助
    setPreferential(goods, promotion, preferentialTotal,
        comboDetails: comboDetails);
  }

  ///添加多个赠品
  ///[giftList] 赠品列表
  ///需要先移除再重新添加
  static void addMultipleGoodsGift({
    required List<GoodsDetailDto> originalGoodsList,
    required List<GoodsDetailDto> giftList,
    required BillPromotionInfoDto promotion,
    required Map<String, List<GoodsDetailDto>> comboDetailsMap,
    bool calculatePreferential = true,
  }) {
    List<GoodsDetailDto> goodsGiftList = giftList.expand((goods) {
      //重新计算金额
      List<GoodsDetailDto>? comboDetails;
      if (goods.comboRow) {
        comboDetails = comboDetailsMap[goods.comboRowId];
      }
      if (calculatePreferential) {
        //计算赠品优惠辅助信息
        setGiftPreferential(promotion, goods, comboDetails);
      }
      return <GoodsDetailDto>[goods, if (comboDetails != null) ...comboDetails];
    }).toList();
    //将赠品插入到促销最后一个商品之后
    int index = -1;
    for (var i = originalGoodsList.length - 1; i >= 0; i--) {
      var element = originalGoodsList[i];
      if (!element.gift && element.promotionId == promotion.id) {
        index = i;
        break;
      }
    }
    if (index < 0) {
      index = originalGoodsList.length - 1;
    }
    insertGiftAfterIndex(
        isAdd: false,
        originalGoodsList: originalGoodsList,
        giftList: goodsGiftList,
        index: index);
  }

  ///设置赠品优惠辅助信息
  static void setGiftPreferential(
      BillPromotionInfoDto promotion, GoodsDetailDto goods,
      [List<GoodsDetailDto>? comboDetails]) {
    //重新计算金额
    GoodsQtyUtil.onQtyChange(goods, goods.unitQty);
    comboDetails ??= [];
    //套餐的优惠辅助信息记录在明细行
    if (goods.comboRow) {
      //计算套餐行的金额
      ComboPreferentialTool.handleComboDetail(goods, comboDetails,
          totalGetter: (g) => g.currencyTotal,
          totalSetter: (g, total) =>
              GoodsTotalUtil.onCurrencyTotalChange(g, total));
    }
    //将促销后价格设置为0，并且记录优惠辅助信息
    setPriceAndSetPreferential(goods, promotion, 0, comboDetails: comboDetails);
  }

  ///生成套餐明细赠品
  static List<GoodsDetailDto> generatePromotionComboDetailGift(
      PromotionPtype giftBean, GoodsDetailDto combo) {
    final details = giftBean.comboDetail ?? [];
    List<GoodsDetailDto> comboDetails = [];
    for (var i = 0; i < details.length; ++i) {
      GoodsDetailDto detail = details[i].changeModel()
        ..gift = true
        ..comboRowId = "${combo.comboRowId}$i"
        ..comboRowParId = combo.comboRowId
        ..comboId = combo.comboId
        ..promotionGiftScope = combo.promotionGiftScope;
      comboDetails.add(detail);
    }
    return comboDetails;
  }

  ///随机选择赠品
  ///[promotion] 促销
  ///[giftMap] 已存在的赠品
  ///[maxQty] 最大数量,注意，这里的数量不包[existGiftList]已存在赠品的数量。而是要随机的最大数量。
  ///返回赠品列表
  static List<PromotionGift> randomChooseGift(
      {required BillPromotionInfoDto promotion,
        required num maxQty,
        required int giftType,
        required Map<String, List<GoodsDetailDto>> comboDetailsMap,
        Map<PromotionPtype, List<PromotionGift>> giftMap = const {}}) {
    giftMap = {...giftMap};
    RandomGiftUtil.randomChooseGift<PromotionPtype>(
        maxQty: maxQty,
        giftList: promotion.giftPtypeList,
        onChooseGift: (giftBean, qty) {
          List<PromotionGift> existGiftList =
          giftMap.putIfAbsent(giftBean, () => []);
          //找到已存在的赠品，以及它的数量
          PromotionGift? existGift = existGiftList.firstOrNull;
          num existGiftQty = existGift?.promotionGiftQty ?? 0;
          //若没有找到已存在的赠品，则创建一个，并且放入map
          if (giftType == PromotionGiftType.goods.value) {
            if (existGift == null) {
              existGift = giftBean.changeToGoodsDetailDto(
                  promotion, promotion.promotionType);
              existGiftList.add(existGift);
            }
            //处理套餐明细行，若没有该赠品的套餐明细，则生成明细
            if (existGift is GoodsDetailDto &&
                existGift.comboRow &&
                !comboDetailsMap.containsKey(existGift.comboRowId)) {
              List<GoodsDetailDto> comboDetails =
              generatePromotionComboDetailGift(giftBean, existGift);
              comboDetailsMap[existGift.comboRowId] = comboDetails;
            }
          } else if (giftType == PromotionGiftType.coupon.value) {
            if (existGift == null) {
              existGift = giftBean.changeToCouponGift(promotion);
              existGiftList.add(existGift);
            }
          } else {
            return;
          }
          //调整赠品数量
          if (existGiftQty > 0) {
            qty = MathUtil.addDec(qty, existGiftQty).toDouble();
          }
          existGift.promotionGiftQty = qty;
        });
    return giftMap.values.expand((element) => element).toList();
  }

  ///商品关联促销
  static void setGoodsPromotion(
      {required GoodsDetailDto goods,
        BillPromotionInfoDto? promotion,
        String promotionId = "",
        int promotionType = -1,
        bool joinOrder = true,
        bool vipRights = true,
        bool calIntegral = true}) {
    if (promotion != null) {
      promotionId = promotion.id;
      promotionType = promotion.promotionType;
      calIntegral = promotion.calIntegral;
      vipRights = promotion.vipRights;
      joinOrder = promotion.joinOrder;
    }
    goods.promotionId = promotionId;
    goods.promotionType = promotionType;
    goods.calIntegral = calIntegral;
    goods.vipRights = vipRights;
    goods.joinOrder = joinOrder;
  }

  ///判断是否可以选择赠品
  static bool canSelectGift(GoodsBillDto bill, [String? promotionId]) {
    Decimal giftCount = bill.promotionGiftRecord
        ?.where((element) =>
    promotionId == null || element.promotionId == promotionId)
        .fold<Decimal>(
        Decimal.zero,
            (previousValue, element) =>
        previousValue +
            Decimal.parse(element.giftCount.toString())) ??
        Decimal.zero;
    if (giftCount <= Decimal.zero) return false;
    Decimal existGiftCount = bill.outDetail
        .where((element) =>
    (promotionId == null || element.promotionId == promotionId) &&
        (element.gift &&
            element.promotionGift &&
            element.promotionGiftScope ==
                PromotionGiftScope.chooseGoods.value))
        .fold<Decimal>(
        Decimal.zero,
            (previousValue, element) =>
        previousValue + Decimal.parse(element.unitQty.toString()));
    existGiftCount = bill.giftCouponList
        .where((element) =>
    promotionId == null || element.promotionId == promotionId)
        .fold(
        existGiftCount,
            (previousValue, element) =>
        previousValue + Decimal.parse(element.unitQty.toString()));
    return existGiftCount < giftCount;
  }

  ///合并赠品优惠券
  static void mergeCouponGift(GoodsBillDto bill, List<CouponGift> coupons) {
    if (bill.giftCouponList.isEmpty) {
      bill.giftCouponList.addAll(coupons);
    } else {
      Set<CouponGift> loopedSet = {};
      Set<CouponGift> sameGoodsSet = {};
      for (var coupon in coupons) {
        if (loopedSet.add(coupon)) {
          List<CouponGift> sameCouponList = bill.giftCouponList
              .where((element) =>
          element.promotionId == coupon.promotionId &&
              element.pid == coupon.pid)
              .toList();
          loopedSet.addAll(sameCouponList);
          //未找到则添加
          if (sameCouponList.isEmpty) {
            bill.giftCouponList.add(coupon);
          } else {
            final first = sameCouponList.first;
            for (var element in sameCouponList) {
              if (first != element) {
                first.unitQty =
                    MathUtil.addDec(first.unitQty, element.unitQty).toDouble();
                sameGoodsSet.add(element);
              }
            }
            if (first != coupon) {
              first.unitQty =
                  MathUtil.addDec(first.unitQty, coupon.unitQty).toDouble();
            }
          }
        }
      }
      if (sameGoodsSet.isNotEmpty) {
        bill.giftCouponList
            .removeWhere((element) => sameGoodsSet.contains(element));
      }
    }
  }

  ///按照促销分组
  static Map<String, BillPromotionInfoDto> groupByPromotionId(
      List<BillPromotionInfoDto> promotions) {
    Map<String, BillPromotionInfoDto> promotionMap = {};
    final promotionList = SpTool.getPromotionList();
    for (var promotion in promotionList) {
      promotionMap.putIfAbsent(promotion.id, () => promotion);
    }
    return promotionMap;
  }
}

///随机赠品工具类
class RandomGiftUtil {
  ///随机选择赠品
  ///[maxQty] 最大数量
  ///[giftList] 赠品列表
  ///[onChooseGift] 选择赠品回调
  static void randomChooseGift<T>({
    required num maxQty,
    required List<T> giftList,
    required void Function(T gift, num qty) onChooseGift,
  }) {
    if (giftList.isEmpty || maxQty <= 0) return;
    Random random = Random();
    while (maxQty > 0) {
      int index = random.nextInt(giftList.length);
      num qty;
      if (maxQty <= 1) {
        qty = maxQty;
      } else {
        qty = random.nextInt(maxQty.toInt()) + 1;
      }
      onChooseGift(giftList[index], qty);
      maxQty = MathUtil.subtractDec(maxQty, qty).toDouble();
    }
  }
}
//endregion 工具类

//region 实体类
///促销提示
///在商品在促销内但是未满足条件没有触发促销时，需要添加此hints
class PromotionHints {
  ///当前hints对应的促销id
  final String promotionId;

  ///当前hints对应的促销类型
  final int promotionType;

  ///促销类型名称
  final String typeName;

  ///hints文字内容
  final String hints;

  ///是否展示详情(赠品列表，当买赠同品的时候不展示)
  final bool showDetail;

  final int promotionGiftScope;

  PromotionHints({
    required this.promotionId,
    required this.promotionType,
    required this.typeName,
    required this.hints,
    this.showDetail = false,
    int? promotionGiftScope,
  }) : promotionGiftScope = promotionGiftScope ?? PromotionGiftScope.chooseGoods.value;

  Map<String, dynamic> toJson() => {
    'promotionId': promotionId,
    'promotionType': promotionType,
    'typeName': typeName,
    'hints': hints,
    'showDetail': showDetail,
  };

  PromotionHints.fromMap(Map<String, dynamic> map)
      : promotionId = map['promotionId'],
        promotionType = map['promotionType'],
        typeName = map['typeName'],
        showDetail = map['showDetail'],
        hints = map['hints'],
        promotionGiftScope = map['promotionGiftScope'] ?? PromotionGiftScope.chooseGoods.value;
}

///促销赠品基类，用于促销赠品选择
abstract class PromotionGift {
  ///促销类型
  abstract int promotionType;

  ///促销id
  abstract String promotionId;

  ///赠品数量
  abstract num promotionGiftQty;

  ///赠品名称
  abstract final String promotionGiftName;

  ///赠品类型
  abstract final int promotionGiftType;

  ///赠品范围(选择赠品，指定赠品，买赠同品)
  abstract final int promotionGiftScope;
}

///优惠券赠品
class CouponGift implements PromotionGift {
  ///卡券模板id
  final String pid;

  final String couponName;

  num unitQty = 0;

  @override
  String promotionId;

  @override
  int promotionType;

  @override
  set promotionGiftQty(num qty) => unitQty = qty;

  @override
  num get promotionGiftQty => unitQty;

  @override
  String get promotionGiftName => couponName;

  @override
  final int promotionGiftType = PromotionGiftType.coupon.value;

  @override
  final int promotionGiftScope;

  Map<String, dynamic> toJson() {
    return {
      'pid': pid,
      'couponName': couponName,
      'unitQty': unitQty,
      'promotionId': promotionId,
      'promotionType': promotionType,
      //兼容单据接口
      'cardTemplateId': pid,
      'qty': unitQty,
      'promotionGiftScope': promotionGiftScope,
    };
  }

  CouponGift.fromMap(Map<String, dynamic> map)
      : pid = map['pid'] ?? map['cardTemplateId'],
        couponName = map['couponName'],
        unitQty = map['unitQty'] ?? map['qty'],
        promotionId = map['promotionId'],
        promotionGiftScope =
            map['promotionGiftScope'] ?? PromotionGiftScope.chooseGoods.value,
        promotionType = map['promotionType'];

  CouponGift({
    required this.pid,
    required this.couponName,
    required this.unitQty,
    required this.promotionId,
    required this.promotionType,
    int? promotionGiftScope,
  }) : promotionGiftScope =
      promotionGiftScope ?? PromotionGiftScope.chooseGoods.value;
}

///促销赠品记录
class PromotionGiftRecord {
  ///促销id
  String promotionId;

  ///促销类型
  int promotionType;

  ///赠品类型
  int giftType;

  ///促销赠品最大数量
  num giftCount;

  PromotionGiftRecord({
    required this.promotionId,
    required this.promotionType,
    required this.giftType,
    required this.giftCount,
  });

  PromotionGiftRecord.fromMap(Map<String, dynamic> map)
      : promotionId = map['promotionId'] ?? "",
        promotionType = map['promotionType'] ?? -1,
        giftType = map['giftType'] ?? -1,
        giftCount = map['giftCount'] ?? 0;

  Map<String, dynamic> toJson() => {
    'promotionId': promotionId,
    'promotionType': promotionType,
    'giftType': giftType,
    'giftMaxQty': giftCount,
  };
}
//endregion 实体类
