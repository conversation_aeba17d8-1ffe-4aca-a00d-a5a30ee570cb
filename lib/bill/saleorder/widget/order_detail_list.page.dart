import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/payment_dto.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/style/app_pos_size.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../common/widget/ptype_note_richtext.dart';
import '../../../entity/system/column_config.dart';
import '../../../enum/bill_pay_state.dart';
import '../../../enum/bill_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../print/tool/print_tool.dart';
import '../../../settting/widget/column_config_page.dart';
import '../../../vip/detail/vip_detail_page.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../entity/delete_bill_request.dart';
import 'order_list_page.dart';

//折叠ExpansionPanelList

class OrderDetailList extends StatefulWidget {
  String? vchcode;
  String? vchtype;
  GoodsBillDto? goodsBillDto;
  final OrderBillType billType;
  Function? afterDeleteCallback;

  OrderDetailList(
    Key key,
    this.vchcode,
    this.vchtype,
    this.billType, {
    this.afterDeleteCallback,
    this.goodsBillDto,
  }) : super(key: key);

  @override
  OrderDetailListState createState() => OrderDetailListState();
}

class OrderDetailListState extends State<OrderDetailList> {
  late GoodsBillDto goodsBillDto = GoodsBillDto();

  late List<ColumnConfig> titleList;
  List<bool> isSelected = [true, false];

  List<Widget> listTop() {
    return titleList.map((e) {
      if (e.type == ColumnType.setting) {
        return Container(
          height: 60.h,
          alignment: Alignment.center,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              //设置列配置
              DialogUtil.showAlertDialog(
                context,
                child: ColumnConfigPages(
                  columnPagesType: ColumnPagesType.ColumnPagesBills,
                  changed: (config) {
                    setState(() {
                      titleList = getColumnData(config: config)!;
                    });
                  },
                ),
              );
            },
            child: IconFont(IconNames.shezhi),
          ),
        );
      }

      return Container(
        height: 60.h,
        alignment:
            e.type == ColumnType.pName
                ? Alignment.centerLeft
                : Alignment.center,
        padding:
            e.type == ColumnType.pName
                ? EdgeInsets.only(left: 16.w)
                : EdgeInsets.zero,
        child: HaloPosLabel(
          e.title,
          maxLines: 1,
          textStyle: TextStyle(
            color: AppColors.normalFontColor,
            decoration: TextDecoration.none,
            fontSize: AppPosSize.secondaryTitleFontSize.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }).toList();
  }

  List<GoodsDetailDto?>? _getDetail() {
    if (widget.vchtype == BillTypeData[BillType.SaleChangeBill]) {
      return (goodsBillDto.inDetail.isNotEmpty && isSelected[0])
          ? goodsBillDto.inDetail
          : goodsBillDto.outDetail;
    } else {
      return goodsBillDto.inDetail.isNotEmpty
          ? goodsBillDto.inDetail
          : goodsBillDto.outDetail;
    }
  }

  List<TableRow> _renderList() {
    List<GoodsDetailDto?>? detail = _getDetail();
    return detail?.map((GoodsDetailDto? detailDto) {
          return TableRow(
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    detailDto?.isShowCombo = !detailDto.isShowCombo;
                  });
                },
                child: Column(
                  children: [
                    _buildRowItem(
                      detailDto,
                      isShowCombo: detailDto!.comboDetailList.isNotEmpty,
                      isComboDetail: false,
                    ),
                    Visibility(
                      child: HaloContainer(
                        direction: Axis.vertical,
                        visible:
                            detailDto.isShowCombo == true &&
                            detailDto.comboDetailList.isNotEmpty,
                        children: _buildCombWidget(detailDto),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        }).toList() ??
        [];
  }

  Widget _buildRowItem(
    GoodsDetailDto? detailDto, {
    bool isShowCombo = false,
    bool isComboDetail = false,
  }) {
    return HaloContainer(
      children:
          titleList.map((e) {
            switch (e.type) {
              case ColumnType.pName:
                return _buildTabItem(
                  (detailDto?.pFullName ?? "") + (detailDto?.skuName ?? ""),
                  padding: EdgeInsets.only(left: 16.w),
                  memo: detailDto!.memo,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  isShowDisconut: detailDto.discount < 1,
                  flex: 200,
                  isShowCombo: isShowCombo,
                  isComboDetail: isComboDetail,
                  detailDto: detailDto,
                );

              case ColumnType.barCode:
                return _buildTabItem(
                  detailDto?.fullbarcode,
                  padding: const EdgeInsets.only(left: 0),
                  flex: 200,
                );
              case ColumnType.userCode:
                return _buildTabItem(
                  detailDto?.pUserCode ?? "",
                  flex: 200,
                  isComboDetail: isComboDetail,
                );

              case ColumnType.unit:
                return _buildTabItem(
                  detailDto?.unitName,
                  isComboDetail: isComboDetail,
                );
              case ColumnType.number:
                return _buildTabItem(
                  detailDto!.unitQty.toString(),
                  textColor: AppColors.redTextColor,
                  isComboDetail: isComboDetail,
                );
              case ColumnType.discount:
                return _buildTabItem(
                  isComboDetail
                      ? ""
                      : "${DecimalDisplayHelper.getDisplayBillDiscountFixed((detailDto!.discount).toString())}折",
                );
              case ColumnType.currentPrice:
                return _buildTabItem(
                  isComboDetail
                      ? ""
                      : DecimalDisplayHelper.getDisplayPriceFixed(
                        MathUtil.division(
                          (MathUtil.add(
                            detailDto!.currencyDisedTaxedTotal.toString(),
                            detailDto.currencyGivePreferentialTotal.toString(),
                          )).toString(),
                          detailDto.unitQty.toString(),
                        ).toString(),
                      ).toString(),
                );
              case ColumnType.subtotalPrice:
                return _buildTabItem(
                  isComboDetail
                      ? ""
                      : MathUtil.add(
                        detailDto!.currencyDisedTaxedTotal.toString(),
                        detailDto.currencyGivePreferentialTotal.toString(),
                      ).toString(),
                );
              case ColumnType.price:
                return _buildTabItem(
                  isComboDetail ? "" : detailDto!.currencyPrice.toString(),
                );
              // DecimalDisplayHelper.getDisplayPriceFixed(
              //     detailDto.currencyPrice.toString()));
              case ColumnType.taxRate:
                return _buildTabItem(
                  detailDto!.comboRow ? "" : detailDto.taxRate.toString(),
                );
              case ColumnType.taxTotal:
                return _buildTabItem(detailDto!.currencyTaxTotal.toString());
              case ColumnType.reducedPrice:
                return _buildTabItem(
                  isComboDetail
                      ? ""
                      : MathUtil.subtraction(
                        detailDto!.currencyPreferentialTotal.toString(),
                        detailDto.currencyGivePreferentialTotal.toString(),
                      ).toString(),
                );
              case ColumnType.setting:
                return _buildTabItem(" ", flex: 5 * titleList.length);
              default:
                return Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: HaloPosLabel(
                    "未知数据",
                    maxLines: 1,
                    textStyle: const TextStyle(
                      color: Colors.black,
                      decoration: TextDecoration.none,
                      fontSize: 18,
                    ),
                  ),
                );
            }
          }).toList(),
    );
  }

  List<Widget> _buildCombWidget(GoodsDetailDto combDto) {
    if (combDto.comboDetailList.isEmpty) {
      return [];
    }
    return combDto.comboDetailList.map((detailDto) {
      return _buildRowItem(detailDto, isShowCombo: false, isComboDetail: true);
    }).toList();
  }

  Widget _buildTabItem(
    String? text, {
    Color? textColor,
    var flex = 100,
    Color? bgColor,
    EdgeInsets padding = EdgeInsets.zero,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    String memo = "",
    bool isShowDisconut = false,
    bool isShowCombo = false,
    bool isComboDetail = false,
    GoodsDetailDto? detailDto,
  }) {
    return Expanded(
      flex: flex,
      child: HaloContainer(
        direction: Axis.vertical,
        crossAxisAlignment: crossAxisAlignment,
        color: bgColor ?? Colors.transparent,
        children: [
          HaloContainer(
            padding: padding,
            height: 50,
            children: [
              _buildTabItemValue(
                text,
                detailDto,
                crossAxisAlignment: crossAxisAlignment,
                memo: memo,
              ),
              Visibility(
                visible: isShowCombo,
                child: Container(
                  margin: EdgeInsets.only(left: 10.w),
                  alignment: Alignment.center,
                  width: 50.w,
                  height: 28.w,
                  child: HaloPosLabel(
                    null != detailDto &&
                            detailDto.isShowCombo == true &&
                            detailDto.comboDetailList.isNotEmpty
                        ? "收起"
                        : "展开",
                    textAlign: TextAlign.center,
                    textStyle: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 20.sp,
                      color: AppColors.accentColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Divider(height: 1, color: AppColors.lineColor),
        ],
      ),
    );
  }

  ///构建表格Item的内容
  Widget _buildTabItemValue(
    String? text,
    GoodsDetailDto? detailDto, {
    Color? textColor,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    String memo = "",
  }) {
    ///商品名称
    if (null != detailDto) {
      return Expanded(child: HaloContainer(
        direction: Axis.vertical,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          PtypeNoteRichText(goodsDetailDto: detailDto),
          Visibility(
            visible: memo.isNotEmpty,
            child: Padding(
              padding: EdgeInsets.only(top: 5.w),
              child: HaloPosLabel(
                "备注：$memo",
                maxLines: 1,
                textStyle: TextStyle(
                  color: ColorUtil.stringColor("f78e34"),
                  decoration: TextDecoration.none,
                  fontSize: AppPosSize.describeFontSize.sp,
                ),
              ),
            ),
          ),
        ],
      ));
    }
    return Flexible(
      child: HaloContainer(
        direction: Axis.vertical,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          HaloPosLabel(
            (text != null && text.isNotEmpty) ? text : "",
            maxLines: 1,
            textStyle: TextStyle(
              color: textColor ?? AppColors.normalFontColor,
              decoration: TextDecoration.none,
              fontSize: AppPosSize.secondaryTitleFontSize.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
          Visibility(
            visible: memo.isNotEmpty,
            child: Padding(
              padding: EdgeInsets.only(top: 5.w),
              child: HaloPosLabel(
                memo,
                maxLines: 1,
                textStyle: TextStyle(
                  color: textColor ?? AppColors.normalFontColor,
                  decoration: TextDecoration.none,
                  fontSize: AppPosSize.describeFontSize.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    goodsBillDto = handleGoodsBill(widget.goodsBillDto);
    setState(() {
      titleList = getColumnData()!;
    });
  }

  List<ColumnConfig>? getColumnData({config}) {
    List<ColumnConfig> temp = SpTool.getBillsColumnConfig();
    if (temp.every((element) => element.title != "设置")) {
      temp.add(ColumnConfig(title: "设置", type: ColumnType.setting));
    }
    return temp.where((element) {
      return element.isShow && element.title != '全部';
    }).toList();
  }

  void updateWightDate(String? vsCode, String? vchType, String billTotal) {
    widget.vchcode = vsCode;
    widget.vchtype = vchType;
    onRequestData().then((value) {
      setState(() {
        goodsBillDto = value;
        goodsBillDto.currencyBillTotal = billTotal;
      });
    });
  }

  void updateWightByGoodsBill(GoodsBillDto? billDto) {
    setState(() {
      goodsBillDto = handleGoodsBill(billDto);
    });
  }

  HashMap<int, TableColumnWidth> buildColumnWidth() {
    HashMap<int, TableColumnWidth> columnWidths = HashMap();
    columnWidths[titleList.length - 1] = FlexColumnWidth(
      0.05 * titleList.length,
    );
    for (int i = 0; i < titleList.length; i++) {
      var element = titleList[i];
      if (element.title == "商品名称") {
        columnWidths[i] = const FlexColumnWidth(2);
      }
      if (element.title == "条码") {
        columnWidths[i] = const FlexColumnWidth(2);
      }

      if (element.title == "商品编号") {
        columnWidths[i] = const FlexColumnWidth(2);
      }
    }
    return columnWidths;
  }

  Widget noDataWidget() {
    return Expanded(
      child: HaloEmptyContainer(
        gravity: EmptyGravity.CENTER,
        image: Image.asset('assets/images/nodata.png'),
        title: "暂无权限查看或数据不存在",
        titleStyle: TextStyle(
          decoration: TextDecoration.none,
          fontSize: ScreenUtil().setSp(30),
          color: Colors.grey,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if ((goodsBillDto.outDetail.isEmpty && goodsBillDto.inDetail.isEmpty)) {
      return noDataWidget();
    }

    return Expanded(
      child: HaloContainer(
        mainAxisSize: MainAxisSize.max,
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(6.sp)),
        margin: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
        children: [
          Expanded(
            child: HaloContainer(
              padding: EdgeInsets.only(top: 16.h),
              direction: Axis.vertical,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                buildBillInfo(),
                buildVipInfo(),
                Expanded(child: buildContent()),
                buildBottomButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<String> getFiledList(OrderBillType billType) {
    List<String> showFields = [];
    showFields.add("单据金额：${goodsBillDto.currencyBillTotal}元");
    if (billType == OrderBillType.OrderSaled) {
      showFields.add("支付方式：${getPaywayName()}");
      showFields.add("支付流水号：${getPayOutNo()}");
    }
    if (billType == OrderBillType.offlineBill) {
      showFields.add("支付方式：${getPaywayName()}");
    }
    showFields.add("优惠金额：${goodsBillDto.getPreferentialTotalUI()}元");
    showFields.add("收银员：${goodsBillDto.createEfullname ?? "暂无"}");
    showFields.add("业务员：${goodsBillDto.efullname ?? "暂无"}");
    return showFields;
  }

  ///构建单据表头信息
  Widget buildBillInfo() {
    List<Widget> widgets = [];
    widgets.add(
      HaloContainer(
        margin: EdgeInsets.only(bottom: 6.h),
        children: [
          HaloPosLabel(
            "订单信息",
            textStyle: TextStyle(
              color: AppColors.normalFontColor,
              fontWeight: FontWeight.w600,
              fontSize: AppPosSize.firstTitleFontSize.sp,
            ),
          ),
          Container(
            height: 34.h,
            margin: EdgeInsets.only(left: 8.w),
            padding: EdgeInsets.only(left: 6.w, right: 6.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.sp),
              border: Border.all(
                color:
                    goodsBillDto.payState ==
                            BillPayStateString[BillPayState.Paied]
                        ? AppColors.payedStateColor
                        : AppColors.unPayStateColor,
              ),
            ),
            child: HaloPosLabel(
              getPayState(),
              textAlign: TextAlign.center,
              textStyle: TextStyle(
                fontSize: AppPosSize.contentFontSize.sp,
                color:
                    goodsBillDto.payState ==
                            BillPayStateString[BillPayState.Paied]
                        ? AppColors.payedStateColor
                        : AppColors.unPayStateColor,
              ),
            ),
          ),
        ],
      ),
    );

    List<String> filedList = getFiledList(widget.billType);

    int row = (filedList.length / 3).ceil();
    for (int i = 0; i < row; i++) {
      widgets.add(buildBillInfoRow(filedList, i));
    }
    widgets.add(SizedBox(height: 6.h));

    widgets.add(
      HaloPosLabel(
        "附加说明：${StringUtil.isEmpty(goodsBillDto.memo) ? "暂无" : goodsBillDto.memo}",
        textStyle: TextStyle(
          color: AppColors.normalFontColor,
          fontSize: AppPosSize.totalFontSize.sp,
        ),
      ),
    );
    return HaloContainer(
      direction: Axis.vertical,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  ///构建单据表头信息的Row
  Widget buildBillInfoRow(List<String> filedList, int rowIndex) {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      margin: EdgeInsets.only(top: 6.h),
      children: [
        buildBillInfoItem(filedList, rowIndex, 0),
        buildBillInfoItem(filedList, rowIndex, 1),
        buildBillInfoItem(filedList, rowIndex, 2),
      ],
    );
  }

  ///构建会员信息
  Widget buildVipInfo() {
    return HaloContainer(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      direction: Axis.vertical,
      visible: StringUtil.isNotZeroOrEmpty(goodsBillDto.vipCardId),
      margin: EdgeInsets.only(top: 16.h),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        HaloPosLabel(
          "会员信息",
          textStyle: TextStyle(
            color: AppColors.normalFontColor,
            fontWeight: FontWeight.w600,
            fontSize: AppPosSize.firstTitleFontSize.sp,
          ),
        ),
        HaloContainer(
          margin: EdgeInsets.only(top: 6.h),
          children: [
            Expanded(
              child: HaloPosLabel(
                "会员名：${getVipName()}",
                textStyle: TextStyle(
                  color: AppColors.normalFontColor,
                  fontSize: AppPosSize.totalFontSize.sp,
                ),
              ),
            ),
            Expanded(
              child: HaloContainer(
                children: [
                  HaloPosLabel(
                    "手机号码：${getVipPhone()}",
                    textStyle: TextStyle(
                      color: AppColors.normalFontColor,
                      fontSize: AppPosSize.totalFontSize.sp,
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      if (goodsBillDto.vipCardId == '0') return;
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  VipDetailPage(vipId: goodsBillDto.vipCardId!),
                        ),
                      );
                    },
                    child: HaloContainer(
                      margin: EdgeInsets.only(left: 8.w),
                      children: [
                        HaloPosLabel(
                          "查看详情 >",
                          textStyle: TextStyle(
                            color: AppColors.accentColor,
                            fontSize: AppPosSize.totalFontSize.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(child: Container()),
          ],
        ),
      ],
    );
  }

  ///构建单据明细
  Widget buildContent() {
    HashMap<int, TableColumnWidth> columnWidths = buildColumnWidth();

    // 计算商品种类和总件数
    int goodsTypes = 0;
    num totalQuantity = 0;
    List<GoodsDetailDto?>? detail = _getDetail();
    if (detail != null) {
      // 统计种类数量和总件数
      for (var item in detail) {
        if (item != null &&
            (item.comboRowParId == "0" || item.comboRowParId.isEmpty)) {
          goodsTypes++;
          totalQuantity += item.unitQty;
        }
      }
    }
    String totalString = DecimalDisplayHelper.getQtyFixed(
      totalQuantity.toString());

    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      margin: EdgeInsets.only(left: 16.w, top: 16.w, right: 16.h),
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: HaloContainer(
            visible:
                goodsBillDto.vchtype != BillTypeData[BillType.SaleChangeBill],
            children: [
              HaloPosLabel(
                "商品信息",
                textStyle: TextStyle(
                  color: AppColors.normalFontColor,
                  fontWeight: FontWeight.w600,
                  fontSize: AppPosSize.firstTitleFontSize.sp,
                ),
              ),
              SizedBox(width: 12.w),
              HaloPosLabel(
                "(共 $goodsTypes 种 $totalString 件)",
                textStyle: TextStyle(
                  color: AppColors.normalFontColor,
                  fontSize: AppPosSize.secondaryTitleFontSize.sp,
                ),
              ),
            ],
          ),
        ),
        Visibility(
          visible:
              goodsBillDto.vchtype == BillTypeData[BillType.SaleChangeBill],
          child: ToggleButtons(
            color: AppColors.normalFontColor,
            selectedColor: AppColors.accentColor,
            selectedBorderColor: Colors.transparent,
            borderColor: Colors.transparent,
            fillColor: Colors.white.withOpacity(0.9),
            isSelected: isSelected,
            onPressed: (index) {
              setState(() {
                for (
                  int buttonIndex = 0;
                  buttonIndex < isSelected.length;
                  buttonIndex++
                ) {
                  if (buttonIndex == index) {
                    isSelected[buttonIndex] = true;
                  } else {
                    isSelected[buttonIndex] = false;
                  }
                }
              });
            },
            children: [
              Container(
                padding: EdgeInsets.only(bottom: 6.h),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color:
                          isSelected[0] ? AppColors.accentColor : Colors.white,
                      width: 4.h,
                    ),
                  ),
                ),
                child: Text(
                  '换入商品',
                  style: TextStyle(
                    fontSize: AppPosSize.firstTitleFontSize.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                padding: EdgeInsets.only(bottom: 6.h),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color:
                          isSelected[1] ? AppColors.accentColor : Colors.white,
                      width: 4.h,
                    ),
                  ),
                ),
                child: Text(
                  '换出商品',
                  style: TextStyle(
                    fontSize: AppPosSize.firstTitleFontSize.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          color: AppColors.pageBackgroundColor,
          child: Table(
            defaultVerticalAlignment: TableCellVerticalAlignment.top,
            defaultColumnWidth: const FlexColumnWidth(),
            columnWidths: columnWidths,
            children: [TableRow(children: listTop())],
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            child: Table(
              defaultVerticalAlignment: TableCellVerticalAlignment.top,
              defaultColumnWidth: const FlexColumnWidth(),
              columnWidths: columnWidths,
              children: _renderList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildBottomButton() {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      border: Border(top: BorderSide(color: AppColors.borderColor, width: 1.h)),
      children: [
        Expanded(
          child: HaloContainer(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              HaloContainer(
                visible:
                    StringUtil.isNotZeroOrEmpty(goodsBillDto.vchcode) &&
                    widget.billType == OrderBillType.OrderSelling,
                margin: EdgeInsets.only(right: 20.w),
                children: [
                  HaloButton(
                    width: 200.w,
                    height: 80.h,
                    text: "删除",
                    buttonType: HaloButtonType.outlinedButton,
                    outLineWidth: 1.w,
                    fontSize: AppPosSize.firstTitleFontSize.sp,
                    borderRadius: 6.sp,
                    textColor: AppColors.normalFontColor,
                    borderColor: AppColors.btnBorderColor,
                    onPressed: () {
                      DialogUtil.showConfirmDialog(
                        context,
                        title: "提示",
                        content: "确定删除该单据？",
                        actionLabels: ["取消", "确定"],
                        confirmCallback: () {
                          DeleteBillRequest deleteBillRequest =
                              DeleteBillRequest();
                          deleteBillRequest.vchcode = goodsBillDto.vchcode;
                          deleteBillRequest.vchtype = "Sale";
                          deleteBillRequest.billPostState = 0;
                          deleteBillRequest.billDate = goodsBillDto.date;
                          BillModel.deleteBill(context, deleteBillRequest).then(
                            (value) {
                              if (value?.success ?? false) {
                                if (null != widget.afterDeleteCallback) {
                                  widget.afterDeleteCallback!();
                                }
                              } else {
                                HaloToast.show(
                                  context,
                                  msg: value?.errorDetail?.first.message,
                                );
                              }
                            },
                          );
                        },
                        cancelCallback: () {},
                      );
                    },
                  ),
                  SizedBox(width: 20.w),
                  HaloButton(
                    height: 80.h,
                    width: 200.w,
                    borderRadius: 6.sp,
                    text: "取单",
                    fontSize: AppPosSize.firstTitleFontSize.sp,
                    backgroundColor: AppColors.accentColor,
                    onPressed: () {
                      //把原始数据复原 (详情展示时，将套餐行折叠展示，对数据进行了处理，取单需要数据还原拆行返回到单据界面中)
                      goodsBillDto.outDetail = goodsBillDto.originalDetail;
                      goodsBillDto.inDetail = goodsBillDto.originalInDetail;
                      Navigator.of(context).pop(goodsBillDto);
                    },
                  ),
                ],
              ),
              Visibility(
                visible:
                    widget.billType == OrderBillType.OrderSaled &&
                    SpTool.getPermission().shopsaleprintpatchworknote!,
                child: HaloButton(
                  height: AppPosSize.btnHeight.h,
                  borderRadius: 6.sp,
                  width: 160.w,
                  text: "补打小票",
                  fontSize: AppPosSize.firstTitleFontSize.sp,
                  backgroundColor: AppColors.accentColor,
                  onPressed: () {
                    onPatchworkNote();
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  buildBillInfoItem(List<String> filedList, int rowIndex, int itemIndex) {
    int showIndex = rowIndex * 3 + itemIndex;
    if (showIndex < filedList.length) {
      return Expanded(
        child: HaloPosLabel(
          filedList[showIndex],
          textStyle: TextStyle(
            color: AppColors.normalFontColor,
            fontSize: AppPosSize.totalFontSize.sp,
          ),
        ),
      );
    }
    return Expanded(child: Container());
  }

  Future<GoodsBillDto> onRequestData() {
    return BillModel.getGoodsBill(
      context,
      widget.vchcode,
      widget.vchtype,
      BillBusinessTypeString[BillBusinessType.SaleNormal]!,
    ).then((value) {
      return handleGoodsBill(value);
    });
  }

  GoodsBillDto handleGoodsBill(GoodsBillDto? value) {
    value ??= GoodsBillDto();
    List<GoodsDetailDto> temp = [];

    if (widget.vchtype == BillTypeData[BillType.SaleBill] ||
        widget.vchtype == BillTypeData[BillType.SaleBackBill]) {
      List<GoodsDetailDto>? detail =
          widget.vchtype == BillTypeData[BillType.SaleBill]
              ? value.outDetail
              : value.inDetail;
      detail.forEach((element) {
        if (element.comboRow) {
          element.comboDetailList =
              []..addAll(
                detail.where(
                  (elementTwo) =>
                      elementTwo.comboRowParId == element.comboRowId,
                ),
              );
          temp.add(element);
        } else if (element.comboRowParId != "0") {
        } else {
          temp.add(element);
        }
      });
      value.originalDetail = detail;
      widget.vchtype == BillTypeData[BillType.SaleBackBill]
          ? value.inDetail = temp
          : value.outDetail = temp;
    } else if (widget.vchtype == BillTypeData[BillType.SaleChangeBill]) {
      List<GoodsDetailDto> outTemp = [];

      List<GoodsDetailDto?>? inDetail = value.inDetail;
      inDetail.forEach((element) {
        if (element!.comboRow) {
          element.comboDetailList =
              []..addAll(
                inDetail.where(
                  (elementTwo) =>
                      elementTwo?.comboRowParId == element.comboRowId,
                ),
              );
          temp.add(element);
        } else if (element.comboRowParId != "0") {
        } else {
          temp.add(element);
        }
      });
      value.originalInDetail = value.inDetail;
      value.inDetail = temp;

      List<GoodsDetailDto?>? outDetail = value.outDetail;
      outDetail.forEach((element) {
        if (element!.comboRow) {
          element.comboDetailList =
              []..addAll(
                outDetail.where(
                  (elementTwo) =>
                      elementTwo?.comboRowParId == element.comboRowId,
                ),
              );
          outTemp.add(element);
        } else if (element.comboRowParId != "0") {
        } else {
          outTemp.add(element);
        }
      });
      value.originalDetail = value.outDetail;
      value.outDetail = outTemp;
    }
    return value;
  }

  bool isContainsFullBarCode() {
    ColumnConfig config = titleList.firstWhere((element) {
      return element.title == "条码";
    }, orElse: () => ColumnConfig());
    return config != null;
  }

  //获取支付方式名称拼接
  String getPaywayName() {
    return goodsBillDto.payment
        .map((e) {
          return e.paywayFullname;
        })
        .join(",");
  }

  String getPayState() {
    String? payState = goodsBillDto.payState;
    if (payState?.isEmpty ?? true) return "未知状态";
    return payState == BillPayStateString[BillPayState.Paied] ? "已支付" : "未支付";
  }

  //查 支付流水号
  String getPayOutNo() {
    PaymentDto? paymentDto = goodsBillDto.payment.firstWhereOrNull(
      (element) => element.outNo == null ? false : (element.outNo!.isNotEmpty),
    );
    return paymentDto?.outNo ?? "暂无";
  }

  String getVipName() {
    return goodsBillDto.vipBillInfo?.name ?? "暂无";
  }

  String getVipPhone() {
    return goodsBillDto.vipBillInfo?.phone ?? "暂无";
  }

  void onPatchworkNote() {
    DialogUtil.showConfirmDialog(
      context,
      title: "提示",
      content: "确认补打小票？",
      actionLabels: ["取消", "确定"],
      confirmCallback: () {
        PrintTool.printBillByVchCode(
          context,
          BillTypeString[goodsBillDto.vchtype]!,
          vchCode: goodsBillDto.vchcode,
          printCount: 1,
        );
      },
      cancelCallback: () {},
    );
  }
}
