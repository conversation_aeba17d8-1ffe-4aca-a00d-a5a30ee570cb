///
///@ClassName: offline_sale_list
///@Description: 离线单据
///@Author: tanglan
///@Date: 2025/3/5
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/bill/tool/bill_tool.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import '../../../bill/entity/delete_bill_request.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../bill/tool/scan_tool.dart';
import '../../../bill/entity/order_bill_item_entity.dart';
import '../../../enum/bill_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../bill/model/bill_model.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/datepicker/pduration.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';

import '../../common/keyboard_hidden.dart';
import '../../common/login/login_center.dart';
import '../../common/tool/DateTimePickUtil.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/widget/datetime_filter.dart';
import '../../db/bill_db_manager.dart';
import '../../widgets/selector/scan_select.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/ptype/ptype_and_sku_dto.dart';
import '../widget/ptype/goods_and_combo_select_list_page.dart';
import 'widget/order_detail_list.page.dart';
import 'widget/order_list_page.dart';

class OfflineSaleList extends BaseStatefulPage {
  OfflineSaleList({
    Key? key,
  }) : super(key: key, rightFlex: 2.5);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _OfflineSaleListState();
}

class _OfflineSaleListState extends BaseStatefulPageState<OfflineSaleList> {
  int index = 0;
  final GlobalKey<OrderListPageState> _globalKey = GlobalKey();
  final GlobalKey<OrderDetailListState> _detailGlobalKey = GlobalKey();
  List<GoodsBillDto> goodsList = [];

  @override
  Future<void> onInitState() async {
    refreshOfflineBill();
    return Future.value();
  }

  @override
  String getActionBarTitle() {
    return "离线单据";
  }

  Future<void> refreshOfflineBill() async {
    String profileId = LoginCenter.getLoginUser().profileId!;
    goodsList = await BillDBManager.selectSaleBill(profileId: profileId);
    goodsList.sort((a, b) {
      int aTime = DateUtil.getDateTime(a.date)?.millisecondsSinceEpoch ?? 0;
      int bTime = DateUtil.getDateTime(b.date)?.millisecondsSinceEpoch ?? 0;

      return bTime.compareTo(aTime);
    });

    _globalKey.currentState!.updateWidgetDataSource(convertOrderBillItem());
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
        color: Colors.white,
        mainAxisSize: MainAxisSize.max,
        direction: Axis.vertical,
        children: [
          Expanded(
              child: OrderListPage(
                  key: _globalKey,
                  billType: OrderBillType.offlineBill,
                  crossCash: false,
                  billList: convertOrderBillItem(),
                  onChanged: (int value, item) {
                    setState(() {
                      index = value;
                      _detailGlobalKey.currentState
                          ?.updateWightByGoodsBill(goodsList[index]);
                    });
                  })),
          HaloContainer(
            border: const Border(
                top: BorderSide(color: AppColors.borderColor, width: 1)),
            padding: EdgeInsets.only(
                left: 20.w, right: 20.w, top: 14.h, bottom: 16.h),
            children: [
              Expanded(
                  child: HaloButton(
                outLineWidth: 1,
                height: 70.h,
                borderColor: AppColors.accentColor,
                fontSize: AppPosSize.firstTitleFontSize.sp,
                textColor: AppColors.accentColor,
                buttonType: HaloButtonType.outlinedButton,
                text: "同步离线单据",
                onPressed: () async {
                  await BillTool.syncOfflineBillSubmit(context,
                      afterSyncSuccess: () async {
                    await refreshOfflineBill();
                    if (goodsList.isEmpty) {
                      _detailGlobalKey.currentState
                          ?.updateWightByGoodsBill(null);
                    } else {
                      _detailGlobalKey.currentState
                          ?.updateWightByGoodsBill(goodsList[0]);
                    }
                  });
                },
              ))
            ],
          )
        ]);
  }

  List<OrderBillItem> convertOrderBillItem() {
    List<OrderBillItem> list = [];
    for (GoodsBillDto goodsItem in goodsList) {
      OrderBillItem billItem = OrderBillItem();
      billItem.vchcode = goodsItem.vchcode ?? "";
      billItem.vchtype = billTypeCodeByStr[goodsItem.vchtype]!;
      billItem.billDate = goodsItem.date ?? "";
      billItem.billTotal = double.parse(goodsItem.currencyBillTotal);
      billItem.billNumber = goodsItem.vchcode ?? "";
      list.add(billItem);
    }
    return list;
  }

  @override
  Widget buildRightBody(BuildContext context) {
    if (goodsList.isEmpty) {
      return Container();
    }
    GoodsBillDto goodsBillDto = goodsList[index];
    goodsBillDto.originalDetail = goodsBillDto.outDetail;
    //右侧详情
    return HaloContainer(
      border: const Border(
          left: BorderSide(width: 1, color: AppColors.dividerColor)),
      direction: Axis.vertical,
      mainAxisSize: MainAxisSize.max,
      children: [
        OrderDetailList(
          _detailGlobalKey, //用于调用方法
          null,
          BillTypeCoreToString[goodsBillDto?.vchtype],
          OrderBillType.offlineBill,
          goodsBillDto: goodsBillDto,
          afterDeleteCallback: () {
            ///删除成功
            _globalKey.currentState?.removeItem(index);
          },
        ),
      ],
    );
  }
}
