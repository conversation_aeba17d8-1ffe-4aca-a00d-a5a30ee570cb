import 'package:decimal/decimal.dart';
import '../../common/standard.dart';
import 'package:halo_utils/utils/math_util.dart';

import '../../enum/bill_type.dart';
import '../tool/promotion/promotion.dart';
import 'bill_sale_bill_detail_dto.dart';
import 'bill_vip_assert_change.dart';
import 'payment_dto.dart';
import 'preferential_dto.dart';
import '../../cashierbox/entity/cash_box_payment_request_dto.dart';
import 'abstract_bill_dto.dart';
import 'fee_dto.dart';
import 'goods_detail_dto.dart';

class GoodsBillDto extends AbstractBillDto {
  //TODO 设备id 核算时间

  String? payState;

  ///礼品券备注
  String couponMemo = "";

  ///优惠辅助表
  ///key PreferentialDtoClass.String
  Map<String, PreferentialDto> preferentialHelp = {};

  ///钱箱新增记录参数
  CashBoxPaymentRequestDto? cashBoxPayment;

  String? payOutNo;

  bool backScoreGrowth = false;

  ///满件赠（赠送优惠券列表）todo 这里实在是改不动，先保持这个，后续删除
  List<GoodsDetailDto> giftCouponListOld = [];

  ///促销优惠券赠品列表 todo 新促销用这个，后续删除上面这个
  List<CouponGift> giftCouponList = [];

  ///促销赠品记录，记录每种促销的id，类型，赠品类型，最大数量
  ///本地用
  Set<PromotionGiftRecord>? promotionGiftRecord;

  ///促销提示(订单满减)
  List<PromotionHints>? promotionHints;

  ///销售单据查询-优惠金额 积分抵现、促销优惠、卡券优惠、整单优惠
  Decimal preferentialTotal = Decimal.zero;

  Decimal preferentialTotalUI = Decimal.zero;

  Decimal getPreferentialTotalUI() {
    ///todo 5.3需要修改 支持换货
    preferentialTotalUI = Decimal.zero;
    //总优惠
    if (vchtype == BillTypeData[BillType.SaleBackBill]) {
      if (inDetail.isNotEmpty) {
        for (var detailDto in originalInDetail) {
          {
            if (detailDto.comboRow) continue;
            preferentialTotalUI = MathUtil.add(
                preferentialTotalUI.toString(),
                MathUtil.subtraction(
                        detailDto.currencyPreferentialTotal.toString(),
                        detailDto.currencyGivePreferentialTotal.toString())
                    .toString());
          }
        }
      }
    } else if (vchtype == BillTypeData[BillType.SaleBill]) {
      if (originalDetail.isNotEmpty) {
        for (var detailDto in originalDetail) {
          {
            if (detailDto.comboRow) continue;
            preferentialTotalUI = MathUtil.add(
                preferentialTotalUI.toString(),
                MathUtil.subtraction(
                        detailDto.currencyPreferentialTotal.toString(),
                        detailDto.currencyGivePreferentialTotal.toString())
                    .toString());
          }
        }
      }
    } else {
      //换货单
      for (var detailDto in originalDetail) {
        {
          if (detailDto.comboRow) continue;
          preferentialTotalUI = MathUtil.add(
              preferentialTotalUI.toString(),
              MathUtil.subtraction(
                      detailDto.currencyPreferentialTotal.toString(),
                      detailDto.currencyGivePreferentialTotal.toString())
                  .toString());
        }
      }
    }

    return preferentialTotalUI;
  }

  /// 优惠
  String discountPreferential = "0";

  ///单品优惠金额汇总，即所有明细行促销优惠金额汇总
  num currencyPtypePreferentialTotal = 0;

  String? kfullname;
  String? kfullname2;
  String? ktypeId;
  String? ktypeId2;

  ///退货单据时的商品列表
  List<GoodsDetailDto> inDetail = [];

  ///销售单据时的商品列表
  List<GoodsDetailDto> outDetail = [];

  List<GoodsDetailDto> originalDetail = [];
  List<GoodsDetailDto> originalInDetail = [];

  ///原单金额
  num originalTotal = 0;

  ///原单赠金优惠
  num originalCurrencyGivePreferentialTotal = 0;

  int? intVchtype;

  String? afullname;
  int? balanceType;

  int? freightCollectType;
  String? paymentDate;
  String? paymentType;
  String? freightBtypeId;

  ///费用分摊类型
  int? shareType;

  List<FeeDto?>? fee;

// // /**
// //  * 多对多的费用信息 销售费用/采购费用
// //  */
  List<FeeDto>? complexFee;

  String? autoShareFreightfeeTotal;
  String? autoShareFeeTotal;

  String? freightBillNo;
  num? currencyFreightCollectTotal;

  List<PaymentDto> sourcePayment = [];

  num? currencyBuyerFreightFee; //买家费用
  int? currencyDepositTotal;
  int? queryPostState;
  double? arTotal; //应收 收银
  double? apTotal; //应付

  String? freightAtypeName;
  String? freightBtypeName;
  String? freightFee;
  String? freightaTypeId;
  String? freightaTypeTotal;

  List<BillVipAssertChange>? vipAsserts;

  ///资产变动操作来源：
  ///0=新增会员；1=会员升级；2=普通发放；3=门店零售；4=积分兑换；5=赠送积分；6=会员充值：7=积分过期；8=卡券到期; 9=解绑权益卡
  int? sourceOperation;

  GoodsBillDto() : super();

  GoodsBillDto.fromMap(Map<String, dynamic> map) : super.fromMap(map) {
    payState = map["payState"];
    cashBoxPayment = map["cashBoxPayment"] != null
        ? CashBoxPaymentRequestDto.fromMap(map["cashBoxPayment"])
        : null;
    couponMemo = map["couponMemo"] ?? "";
    tips = map["tips"] ?? "";
    preferentialHelp = {}
      ..addAll(((map["preferentialHelp"] ?? {}) as Map).map((key, value) {
        if (value is Map) {
          return MapEntry(key, PreferentialDto.fromMap(value));
        } else {
          return MapEntry(key, value);
        }
      }));
    giftCouponListOld = (map['giftCouponListOld'] as List?)?.map((o) {
          return o is GoodsDetailDto ? o : GoodsDetailDto.fromMap(o);
        }).toList() ??
        [];
    giftCouponList = (map['giftCouponList'] as List?)
            ?.map((o) => CouponGift.fromMap(o))
            .toList() ??
        [];
    promotionGiftRecord = (map['promotionGiftRecord'] as List?)
        ?.map((o) => PromotionGiftRecord.fromMap(o))
        .toSet();
    discountPreferential = map["discountPreferential"] ?? "0";
    preferentialTotal = Decimal.fromJson(map["preferentialTotal"] == null
        ? "0"
        : map["preferentialTotal"].toString());
    afullname = map['afullname'];
    atypeId = map['atypeId'];
    balanceType = map['balanceType'];
    bfullname = map['bfullname'];
    btypeId = map['btypeId'];
    payOutNo = map['payOutNo'];
    currencyBuyerFreightFee = map['currencyBuyerFreightFee'];
    currencyDepositTotal = map['currencyDepositTotal'];
    currencyFreightCollectTotal = map['currencyFreightCollectTotal'];
    fee = (map['fee'] as List?)?.map((o) {
          return o is FeeDto ? o : FeeDto.fromMap(o);
        }).toList() ??
        [];
    freightBillNo = map['freightBillNo'];
    freightBtypeId = map['freightBtypeId'];
    freightaTypeTotal = map['freightaTypeTotal'];
    freightFee = map['freightFee'];
    freightBtypeName = map['freightBtypeName'];
    freightAtypeName = map['freightAtypeName'];
    freightaTypeId = map['freightaTypeId'];
    freightaTypeTotal = map['freightaTypeTotal'];
    freightCollectType = map[' freightCollectType'];
    inDetail = (map['inDetail'] as List?)?.map((o) {
          return o is GoodsDetailDto ? o : GoodsDetailDto.fromMap(o);
        }).toList() ??
        [];
    kfullname = map['kfullname'];
    kfullname2 = map['kfullname2'];
    ktypeId = map['ktypeId'];
    ktypeId2 = map['ktypeId2'];
    outDetail = (map['outDetail'] as List?)?.map((o) {
          return o is GoodsDetailDto ? o : GoodsDetailDto.fromMap(o);
        }).toList() ??
        [];
    sourcePayment = (map['sourcePayment'] as List?)?.map((o) {
          return o is PaymentDto ? o : PaymentDto.fromMap(o);
        }).toList() ??
        [];
    paymentDate = map['paymentDate'];
    paymentType = map['paymentType'];
    queryPostState = map['queryPostState'];
    arTotal = map['arTotal'];
    apTotal = map['apTotal'];
    complexFee = [
      ...(map['complexFee'] is List ? map['complexFee'] as List : []).map((o) {
        return o is FeeDto ? o : FeeDto.fromMap(o);
      })
    ];
    autoShareFreightfeeTotal = map['autoShareFreightfeeTotal'];
    autoShareFeeTotal = map['autoShareFeeTotal'];
    shareType = map['shareType'];
    currencyOrderPreferentialAllotTotal =
        map["currencyOrderPreferentialAllotTotal"] ?? 0;
    vipBillInfo = map["vipBillInfo"] != null
        ? VipBillInfoBean.fromMap(map["vipBillInfo"])
        : null;
    backScoreGrowth = map["backScoreGrowth"] ?? false;

    promotionHints = (map["promotionHints"] as List?)
        ?.map((e) => PromotionHints.fromMap(e))
        .toList();
    currencyPtypePreferentialTotal = map["currencyPtypePreferentialTotal"] ?? 0;
    originalTotal = map["originalTotal"] ?? 0;
    originalCurrencyGivePreferentialTotal =
        map["originalCurrencyGivePreferentialTotal"] ?? 0;
    vipAsserts = (map["vipAsserts"] as List?)
        ?.map((o) => BillVipAssertChange.fromMap(o))
        .toList();
    sourceOperation = map["sourceOperation"];
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = super.toJson();
    List outDetailJson = [];
    for (GoodsDetailDto goodsDetailDto in outDetail) {
      outDetailJson.add(goodsDetailDto.toJson());
    }
    List inDetailJson = [];
    for (GoodsDetailDto goodsDetailDto in inDetail) {
      inDetailJson.add(goodsDetailDto.toJson());
    }
    List giftCouponJson = [];
    for (GoodsDetailDto goodsDetailDto in giftCouponListOld) {
      giftCouponJson.add(goodsDetailDto.toJson());
    }
    map.addAll({
      //todo 是否写入资产.
      "isProperty": false,
      "payState": payState,
      "couponMemo": couponMemo,
      "tips": tips,
      "preferentialTotal": preferentialTotal,
      "payOutNo": payOutNo,
      "giftCouponListOld": giftCouponJson,
      "giftCouponList": giftCouponList.map((e) => e.toJson()).toList(),
      "promotionGiftRecord":
          promotionGiftRecord?.map((e) => e.toJson()).toList(),
      "preferentialHelp": preferentialHelp,
      "preferentialList": preferentialHelp.values.toList(),
      "discountPreferential": discountPreferential,
      "afullname": afullname,
      "atypeId": atypeId,
      "balanceType": balanceType,
      "bfullname": bfullname,
      "btypeId": btypeId,
      // "buyer": buyer,
      "currencyAdvanceTotal": currencyAdvanceTotal,
      "currencyBuyerFreightFee": currencyBuyerFreightFee,
      "currencyDepositTotal": currencyDepositTotal,
      "currencyFreightCollectTotal": currencyFreightCollectTotal,
      "fee": fee,
      "freightBillNo": freightBillNo,
      "freightBtypeId": freightBtypeId,
      "freightCollectType": freightCollectType,
      "freightAtypeName": freightAtypeName,
      "freightBtypeName": freightBtypeName,
      "freightFee": freightFee,
      "freightaTypeId": freightaTypeId,
      "freightaTypeTotal": freightaTypeTotal,
      "inDetail": inDetailJson,
      "kfullname": kfullname,
      "kfullname2": kfullname2,
      "ktypeId": ktypeId,
      "ktypeId2": ktypeId2,
      "outDetail": outDetailJson,
      "sourcePayment": sourcePayment.map((e) => e.toJson()).toList(),
      "paymentDate": paymentDate,
      "paymentType": paymentType,
      "queryPostState": queryPostState,
      "arTotal": arTotal,
      "apTotal": apTotal,
      "complexFee": complexFee,
      "autoShareFreightfeeTotal": autoShareFreightfeeTotal,
      "autoShareFeeTotal": autoShareFeeTotal,
      "shareType": shareType,
      "cashBoxPayment": cashBoxPayment?.toJson(),
      "vipBillInfo": vipBillInfo?.toJson(),
      "backScoreGrowth": backScoreGrowth,
      "promotionHints": promotionHints?.map((e) => e.toJson()).toList(),
      "currencyPtypePreferentialTotal": currencyPtypePreferentialTotal,
      "currencyGivePreferentialTotal": currencyGivePreferentialTotal,
      "originalTotal": originalTotal,
      "originalCurrencyGivePreferentialTotal":
          originalCurrencyGivePreferentialTotal,
      "vipAsserts": vipAsserts?.map((e) => e.toJson()).toList(),
      "sourceOperation": sourceOperation,
    });
    return map;
  }
}
