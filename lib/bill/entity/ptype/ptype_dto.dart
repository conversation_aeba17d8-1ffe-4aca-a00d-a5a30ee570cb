import 'dart:convert';

import 'package:haloui/widget/halo_chip.dart';

import '../../../bill/entity/ptype/ptype_fullbarcode_dto.dart';
import '../../../common/tool/sp_tool.dart';

/// allowDelUnusedSKU : true
/// barcode : "string"
/// batchenabled : true
/// brandId : 0
/// brandName : "string"
/// buyDefaultUnit : 0
/// buyDefaultUnitCode : 0
/// classed : true
/// costMode : 0
/// costPrice : 0
/// createTime : "2021-06-22T02:47:27.838Z"
/// createType : 0
/// deleted : true
/// difficultyLevel : 0
/// fullname : "string"
/// id : 0
/// ktypeLimit : true
/// lengthUnit : 0
/// lithiumBattery : true
/// memo : "string"
/// namepy : "string"
/// parFullname : "string"
/// partypeid : "string"
/// pcategory : 0
/// pics : [{"createTime":"2021-06-22T02:47:27.838Z","id":0,"picName":"string","picUrl":"string","profileId":0,"ptypeId":0,"rowindex":0,"updateTime":"2021-06-22T02:47:27.838Z"}]
/// priceList : [{"buyPrice":0,"createTime":"2021-06-22T02:47:27.838Z","id":0,"lastBuyPrice":0,"lastBuyTime":"2021-06-22T02:47:27.838Z","lastSalePrice":0,"lastSaleTime":"2021-06-22T02:47:27.838Z","minSalePrice":0,"preprice1":0,"preprice10":0,"preprice2":0,"preprice3":0,"preprice4":0,"preprice5":0,"preprice6":0,"preprice7":0,"preprice8":0,"preprice9":0,"profileId":0,"ptypeId":0,"retailPrice":0,"sku":{"costPrice":0,"createTime":"2021-06-22T02:47:27.838Z","deleted":true,"hashCode":0,"id":0,"memo":"string","picUrl":"string","profileId":0,"propId1":0,"propId2":0,"propId3":0,"propId4":0,"propId5":0,"propId6":0,"propName1":"string","propName2":"string","propName3":"string","propName4":"string","propName5":"string","propName6":"string","propNames":"string","propvalueId1":0,"propvalueId2":0,"propvalueId3":0,"propvalueId4":0,"propvalueId5":0,"propvalueId6":0,"propvalueName1":"string","propvalueName2":"string","propvalueName3":"string","propvalueName4":"string","propvalueName5":"string","propvalueName6":"string","propvalueNames":"string","ptypeId":0,"stoped":true,"updateTime":"2021-06-22T02:47:27.838Z"},"skuId":0,"unitCode":0,"unitId":0,"updateTime":"2021-06-22T02:47:27.838Z"}]
/// profileId : 0
/// propenabled : true
/// props : [{"createTime":"2021-06-22T02:47:27.838Z","deleted":true,"id":0,"memo":"string","profileId":0,"propName":"string","rowindex":0,"stoped":true,"updateTime":"2021-06-22T02:47:27.838Z"}]
/// propvalues : [{"barcode":"string","createTime":"2021-06-22T02:47:27.838Z","deleted":true,"id":0,"memo":"string","profileId":0,"propId":0,"propvalueName":"string","rowindex":0,"stoped":true,"updateTime":"2021-06-22T02:47:27.838Z"}]
/// propvaluesDescartCount : 0
/// protectDays : 0
/// protectDaysUnit : 0
/// protectDaysView : 0
/// protectWarndays : 0
/// protectWarndaysUnit : 0
/// protectWarndaysView : 0
/// ptypeArea : "string"
/// ptypeBatchUpdateType : "string"
/// ptypeBtypeRelations : [{"allused":true,"bcategory":0,"btypeId":0,"createTime":"2021-06-22T02:47:27.838Z","defaulted":true,"fullname":"string","id":0,"profileId":0,"ptypeId":0,"shortname":"string","stoped":true,"updateTime":"2021-06-22T02:47:27.838Z","usercode":"string"}]
/// ptypeHeight : 0
/// ptypeKtypeRelations : [{"createTime":"2021-06-22T02:47:27.839Z","defaulted":true,"fullname":"string","id":0,"ktypeId":0,"memo":"string","profileId":0,"ptypeId":0,"scategory":0,"shortname":"string","stoped":true,"updateTime":"2021-06-22T02:47:27.839Z","usercode":"string"}]
/// ptypeLength : 0
/// ptypeOtypeRelations : [{"createTime":"2021-06-22T02:47:27.839Z","fullname":"string","id":0,"otypeId":0,"profileId":0,"ptypeId":0,"updateTime":"2021-06-22T02:47:27.839Z","usercode":"string"}]
/// ptypeType : "string"
/// ptypeWidth : 0
/// ptypeidlist : [0]
/// retailDefaultUnit : 0
/// retailDefaultUnitCode : 0
/// rowindex : 0
/// saleDefaultUnit : 0
/// saleDefaultUnitCode : 0
/// shortname : "string"
/// skuPrice : 0
/// skus : [{"costPrice":0,"createTime":"2021-06-22T02:47:27.839Z","deleted":true,"hashCode":0,"id":0,"memo":"string","picUrl":"string","profileId":0,"propId1":0,"propId2":0,"propId3":0,"propId4":0,"propId5":0,"propId6":0,"propName1":"string","propName2":"string","propName3":"string","propName4":"string","propName5":"string","propName6":"string","propNames":"string","propvalueId1":0,"propvalueId2":0,"propvalueId3":0,"propvalueId4":0,"propvalueId5":0,"propvalueId6":0,"propvalueName1":"string","propvalueName2":"string","propvalueName3":"string","propvalueName4":"string","propvalueName5":"string","propvalueName6":"string","propvalueNames":"string","ptypeId":0,"stoped":true,"updateTime":"2021-06-22T02:47:27.839Z"}]
/// snenabled : 0
/// solid : true
/// standard : "string"
/// stockDefaultUnit : 0
/// stockDefaultUnitCode : 0
/// stoped : true
/// supplyInfo : "string"
/// syncStock : true
/// taxNumber : "string"
/// taxRate : 0
/// typeid : "string"
/// units : [{"barcode":"string","createTime":"2021-06-22T02:47:27.839Z","id":0,"profileId":0,"ptypeId":0,"unitCode":0,"unitName":"string","unitRate":0,"updateTime":"2021-06-22T02:47:27.839Z"}]
/// updateTime : "2021-06-22T02:47:27.839Z"
/// usercode : "string"
/// weight : 0
/// weightUnit : 0
/// weighted : true

class PtypeDto {
  bool? allowDelUnusedSKU = false;
  String? barcode;
  bool? batchenabled = false;
  String? brandId;
  String? brandName;
  String? buyDefaultUnit;
  int? buyDefaultUnitCode;
  bool? classed;
  int? costMode;
  String? costPrice;
  String? createTime;
  int? createType;
  bool? deleted;
  String? difficultyLevel;
  String? fullname = "";
  String? id;
  bool? ktypeLimit;
  int? lengthUnit = 0;
  bool? lithiumBattery;
  String? memo;
  String? namepy;
  String? parFullname;
  String? partypeid;
  int? pcategory;
  List<PicsBean>? pics;
  List<PriceListBean>? priceList;
  String? profileId;
  bool? propenabled = false;
  List<PropsBean>? props;
  List<PropvaluesBean>? propvalues;
  int? propvaluesDescartCount;
  int? protectDays = 0;
  int? protectDaysUnit;
  String? protectDaysView = "0";
  int? protectWarndays = 0;
  int? protectWarndaysUnit;
  String? protectWarndaysView = "0";
  String? ptypeArea;
  String? ptypeBatchUpdateType;
  List<PtypeBtypeRelationsBean>? ptypeBtypeRelations;
  double ptypeHeight = 0;
  List<PtypeKtypeRelationsBean>? ptypeKtypeRelations;
  double ptypeLength = 0;
  List<PtypeOtypeRelationsBean>? ptypeOtypeRelations;
  String? ptypeType;
  double ptypeWidth = 0;
  List<String?>? ptypeidlist;
  String? retailDefaultUnit;
  int? retailDefaultUnitCode;
  String? rowindex;
  String? saleDefaultUnit;
  int? saleDefaultUnitCode;
  String? shortname;
  int? skuPrice = 0;
  List<SkuBean?>? skus;
  String? snenabled = "0";
  bool? solid;
  String? standard;
  String? stockDefaultUnit;
  int? stockDefaultUnitCode;
  bool? stoped;
  String? supplyInfo;
  bool? syncStock;
  String? taxNumber;
  String? taxRate;
  String? typeid;
  List<UnitsBean>? units;
  String? updateTime;
  String? usercode;
  String? weight;
  String? weightUnit = "1";
  bool? weighted;
  List<PtypeFullbarcodeDto>? fullbarcodes;

  static PtypeDto fromMap(Map<String?, dynamic>? map) {
    if (map == null) return PtypeDto();
    PtypeDto ptypeDtoBean = PtypeDto();
    ptypeDtoBean.allowDelUnusedSKU = map['allowDelUnusedSKU'];
    ptypeDtoBean.barcode = map['barcode'];
    ptypeDtoBean.batchenabled = map['batchenabled'];
    ptypeDtoBean.brandId = map['brandId'];
    ptypeDtoBean.brandName = map['brandName'];
    ptypeDtoBean.buyDefaultUnit = map['buyDefaultUnit'];
    ptypeDtoBean.buyDefaultUnitCode = map['buyDefaultUnitCode'] is String?
        ? int?.parse(map['buyDefaultUnitCode'])
        : map['buyDefaultUnitCode'];
    ptypeDtoBean.classed = map['classed'];
    ptypeDtoBean.costMode = map['costMode'] is String?
        ? int?.parse(map['costMode'])
        : map['costMode'];
    ptypeDtoBean.costPrice = map['costPrice'] is double
        ? map['costPrice'].toString()
        : map['costPrice'];
    ptypeDtoBean.createTime = map['createTime'];
    ptypeDtoBean.createType = map['createType'] is String?
        ? int?.parse(map['createType'])
        : map['createType'];
    ptypeDtoBean.deleted = map['deleted'];
    ptypeDtoBean.difficultyLevel = map['difficultyLevel'];
    ptypeDtoBean.fullname = map['fullname'];
    ptypeDtoBean.id = map['id'];
    ptypeDtoBean.ktypeLimit = map['ktypeLimit'];
    ptypeDtoBean.lengthUnit = map['lengthUnit'] is String?
        ? int?.parse(map['lengthUnit'])
        : map['lengthUnit'];
    ptypeDtoBean.lithiumBattery = map['lithiumBattery'];
    ptypeDtoBean.memo = map['memo'];
    ptypeDtoBean.namepy = map['namepy'];
    ptypeDtoBean.parFullname = map['parFullname'];
    ptypeDtoBean.partypeid = map['partypeid'];
    ptypeDtoBean.pcategory = map['pcategory'] is String?
        ? int?.parse(map['pcategory'])
        : map['pcategory'];
    ptypeDtoBean.pics =
        (map['pics'] as List ?? []).map((o) => PicsBean.fromMap(o)).toList();
    ptypeDtoBean.priceList = (map['priceList'] as List ?? [])
        .map((o) => PriceListBean.fromMap(o))
        .toList();
    ptypeDtoBean.profileId = map['profileId'];
    ptypeDtoBean.propenabled = map['propenabled'];
    ptypeDtoBean.props =
        (map['props'] as List ?? []).map((o) => PropsBean.fromMap(o)).toList();
    ptypeDtoBean.propvalues = (map['propvalues'] as List ?? [])
        .map((o) => PropvaluesBean.fromMap(o))
        .toList();
    ptypeDtoBean.propvaluesDescartCount = map['propvaluesDescartCount'];
    ptypeDtoBean.protectDays = map['protectDays'];
    ptypeDtoBean.protectDaysUnit = map['protectDaysUnit'] is String?
        ? int?.parse(map['protectDaysUnit'])
        : map['protectDaysUnit'];
    ptypeDtoBean.protectDaysView = map['protectDaysView'].toString();
    ptypeDtoBean.protectWarndays = map['protectWarndays'];
    ptypeDtoBean.protectWarndaysUnit = map['protectWarndaysUnit'] is String?
        ? int?.parse(map['protectWarndaysUnit'])
        : map['protectWarndaysUnit'];
    ptypeDtoBean.protectWarndaysView = map['protectWarndaysView'].toString();
    ptypeDtoBean.ptypeArea = map['ptypeArea'];
    ptypeDtoBean.ptypeBatchUpdateType = map['ptypeBatchUpdateType'];
    ptypeDtoBean.ptypeBtypeRelations =
        (map['ptypeBtypeRelations'] as List ?? [])
            .map((o) => PtypeBtypeRelationsBean.fromMap(o))
            .toList();
    ptypeDtoBean.ptypeHeight = map['ptypeHeight'] is String?
        ? double.parse(map['ptypeHeight'])
        : map['ptypeHeight'];
    ptypeDtoBean.ptypeKtypeRelations =
        (map['ptypeKtypeRelations'] as List ?? [])
            .map((o) => PtypeKtypeRelationsBean.fromMap(o))
            .toList();
    ptypeDtoBean.ptypeLength = map['ptypeLength'] is String?
        ? double.parse(map['ptypeLength'])
        : map['ptypeLength'];
    ptypeDtoBean.ptypeOtypeRelations =
        (map['ptypeOtypeRelations'] as List ?? [])
            .map((o) => PtypeOtypeRelationsBean.fromMap(o))
            .toList();
    ptypeDtoBean.ptypeType = map['ptypeType'];
    ptypeDtoBean.ptypeWidth = map['ptypeWidth'] is String?
        ? double.parse(map['ptypeWidth'])
        : map['ptypeWidth'];
    ptypeDtoBean.ptypeidlist =
        (map['ptypeidlist'] as List ?? []).map((o) => o.toString()).toList();
    ptypeDtoBean.retailDefaultUnit = map['retailDefaultUnit'];
    ptypeDtoBean.retailDefaultUnitCode = map['retailDefaultUnitCode'] is String?
        ? int?.parse(map['retailDefaultUnitCode'])
        : map['retailDefaultUnitCode'];
    ptypeDtoBean.rowindex = map['rowindex'];
    ptypeDtoBean.saleDefaultUnit = map['saleDefaultUnit'];
    ptypeDtoBean.saleDefaultUnitCode = map['saleDefaultUnitCode'] is String?
        ? int?.parse(map['saleDefaultUnitCode'])
        : map['saleDefaultUnitCode'];
    ptypeDtoBean.shortname = map['shortname'];
    ptypeDtoBean.skuPrice = map['skuPrice'];
    ptypeDtoBean.skus =
        ((map['skus'] ?? []) as List).map((o) => SkuBean.fromMap(o)).toList();
    ptypeDtoBean.snenabled = map['snenabled'].toString();
    ptypeDtoBean.solid = map['solid'];
    ptypeDtoBean.standard = map['standard'];
    ptypeDtoBean.stockDefaultUnit = map['stockDefaultUnit'];
    ptypeDtoBean.stockDefaultUnitCode = map['stockDefaultUnitCode'] is String?
        ? int?.parse(map['stockDefaultUnitCode'])
        : map['stockDefaultUnitCode'];
    ptypeDtoBean.stoped = map['stoped'];
    ptypeDtoBean.supplyInfo = map['supplyInfo'];
    ptypeDtoBean.syncStock = map['syncStock'];
    ptypeDtoBean.taxNumber = map['taxNumber'];
    ptypeDtoBean.taxRate = map['taxRate'];
    ptypeDtoBean.typeid = map['typeid'];
    ptypeDtoBean.units =
        (map['units'] as List ?? []).map((o) => UnitsBean.fromMap(o)).toList();
    ptypeDtoBean.updateTime = map['updateTime'];
    ptypeDtoBean.usercode = map['usercode'];
    ptypeDtoBean.weight = map['weight'];
    ptypeDtoBean.weightUnit = map['weightUnit'].toString();
    ptypeDtoBean.weighted = map['weighted'];
    ptypeDtoBean.fullbarcodes = (map['fullbarcodes'] as List ?? [])
        .map((o) => PtypeFullbarcodeDto.fromMap(o))
        .toList();
    return ptypeDtoBean;
  }

  Map<String?, dynamic> toJson() => {
        "allowDelUnusedSKU": allowDelUnusedSKU,
        "barcode": barcode,
        "batchenabled": batchenabled,
        "brandId": brandId,
        "brandName": brandName,
        "buyDefaultUnit": buyDefaultUnit,
        "buyDefaultUnitCode": buyDefaultUnitCode,
        "classed": classed,
        "costMode": costMode,
        "costPrice": costPrice,
        "createTime": createTime,
        "createType": createType,
        "deleted": deleted,
        "difficultyLevel": difficultyLevel,
        "fullname": fullname,
        "id": id,
        "ktypeLimit": ktypeLimit,
        "lengthUnit": lengthUnit,
        "lithiumBattery": lithiumBattery,
        "memo": memo,
        "namepy": namepy,
        "parFullname": parFullname,
        "partypeid": partypeid,
        "pcategory": pcategory,
        "pics": jsonDecode(jsonEncode(pics)),
        "priceList": jsonDecode(jsonEncode(priceList)),
        "profileId": profileId,
        "propenabled": propenabled,
        "props": jsonDecode(jsonEncode(props)),
        "propvalues": jsonDecode(jsonEncode(propvalues)),
        "propvaluesDescartCount": propvaluesDescartCount,
        "protectDays": protectDays,
        "protectDaysUnit": protectDaysUnit,
        "protectDaysView": protectDaysView,
        "protectWarndays": protectWarndays,
        "protectWarndaysUnit": protectWarndaysUnit,
        "protectWarndaysView": protectWarndaysView,
        "ptypeArea": ptypeArea,
        "ptypeBatchUpdateType": ptypeBatchUpdateType,
        "ptypeBtypeRelations": jsonDecode(jsonEncode(ptypeBtypeRelations)),
        "ptypeHeight": ptypeHeight,
        "ptypeKtypeRelations": jsonDecode(jsonEncode(ptypeKtypeRelations)),
        "ptypeLength": ptypeLength,
        "ptypeOtypeRelations": jsonDecode(jsonEncode(ptypeOtypeRelations)),
        "ptypeType": ptypeType,
        "ptypeWidth": ptypeWidth,
        "ptypeidlist": jsonDecode(jsonEncode(ptypeidlist)),
        "retailDefaultUnit": retailDefaultUnit,
        "retailDefaultUnitCode": retailDefaultUnitCode,
        "rowindex": rowindex,
        "saleDefaultUnit": saleDefaultUnit,
        "saleDefaultUnitCode": saleDefaultUnitCode,
        "shortname": shortname,
        "skuPrice": skuPrice,
        "skus": jsonDecode(jsonEncode(skus)),
        "snenabled": snenabled,
        "solid": solid,
        "standard": standard,
        "stockDefaultUnit": stockDefaultUnit,
        "stockDefaultUnitCode": stockDefaultUnitCode,
        "stoped": stoped,
        "supplyInfo": supplyInfo,
        "syncStock": syncStock,
        "taxNumber": taxNumber,
        "taxRate": taxRate,
        "typeid": typeid,
        "units": jsonDecode(jsonEncode(units)),
        "updateTime": updateTime,
        "usercode": usercode,
        "weight": weight,
        "weightUnit": weightUnit,
        "weighted": weighted,
        "fullbarcodes": jsonDecode(jsonEncode(fullbarcodes)),
      };
}

/// barcode : "string"
/// createTime : "2021-06-22T02:47:27.839Z"
/// id : 0
/// profileId : 0
/// ptypeId : 0
/// unitCode : 0
/// unitName : "string"
/// unitRate : 0
/// updateTime : "2021-06-22T02:47:27.839Z"

class PriceBean {
  String? priceName;
  String? price;
}

class UnitsBean {
  String? barcode;
  double? buyPrice;
  String? createTime;
  String? id;
  double? minSalePrice;
  double? preprice1;
  double? preprice10;
  double? preprice2;
  double? preprice3;
  double? preprice4;
  double? preprice5;
  double? preprice6;
  double? preprice7;
  double? preprice8;
  double? preprice9;
  String? profileId;
  String? ptypeId;
  double? retailPrice;
  int? unitCode;
  String? unitName;
  num? unitRate;
  String? updateTime;
  num? ptypeWeight;

  static UnitsBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return UnitsBean();
    UnitsBean unitBean = UnitsBean();
    unitBean.barcode = map['barcode'];
    unitBean.buyPrice = map['buyPrice'];
    unitBean.createTime = map['createTime'];
    unitBean.id = map['id'];
    unitBean.minSalePrice = map['minSalePrice'];
    unitBean.preprice1 = map['preprice1'];
    unitBean.preprice10 = map['preprice10'];
    unitBean.preprice2 = map['preprice2'];
    unitBean.preprice3 = map['preprice3'];
    unitBean.preprice4 = map['preprice4'];
    unitBean.preprice5 = map['preprice5'];
    unitBean.preprice6 = map['preprice6'];
    unitBean.preprice7 = map['preprice7'];
    unitBean.preprice8 = map['preprice8'];
    unitBean.preprice9 = map['preprice9'];
    unitBean.profileId = map['profileId'];
    unitBean.ptypeId = map['ptypeId'];
    unitBean.retailPrice = map['retailPrice'];
    unitBean.unitCode = map['unitCode'];
    unitBean.unitName = map['unitName'];
    unitBean.unitRate = map['unitRate'];
    unitBean.updateTime = map['updateTime'];
    unitBean.ptypeWeight = map["ptypeWeight"];
    return unitBean;
  }

  Map<String, dynamic> toJson() => {
        "barcode": barcode,
        "buyPrice": buyPrice,
        "createTime": createTime,
        "id": id,
        "minSalePrice": minSalePrice,
        "preprice1": preprice1,
        "preprice10": preprice10,
        "preprice2": preprice2,
        "preprice3": preprice3,
        "preprice4": preprice4,
        "preprice5": preprice5,
        "preprice6": preprice6,
        "preprice7": preprice7,
        "preprice8": preprice8,
        "preprice9": preprice9,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "retailPrice": retailPrice,
        "unitCode": unitCode,
        "unitName": unitName,
        "unitRate": unitRate,
        "updateTime": updateTime,
        "ptypeWeight": ptypeWeight
      };

  deleteUnitNormal(PtypeDto ptypeDto, PriceListBean priceBean) {
    if (id == ptypeDto.buyDefaultUnit) {
      ptypeDto.buyDefaultUnit = ptypeDto.units?.first.id;
      ptypeDto.buyDefaultUnitCode = ptypeDto.units?.first.unitCode;
    } else if (id == ptypeDto.retailDefaultUnit) {
      ptypeDto.retailDefaultUnit = ptypeDto.units?.first.id;
      ptypeDto.retailDefaultUnitCode = ptypeDto.units?.first.unitCode;
    } else if (id == ptypeDto.saleDefaultUnit) {
      ptypeDto.saleDefaultUnit = ptypeDto.units?.first.id;
      ptypeDto.saleDefaultUnitCode = ptypeDto.units?.first.unitCode;
    } else if (id == ptypeDto.stockDefaultUnit) {
      ptypeDto.stockDefaultUnit = ptypeDto.units?.first.id;
      ptypeDto.stockDefaultUnitCode = ptypeDto.units?.first.unitCode;
    }
    ptypeDto.units?.remove(this);
    ptypeDto.priceList?.remove(priceBean);
    for (int i = 0; i < ptypeDto.units!.length; i++) {
      UnitsBean unitsBean = ptypeDto.units![i];
      unitsBean.unitCode = i + 1;
      PriceListBean priceListBean = ptypeDto.priceList![i];
      priceListBean.unitCode = i + 1;
    }
  }

  deleteUnitSku(PtypeDto ptypeDto) {
    if (id == ptypeDto.buyDefaultUnit) {
      ptypeDto.buyDefaultUnit = ptypeDto.units?.first.id;
      ptypeDto.buyDefaultUnitCode = ptypeDto.units?.first.unitCode;
    } else if (id == ptypeDto.retailDefaultUnit) {
      ptypeDto.retailDefaultUnit = ptypeDto.units?.first.id;
      ptypeDto.retailDefaultUnitCode = ptypeDto.units?.first.unitCode;
    } else if (id == ptypeDto.saleDefaultUnit) {
      ptypeDto.saleDefaultUnit = ptypeDto.units?.first.id;
      ptypeDto.saleDefaultUnitCode = ptypeDto.units?.first.unitCode;
    } else if (id == ptypeDto.stockDefaultUnit) {
      ptypeDto.stockDefaultUnit = ptypeDto.units?.first.id;
      ptypeDto.stockDefaultUnitCode = ptypeDto.units?.first.unitCode;
    }
    ptypeDto.priceList?.removeWhere((element) {
      return element.unitCode == unitCode;
    });
    ptypeDto.units?.remove(this);
    for (int i = 0; i < ptypeDto.units!.length; i++) {
      UnitsBean unitsBean = ptypeDto.units![i];
      int? unitCode = i + 1;
      for (PriceListBean priceListBean in ptypeDto.priceList!) {
        if (priceListBean.unitCode == unitsBean.unitCode) {
          priceListBean.unitCode = unitCode;
        }
      }
      unitsBean.unitCode = unitCode;
    }
  }

  static PriceListBean findPriceListBean(
      UnitsBean unitsBean, List<PriceListBean> list) {
    PriceListBean bean = PriceListBean();
    for (int i = 0; i < list.length; i++) {
      PriceListBean element = list[i];
      if (element.unitId == unitsBean.id &&
          element.unitId != "0" &&
          unitsBean.id != "0") {
        bean = element;
      } else if (element.unitId == "0" &&
          unitsBean.id == "0" &&
          element.unitCode == unitsBean.unitCode) {
        bean = element;
      }
    }
    return bean;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UnitsBean && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// costPrice : 0
/// createTime : "2021-06-22T02:47:27.839Z"
/// deleted : true
/// hashCode : 0
/// id : 0
/// memo : "string"
/// picUrl : "string"
/// profileId : 0
/// propId1 : 0
/// propId2 : 0
/// propId3 : 0
/// propId4 : 0
/// propId5 : 0
/// propId6 : 0
/// propName1 : "string"
/// propName2 : "string"
/// propName3 : "string"
/// propName4 : "string"
/// propName5 : "string"
/// propName6 : "string"
/// propNames : "string"
/// propvalueId1 : 0
/// propvalueId2 : 0
/// propvalueId3 : 0
/// propvalueId4 : 0
/// propvalueId5 : 0
/// propvalueId6 : 0
/// propvalueName1 : "string"
/// propvalueName2 : "string"
/// propvalueName3 : "string"
/// propvalueName4 : "string"
/// propvalueName5 : "string"
/// propvalueName6 : "string"
/// propvalueNames : "string"
/// ptypeId : 0
/// stoped : true
/// updateTime : "2021-06-22T02:47:27.839Z"

/// createTime : "2021-06-22T02:47:27.839Z"
/// fullname : "string"
/// id : 0
/// otypeId : 0
/// profileId : 0
/// ptypeId : 0
/// updateTime : "2021-06-22T02:47:27.839Z"
/// usercode : "string"

class PtypeOtypeRelationsBean {
  String? createTime;
  String? fullname;
  String? id;
  String? otypeId;
  String? profileId;
  String? ptypeId;
  String? updateTime;
  String? usercode;

  static PtypeOtypeRelationsBean fromMap(Map<String?, dynamic> map) {
    PtypeOtypeRelationsBean ptypeOtypeRelationsBean = PtypeOtypeRelationsBean();
    ptypeOtypeRelationsBean.createTime = map['createTime'];
    ptypeOtypeRelationsBean.fullname = map['fullname'];
    ptypeOtypeRelationsBean.id = map['id'];
    ptypeOtypeRelationsBean.otypeId = map['otypeId'];
    ptypeOtypeRelationsBean.profileId = map['profileId'];
    ptypeOtypeRelationsBean.ptypeId = map['ptypeId'];
    ptypeOtypeRelationsBean.updateTime = map['updateTime'];
    ptypeOtypeRelationsBean.usercode = map['usercode'];
    return ptypeOtypeRelationsBean;
  }

  Map<String, dynamic> toJson() => {
        "createTime": createTime,
        "fullname": fullname,
        "id": id,
        "otypeId": otypeId,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "updateTime": updateTime,
        "usercode": usercode,
      };
}

/// createTime : "2021-06-22T02:47:27.839Z"
/// defaulted : true
/// fullname : "string"
/// id : 0
/// ktypeId : 0
/// memo : "string"
/// profileId : 0
/// ptypeId : 0
/// scategory : 0
/// shortname : "string"
/// stoped : true
/// updateTime : "2021-06-22T02:47:27.839Z"
/// usercode : "string"

class PtypeKtypeRelationsBean {
  String? createTime;
  bool? defaulted;
  String? fullname;
  String? id;
  String? ktypeId;
  String? memo;
  String? profileId;
  String? ptypeId;
  int? scategory;
  String? shortname;
  bool? stoped;
  String? updateTime;
  String? usercode;

  static PtypeKtypeRelationsBean fromMap(Map<String?, dynamic> map) {
    if (map == null) return PtypeKtypeRelationsBean();
    PtypeKtypeRelationsBean ptypeKtypeRelationsBean = PtypeKtypeRelationsBean();
    ptypeKtypeRelationsBean.createTime = map['createTime'];
    ptypeKtypeRelationsBean.defaulted = map['defaulted'];
    ptypeKtypeRelationsBean.fullname = map['fullname'];
    ptypeKtypeRelationsBean.id = map['id'];
    ptypeKtypeRelationsBean.ktypeId = map['ktypeId'];
    ptypeKtypeRelationsBean.memo = map['memo'];
    ptypeKtypeRelationsBean.profileId = map['profileId'];
    ptypeKtypeRelationsBean.ptypeId = map['ptypeId'];
    ptypeKtypeRelationsBean.scategory = map['scategory'];
    ptypeKtypeRelationsBean.shortname = map['shortname'];
    ptypeKtypeRelationsBean.stoped = map['stoped'];
    ptypeKtypeRelationsBean.updateTime = map['updateTime'];
    ptypeKtypeRelationsBean.usercode = map['usercode'];
    return ptypeKtypeRelationsBean;
  }

  Map<String, dynamic> toJson() => {
        "createTime": createTime,
        "defaulted": defaulted,
        "fullname": fullname,
        "id": id,
        "ktypeId": ktypeId,
        "memo": memo,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "scategory": scategory,
        "shortname": shortname,
        "stoped": stoped,
        "updateTime": updateTime,
        "usercode": usercode,
      };
}

/// allused : true
/// bcategory : 0
/// btypeId : 0
/// createTime : "2021-06-22T02:47:27.838Z"
/// defaulted : true
/// fullname : "string"
/// id : 0
/// profileId : 0
/// ptypeId : 0
/// shortname : "string"
/// stoped : true
/// updateTime : "2021-06-22T02:47:27.838Z"
/// usercode : "string"

class PtypeBtypeRelationsBean {
  bool? allused;
  int? bcategory;
  String? btypeId;
  String? createTime;
  bool? defaulted;
  String? fullname;
  String? id;
  String? profileId;
  String? ptypeId;
  String? shortname;
  bool? stoped;
  String? updateTime;
  String? usercode;

  static PtypeBtypeRelationsBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return PtypeBtypeRelationsBean();
    PtypeBtypeRelationsBean ptypeBtypeRelationsBean = PtypeBtypeRelationsBean();
    ptypeBtypeRelationsBean.allused = map['allused'];
    ptypeBtypeRelationsBean.bcategory = map['bcategory'];
    ptypeBtypeRelationsBean.btypeId = map['btypeId'];
    ptypeBtypeRelationsBean.createTime = map['createTime'];
    ptypeBtypeRelationsBean.defaulted = map['defaulted'];
    ptypeBtypeRelationsBean.fullname = map['fullname'];
    ptypeBtypeRelationsBean.id = map['id'];
    ptypeBtypeRelationsBean.profileId = map['profileId'];
    ptypeBtypeRelationsBean.ptypeId = map['ptypeId'];
    ptypeBtypeRelationsBean.shortname = map['shortname'];
    ptypeBtypeRelationsBean.stoped = map['stoped'];
    ptypeBtypeRelationsBean.updateTime = map['updateTime'];
    ptypeBtypeRelationsBean.usercode = map['usercode'];
    return ptypeBtypeRelationsBean;
  }

  Map<String, dynamic> toJson() => {
        "allused": allused,
        "bcategory": bcategory,
        "btypeId": btypeId,
        "createTime": createTime,
        "defaulted": defaulted,
        "fullname": fullname,
        "id": id,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "shortname": shortname,
        "stoped": stoped,
        "updateTime": updateTime,
        "usercode": usercode,
      };
}

/// barcode : "string"
/// createTime : "2021-06-22T02:47:27.838Z"
/// deleted : true
/// id : 0
/// memo : "string"
/// profileId : 0
/// propId : 0
/// propvalueName : "string"
/// rowindex : 0
/// stoped : true
/// updateTime : "2021-06-22T02:47:27.838Z"

class PropvaluesBean {
  String? barcode;
  String? createTime;
  bool? deleted;
  String? id;
  String? memo;
  String? profileId;
  String? propId;
  String? propvalueName;
  int? rowindex;
  bool? stoped;
  String? updateTime;
  bool? checked = false;
  ChipStatus propsChecked = ChipStatus.noChoose; //属性选择状态

  PropvaluesBean();

  PropvaluesBean.newAttr(String? propId, String? propName, bool? checked,
      ChipStatus propsChecked) {
    this.propId = propId;
    this.propvalueName = propName;
    this.checked = checked;
    this.propsChecked = propsChecked;
  }

  static PropvaluesBean fromMap(Map<dynamic, dynamic> map) {
    PropvaluesBean propvaluesBean = PropvaluesBean();
    propvaluesBean.barcode = map['barcode'];
    propvaluesBean.createTime = map['createTime'];
    propvaluesBean.deleted = map['deleted'];
    propvaluesBean.id = map['id'];
    propvaluesBean.memo = map['memo'];
    propvaluesBean.profileId = map['profileId'];
    propvaluesBean.propId = map['propId'];
    propvaluesBean.propvalueName = map['propvalueName'];
    propvaluesBean.rowindex = map['rowindex'];
    propvaluesBean.stoped = map['stoped'];
    propvaluesBean.updateTime = map['updateTime'];
    return propvaluesBean;
  }

  Map<String, dynamic> toJson() => {
        "barcode": barcode,
        "createTime": createTime,
        "deleted": deleted,
        "id": id,
        "memo": memo,
        "profileId": profileId,
        "propId": propId,
        "propvalueName": propvalueName,
        "rowindex": rowindex,
        "stoped": stoped,
        "updateTime": updateTime,
      };
}

/// createTime : "2021-06-22T02:47:27.838Z"
/// deleted : true
/// id : 0
/// memo : "string"
/// profileId : 0
/// propName : "string"
/// rowindex : 0
/// stoped : true
/// updateTime : "2021-06-22T02:47:27.838Z"

class PropsBean {
  String? createTime;
  bool? deleted;
  String? id;
  String? memo;
  String? profileId;
  String? propName;
  int? rowindex;
  bool? stoped;
  String? updateTime;
  bool? checked = false;

  static PropsBean fromMap(Map<String?, dynamic> map) {
    PropsBean propsBean = PropsBean();
    propsBean.createTime = map['createTime'];
    propsBean.deleted = map['deleted'];
    propsBean.id = map['id'];
    propsBean.memo = map['memo'];
    propsBean.profileId = map['profileId'];
    propsBean.propName = map['propName'];
    propsBean.rowindex = map['rowindex'];
    propsBean.stoped = map['stoped'];
    propsBean.updateTime = map['updateTime'];
    return propsBean;
  }

  Map<String, dynamic> toJson() => {
        "createTime": createTime,
        "deleted": deleted,
        "id": id,
        "memo": memo,
        "profileId": profileId,
        "propName": propName,
        "rowindex": rowindex,
        "stoped": stoped,
        "updateTime": updateTime,
      };
}

/// buyPrice : 0
/// createTime : "2021-06-22T02:47:27.838Z"
/// id : 0
/// lastBuyPrice : 0
/// lastBuyTime : "2021-06-22T02:47:27.838Z"
/// lastSalePrice : 0
/// lastSaleTime : "2021-06-22T02:47:27.838Z"
/// minSalePrice : 0
/// preprice1 : 0
/// preprice10 : 0
/// preprice2 : 0
/// preprice3 : 0
/// preprice4 : 0
/// preprice5 : 0
/// preprice6 : 0
/// preprice7 : 0
/// preprice8 : 0
/// preprice9 : 0
/// profileId : 0
/// ptypeId : 0
/// retailPrice : 0
/// sku : {"costPrice":0,"createTime":"2021-06-22T02:47:27.838Z","deleted":true,"hashCode":0,"id":0,"memo":"string","picUrl":"string","profileId":0,"propId1":0,"propId2":0,"propId3":0,"propId4":0,"propId5":0,"propId6":0,"propName1":"string","propName2":"string","propName3":"string","propName4":"string","propName5":"string","propName6":"string","propNames":"string","propvalueId1":0,"propvalueId2":0,"propvalueId3":0,"propvalueId4":0,"propvalueId5":0,"propvalueId6":0,"propvalueName1":"string","propvalueName2":"string","propvalueName3":"string","propvalueName4":"string","propvalueName5":"string","propvalueName6":"string","propvalueNames":"string","ptypeId":0,"stoped":true,"updateTime":"2021-06-22T02:47:27.838Z"}
/// skuId : 0
/// unitCode : 0
/// unitId : 0
/// updateTime : "2021-06-22T02:47:27.838Z"

class PriceListBean {
  bool? isExpand = false;

  String? buyPrice;
  String? createTime;
  String? id;
  String? lastBuyPrice;
  String? lastBuyTime;
  String? lastSalePrice;
  String? lastSaleTime;
  String? minSalePrice;
  String? preprice1;
  String? preprice10;
  String? preprice2;
  String? preprice3;
  String? preprice4;
  String? preprice5;
  String? preprice6;
  String? preprice7;
  String? preprice8;
  String? preprice9;
  String? profileId;
  String? ptypeId;
  String? retailPrice;
  SkuBean? sku;
  String? skuId;
  int? unitCode;
  String? unitId = "0";
  String? updateTime;

  String? getPrePrice(int? index) {
    switch (index) {
      case 0:
        return preprice1 ?? "";
      case 1:
        return preprice2 ?? "";
      case 2:
        return preprice3 ?? "";
      case 3:
        return preprice4 ?? "";
      case 4:
        return preprice5 ?? "";
      case 5:
        return preprice6 ?? "";
      case 6:
        return preprice7 ?? "";
      case 7:
        return preprice8 ?? "";
      case 8:
        return preprice9 ?? "";
      case 9:
        return preprice10 ?? "";
      default:
        return "0";
    }
  }

  void setPrePrice(int? index, String? price) {
    switch (index) {
      case 0:
        preprice1 = price;
        break;
      case 1:
        preprice2 = price;
        break;
      case 2:
        preprice3 = price;
        break;
      case 3:
        preprice4 = price;
        break;
      case 4:
        preprice5 = price;
        break;
      case 5:
        preprice6 = price;
        break;
      case 6:
        preprice7 = price;
        break;
      case 7:
        preprice8 = price;
        break;
      case 8:
        preprice9 = price;
        break;
      case 9:
        preprice10 = price;
        break;
    }
  }

  static PriceListBean fromMap(Map<String?, dynamic> map) {
    PriceListBean priceListBean = PriceListBean();
    priceListBean.buyPrice = map['buyPrice'];
    priceListBean.createTime = map['createTime'];
    priceListBean.id = map['id'];
    priceListBean.lastBuyPrice = map['lastBuyPrice'];
    priceListBean.lastBuyTime = map['lastBuyTime'];
    priceListBean.lastSalePrice = map['lastSalePrice'];
    priceListBean.lastSaleTime = map['lastSaleTime'];
    priceListBean.minSalePrice = map['minSalePrice'];
    priceListBean.preprice1 = map['preprice1'];
    priceListBean.preprice10 = map['preprice10'];
    priceListBean.preprice2 = map['preprice2'];
    priceListBean.preprice3 = map['preprice3'];
    priceListBean.preprice4 = map['preprice4'];
    priceListBean.preprice5 = map['preprice5'];
    priceListBean.preprice6 = map['preprice6'];
    priceListBean.preprice7 = map['preprice7'];
    priceListBean.preprice8 = map['preprice8'];
    priceListBean.preprice9 = map['preprice9'];
    priceListBean.profileId = map['profileId'];
    priceListBean.ptypeId = map['ptypeId'];
    priceListBean.retailPrice = map['retailPrice'];
    priceListBean.sku = SkuBean.fromMap(map['sku']);
    priceListBean.skuId = map['skuId'];
    priceListBean.unitCode = map['unitCode'];
    priceListBean.unitId = map['unitId'];
    priceListBean.updateTime = map['updateTime'];
    return priceListBean;
  }

  Map<String, dynamic> toJson() => {
        "buyPrice": buyPrice,
        "createTime": createTime,
        "id": id,
        "lastBuyPrice": lastBuyPrice,
        "lastBuyTime": lastBuyTime,
        "lastSalePrice": lastSalePrice,
        "lastSaleTime": lastSaleTime,
        "minSalePrice": minSalePrice,
        "preprice1": preprice1,
        "preprice10": preprice10,
        "preprice2": preprice2,
        "preprice3": preprice3,
        "preprice4": preprice4,
        "preprice5": preprice5,
        "preprice6": preprice6,
        "preprice7": preprice7,
        "preprice8": preprice8,
        "preprice9": preprice9,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "retailPrice": retailPrice,
        "sku": sku,
        "skuId": skuId,
        "unitCode": unitCode,
        "unitId": unitId,
        "updateTime": updateTime,
      };
}

/// costPrice : 0
/// createTime : "2021-06-22T02:47:27.838Z"
/// deleted : true
/// hashCode : 0
/// id : 0
/// memo : "string"
/// picUrl : "string"
/// profileId : 0
/// propId1 : 0
/// propId2 : 0
/// propId3 : 0
/// propId4 : 0
/// propId5 : 0
/// propId6 : 0
/// propName1 : "string"
/// propName2 : "string"
/// propName3 : "string"
/// propName4 : "string"
/// propName5 : "string"
/// propName6 : "string"
/// propNames : "string"
/// propvalueId1 : 0
/// propvalueId2 : 0
/// propvalueId3 : 0
/// propvalueId4 : 0
/// propvalueId5 : 0
/// propvalueId6 : 0
/// propvalueName1 : "string"
/// propvalueName2 : "string"
/// propvalueName3 : "string"
/// propvalueName4 : "string"
/// propvalueName5 : "string"
/// propvalueName6 : "string"
/// propvalueNames : "string"
/// ptypeId : 0
/// stoped : true
/// updateTime : "2021-06-22T02:47:27.838Z"

class SkuBean {
  bool? check = false;
  dynamic costPrice;
  String? createTime;
  bool? deleted;
  String? id;
  String? memo;
  String? picUrl;
  String? profileId;
  String? propId1;
  String? propId2;
  String? propId3;
  String? propId4;
  String? propId5;
  String? propId6;
  String? propName1;
  String? propName2;
  String? propName3;
  String? propName4;
  String? propName5;
  String? propName6;
  String? propNames;
  String? propvalueId1;
  String? propvalueId2;
  String? propvalueId3;
  String? propvalueId4;
  String? propvalueId5;
  String? propvalueId6;
  String? propvalueName1;
  String? propvalueName2;
  String? propvalueName3;
  String? propvalueName4;
  String? propvalueName5;
  String? propvalueName6;
  String? propvalueNames;
  String? ptypeId;
  bool? stoped;
  String? updateTime;
  String? fullbarcode;

  static SkuBean? fromMap(Map<String?, dynamic>? map) {
    if (map == null) return null;
    SkuBean skuBean = SkuBean();
    skuBean.check = map['check'] ?? false;
    skuBean.costPrice = map['costPrice'];
    skuBean.createTime = map['createTime'];
    skuBean.deleted = map['deleted'];
    skuBean.id = map['id'];
    skuBean.memo = map['memo'];
    skuBean.picUrl = map['picUrl'];
    skuBean.profileId = map['profileId'];
    skuBean.propId1 = map['propId1'];
    skuBean.propId2 = map['propId2'];
    skuBean.propId3 = map['propId3'];
    skuBean.propId4 = map['propId4'];
    skuBean.propId5 = map['propId5'];
    skuBean.propId6 = map['propId6'];
    skuBean.propName1 = map['propName1'];
    skuBean.propName2 = map['propName2'];
    skuBean.propName3 = map['propName3'];
    skuBean.propName4 = map['propName4'];
    skuBean.propName5 = map['propName5'];
    skuBean.propName6 = map['propName6'];
    skuBean.propNames = map['propNames'];
    skuBean.propvalueId1 = map['propvalueId1'];
    skuBean.propvalueId2 = map['propvalueId2'];
    skuBean.propvalueId3 = map['propvalueId3'];
    skuBean.propvalueId4 = map['propvalueId4'];
    skuBean.propvalueId5 = map['propvalueId5'];
    skuBean.propvalueId6 = map['propvalueId6'];
    skuBean.propvalueName1 = map['propvalueName1'];
    skuBean.propvalueName2 = map['propvalueName2'];
    skuBean.propvalueName3 = map['propvalueName3'];
    skuBean.propvalueName4 = map['propvalueName4'];
    skuBean.propvalueName5 = map['propvalueName5'];
    skuBean.propvalueName6 = map['propvalueName6'];
    skuBean.propvalueNames = map['propvalueNames'];
    skuBean.fullbarcode = map['fullbarcode'];
    skuBean.ptypeId = map['ptypeId'];
    skuBean.stoped = map['stoped'];
    skuBean.updateTime = map['updateTime'];
    return skuBean;
  }

  Map<String?, dynamic> toJson() => {
        "check": check,
        "costPrice": costPrice,
        "createTime": createTime,
        "deleted": deleted,
        "id": id,
        "memo": memo,
        "picUrl": picUrl,
        "profileId": profileId,
        "propId1": propId1,
        "propId2": propId2,
        "propId3": propId3,
        "propId4": propId4,
        "propId5": propId5,
        "propId6": propId6,
        "propName1": propName1,
        "propName2": propName2,
        "propName3": propName3,
        "propName4": propName4,
        "propName5": propName5,
        "propName6": propName6,
        "propNames": propNames,
        "propvalueId1": propvalueId1,
        "propvalueId2": propvalueId2,
        "propvalueId3": propvalueId3,
        "propvalueId4": propvalueId4,
        "propvalueId5": propvalueId5,
        "propvalueId6": propvalueId6,
        "propvalueName1": propvalueName1,
        "propvalueName2": propvalueName2,
        "propvalueName3": propvalueName3,
        "propvalueName4": propvalueName4,
        "propvalueName5": propvalueName5,
        "propvalueName6": propvalueName6,
        "propvalueNames": propvalueNames,
        "fullbarcode": fullbarcode,
        "ptypeId": ptypeId,
        "stoped": stoped,
        "updateTime": updateTime,
      };
}

/// createTime : "2021-06-22T02:47:27.838Z"
/// id : 0
/// picName : "string"
/// picUrl : "string"
/// profileId : 0
/// ptypeId : 0
/// rowindex : 0
/// updateTime : "2021-06-22T02:47:27.838Z"

class PicsBean {
  String? createTime;
  String? id;
  String? picName;
  String? picUrl;
  String? profileId;
  String? ptypeId;
  String? rowindex;
  String? updateTime;

  String? getHttpUrl({bool isThumb = true}) {
    if (picUrl == null) {
      return "";
    }
    return SpTool.getQiNiuThumbnail(picUrl!, isThumb);
  }

  static PicsBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return PicsBean();
    PicsBean picsBean = PicsBean();
    picsBean.createTime = map['createTime'];
    picsBean.id = map['id'];
    picsBean.picName = map['picName'];
    picsBean.picUrl = map['picUrl'];
    picsBean.profileId = map['profileId'];
    picsBean.ptypeId = map['ptypeId'];
    picsBean.rowindex = map['rowindex'].toString();
    picsBean.updateTime = map['updateTime'];
    return picsBean;
  }

  Map<String, dynamic> toJson() => {
        "createTime": createTime,
        "id": id,
        "picName": picName,
        "picUrl": picUrl,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "rowindex": rowindex,
        "updateTime": updateTime,
      };
}
