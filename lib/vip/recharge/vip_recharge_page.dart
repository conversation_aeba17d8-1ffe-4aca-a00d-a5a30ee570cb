import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:halo_pos/common/num_extension.dart';
import 'package:halo_pos/common/tool/dialog_util.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../bill/widget/mixin/select_vip_mixin.dart';
import '../../../common/standard.dart';
import '../../../common/tool/decimal_scale_input_formatter.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../settting/widget/checkbox.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../vip/entity/recharge_strategy.dart';
import '../../../vip/model/vip_model.dart';
import '../../../vip/model/vip_recharge_model.dart';
import '../../../vip/recharge/vip_recharge_settlement_page.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../common/tool/system_config_tool.dart';
import '../../enum/bill_decimal_type.dart';
import '../../plugin/secondary_screen_windows.dart';
import '../../widgets/memo_dialog.dart';
import '../utils/svip_util.dart';
import 'recharge_select_goods.dart';

const _textColor = Color(0xFF333333);

const _colorBlue = Color(0xFF4679FC);

const _colorRed = Color(0xFFE42C2D);

double get rechargeTitleWidth => 140.w;

double get inputSpacingWidth => 46.w;

double get inputTextFieldWidth => 676.w;

double get inputLineWidth => MathUtil.addDec(
      (MathUtil.addDec(rechargeTitleWidth, inputTextFieldWidth) *
              Decimal.fromInt(2))
          .toDouble(),
      inputSpacingWidth,
    ).toDouble();

final TextStyle rechargeTextStyle =
    TextStyle(color: _textColor, fontSize: 28.sp);

///构建充值策略文字信息
Widget buildStrategyGearDetail(Gear gear) {
  Map<int, List<Detail>> details = gear.details
          ?.where((element) =>
              element.rules != null &&
              element.rules!.isNotEmpty &&
              element.detailType != null &&
              (element.valueQty ?? 0) > 0)
          .groupListsBy((element) => element.detailType!) ??
      <int, List<Detail>>{};
  List<Widget> detailsWidgets = details.entries.map((entry) {
    String typeValues = "";
    switch (entry.key) {
      case 1:
        typeValues =
            "送￥${entry.value.map((e) => e.valueQty!).reduce((value, element) => MathUtil.addDec(value, element).toDouble()).getIntWhenInteger}赠金";
        break;
      case 2:
        typeValues =
            "送${entry.value.map((e) => e.valueQty!).reduce((value, element) => MathUtil.addDec(value, element).toDouble()).getIntWhenInteger}积分";
        break;
      case 4:
        typeValues = entry.value
            .map((e) => e.rules!.first.let((rule) =>
                "「${rule.cardName}」*${rule.valueQty?.getIntWhenInteger}"))
            .join("、");
        typeValues = "赠送$typeValues";
        break;
      case 5:
        typeValues = entry.value.expand<String>((detail) {
          //固定商品
          if (detail.giveMode == 0) {
            return detail.rules!
                .where((rule) =>
                    rule.skuId != null &&
                    rule.ptypeId != null &&
                    rule.unitId != null)
                .map(
                    (e) => "「${e.ptypeName}」*${e.valueQty?.getIntWhenInteger}");
          }
          //范围内指定赠品 赠送「纯棉印花四件套西派假日」*1，「纯棉印花四件套斑斓梦境」*1
          else if (detail.giveMode == 1) {
            //赠送
            if ((detail.rules!.first.giveType ?? 0) == 0) {
              return ["「指定范围内自选赠品」*${detail.valueQty?.getIntWhenInteger}"];
            }
            //换购
            else {
              return ["换购商品*${detail.valueQty?.getIntWhenInteger}"];
            }
          }
          return [];
        }).join("、");
        typeValues = "赠送$typeValues";
        break;
      default:
        //权益卡和次卡暂不做
        return Container();
    }
    return Container(
        padding: EdgeInsets.only(bottom: 20.h),
        child: HaloContainer(
          padding: EdgeInsets.only(top: 4.w),
          children: [
            Text("•",
                style: rechargeTextStyle.copyWith(
                    fontSize: 24.sp, fontWeight: FontWeight.bold)),
            SizedBox(width: 6.w),
            Expanded(
                child: Text(typeValues,
                    style: rechargeTextStyle.copyWith(fontSize: 24.sp))),
          ],
        ));
  }).toList();
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 10.w),
    child: Column(mainAxisSize: MainAxisSize.min, children: detailsWidgets),
  );
}

///给内容添加左边的标题
Widget buildContentWithTitle({
  required String title,
  Widget? content,
  int titleSize = 26,
  double? titleHeight,
  bool expandedContent = true,
  CrossAxisAlignment alignment = CrossAxisAlignment.center,
}) {
  if (expandedContent) {
    content = Expanded(child: content ?? Container());
  }
  Widget titleWidget = Text(title,
      style: TextStyle(
          fontSize: titleSize.sp,
          color: _textColor,
          fontWeight: FontWeight.w600));
  if (titleHeight != null && titleHeight > 0) {
    titleWidget = Container(
        height: titleHeight,
        alignment: Alignment.centerLeft,
        child: titleWidget);
  }
  return Row(
    mainAxisSize: MainAxisSize.min,
    crossAxisAlignment: alignment,
    children: [
      SizedBox(width: rechargeTitleWidth, child: titleWidget),
      content ?? Container(),
    ],
  );
}

///构建充值活动列表
Widget buildStrategyList<T>({
  required ItemScrollController scrollController,
  int initialScrollIndex = 0,
  required ValueGetter<List<T>> dataGetter,
  required Gear? Function(T) gearGetter,
  required bool Function(T) isSelected,
  void Function(T)? onTap,
}) {
  return buildContentWithTitle(
    title: "充值活动",
    titleSize: 28,
    alignment: CrossAxisAlignment.start,
    content: ScrollablePositionedList.builder(
        itemScrollController: scrollController,
        initialScrollIndex: initialScrollIndex,
        itemCount: dataGetter().length,
        itemBuilder: (context, index) {
          final item = dataGetter()[index];
          final gear = gearGetter(item) ?? Gear();
          Widget child = HaloContainer(
            padding: EdgeInsets.only(left: 25.w),
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  HaloPosCheckBox(
                      value: isSelected(item), width: 26.w, height: 26.w),
                  SizedBox(
                      width: 14.w,
                      child: Text(
                        "￥",
                        style: TextStyle(
                            fontSize: 28.sp,
                            color: Colors.transparent,
                            fontWeight: FontWeight.w600),
                      ))
                ],
              ),
              Container(
                width: 180.w,
                padding: EdgeInsets.only(right: 10.w),
                child: Text("充￥${gear.chargeTotal}",
                    style: TextStyle(
                        fontSize: 28.sp,
                        color: _colorBlue,
                        fontWeight: FontWeight.w600)),
              ),
              Expanded(child: buildStrategyGearDetail(gear)),
            ],
          );
          if (onTap != null) {
            child = SizedBox(
              width: double.infinity,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => onTap(item),
                child: child,
              ),
            );
          }
          return child;
        }),
  );
}

///更换赠品行
Widget buildChangeGoodsWidget({
  required String title,
  required String content,
  String? buttonText,
  VoidCallback? onTap,
}) {
  Widget result = HaloLabel(
    content,
    overflow: TextOverflow.ellipsis,
    maxLines: 1,
    textStyle: rechargeTextStyle.copyWith(fontSize: 26.sp),
  );
  if (onTap != null) {
    result = Row(
      children: [
        if (content.isNotEmpty) ...[
          Flexible(
            child: result,
          ),
          SizedBox(width: 22.w),
        ],
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: onTap,
          child: Text(buttonText ?? "",
              style: TextStyle(
                  color: _colorBlue,
                  fontWeight: FontWeight.w600,
                  fontSize: 26.sp)),
        )
      ],
    );
  }
  return SizedBox(
    width: inputLineWidth,
    child: buildContentWithTitle(title: title, content: result),
  );
}

///选择赠品行
Widget buildSelectGoodsWidget({
  required String title,
  required String goodsNames,
  bool isExchange = false,
  VoidCallback? onTap,
}) {
  TextStyle textStyle;
  if (goodsNames.isEmpty) {
    goodsNames = isExchange ? "在指定范围内自选换购商品" : "在指定范围内自选赠品";
    textStyle = rechargeTextStyle.copyWith(
        fontSize: 26.sp, color: const Color(0xFF999999));
  } else {
    textStyle = rechargeTextStyle.copyWith(fontSize: 26.sp);
  }
  Widget result = HaloLabel(
    goodsNames,
    overflow: TextOverflow.ellipsis,
    maxLines: 1,
    textStyle: textStyle,
  );
  if (onTap != null) {
    result = Row(
      children: [
        Expanded(child: result),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: onTap,
          child: Container(
            height: 70.h,
            width: 70.h,
            alignment: Alignment.center,
            // child: IconFont(IconNames.guolvxiala, size: 15.w),
            child: Icon(
              Icons.more_horiz_sharp,
              size: 50.h,
              color: const Color(0xFFC5C5C5),
            ),
          ),
        ),
      ],
    );
  }
  result = Container(
    height: 70.h,
    alignment: Alignment.centerLeft,
    padding: EdgeInsets.symmetric(horizontal: 22.w),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8.w),
      border: Border.all(color: const Color(0xFFC5C5C5), width: 1.w),
    ),
    child: result,
  );
  return SizedBox(
    width: inputLineWidth,
    child: buildContentWithTitle(title: title, content: result),
  );
}

///拼接赠品商品名称
String formatGoodsName(List<Detail>? presentationGoods) {
  return presentationGoods?.expand<String>((detail) {
        //固定商品
        if (detail.giveMode == 0) {
          return detail.rules!
              .where((rule) =>
                  rule.skuId != null &&
                  rule.ptypeId != null &&
                  rule.unitId != null)
              .map((e) =>
                  "「${e.ptypeName ?? ""}」*${(e.valueQty ?? 0).getIntWhenInteger}");
        }
        //范围内指定赠品
        else if (detail.giveMode == 1) {
          num qty = (detail.valueQty ?? 0).getIntWhenInteger;
          if ((detail.rules!.first.giveType ?? 0) == 0) {
            return ["「指定范围内自选赠品」*$qty"];
          } else {
            return ["换购商品*$qty"];
          }
        }
        return [];
      }).join("、") ??
      "";
}

///判断赠品是否是商品
bool _checkDetailIsGoods(Detail detail) {
  return detail.detailType == 5 &&
      detail.rules != null &&
      detail.rules!.isNotEmpty &&
      (detail.valueQty ?? 0) > 0;
}

///从充值策略中获取商品赠品
List<Detail>? _getGoodsDetailsFromGear(Gear? gear) {
  return gear?.details
      ?.where((detail) => _checkDetailIsGoods(detail))
      .toList(growable: false);
}

///会员充值界面
class VipRechargePage extends BaseStatefulPage {
  final VipWithLevelAssertsRightsCardDTO vipInfo;

  const VipRechargePage({Key? key, required this.vipInfo}) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _VipRechargePageState();
}

class _VipRechargePageState extends BaseStatefulPageState<VipRechargePage> {
  ///会员信息
  VipWithLevelAssertsRightsCardDTO? vipInfo;

  @override
  String getActionBarTitle() => "会员充值";

  EdgeInsets get _padding => EdgeInsets.symmetric(horizontal: 20.w);

  ///充值金额输入框controller
  final TextEditingController _rechargeController = TextEditingController();

  ///赠送金额输入controller
  final TextEditingController _presentationController = TextEditingController();

  ///列表滚动控制
  final ItemScrollController _scrollController = ItemScrollController();

  ///备注controller
  final TextEditingController _memoController = TextEditingController();

  ///列表头部的index
  int _listViewHeaderIndex = 0;

  ///策略列表
  List<RechargeStrategy> _strategyList = [];

  ///当前展示的策略列表
  List<Gear> _visibleStrategyList = [];

  ///当前充值策略对应的活动
  RechargeStrategy? _currentStrategy;

  ///当前选中的充值策略
  final ValueNotifier<Gear?> _currentGearController = ValueNotifier(null);

  Gear? get _currentGear => _currentGearController.value;

  set _currentGear(Gear? gear) => _currentGearController.value = gear;

  ///赠品商品(包含换购和免费赠品)
  List<Detail>? _goodsDetailList;

  ///范围内选择商品的detail
  List<Detail>? _rangeChooseGoodsDetail;

  ///已选商品名称
  String selectGoodsNames = "";

  ///当前商品赠品名称
  String _currentGoodsGiftName = "";

  ///当前赠品商品对应的充值档位id
  String? _gearIdOfPresentationGoods;

  ///充值金额
  int get _rechargeMoney => int.tryParse(_rechargeController.text) ?? 0;

  ///应付金额
  num _shouldPayMoney = 0;

  ///赠送金额
  int _presentationMoney = 0;

  ///是否展示更换赠品
  bool get _showChangeGift => _currentGear != null && _currentStrategy != null;

  ///是否展示选择范围赠品
  bool get _showSelectGoods {
    return _currentGear != null && _rangeChooseGoodsDetail?.isNotEmpty == true;
  }

  ///是否是换购商品
  bool get isExchange => _currentStrategy?.rechargeType == 1;

  ///选择范围赠品的标题
  String get _selectGoodsTitle => isExchange ? "选择换购" : "选择赠品";

  ///更换赠品的标题
  String get _changeGiftTitle => isExchange ? "换购列表" : "赠品列表";

  ///是否可以编辑赠金
  final bool _presentationMoneyPermission =
      SpTool.getPermission().memberrechargeStrategyeditGiveMoney ?? false;

  @override
  Future<void> onInitState() async {
    vipInfo = widget.vipInfo;
    showRechargeForWin();
    _rechargeController.addListener(() {
      setState(() {
        int rechargeMoney = _rechargeMoney;
        changeRechargeInfoForWin(
          rechargeAmount: rechargeMoney.toString(),
          showSelectedGoods: _showSelectGoods,
          selectedGoodsTitle: _selectGoodsTitle,
          selectedGoods: selectGoodsNames,
          showGift: _showChangeGift,
          giftTitle: _changeGiftTitle,
          gift: _currentGoodsGiftName,
          isExchange: isExchange,
        );
        //如果修改了金额，需要和现在选中的充值策略进行比较，判断是否还满足条件
        if ((_currentGear?.chargeTotal ?? 0) > rechargeMoney) {
          _currentGear = null;
        } else {
          //计算金额
          _getShouldPay(rechargeMoney);
        }
        //变更展示的策略列表
        _refreshVisibleStrategyList();
        changeRechargeStrategyListForWin(
            list: _visibleStrategyList
                .map((e) => RechargeStrategyForWindows(
                    selected: _currentGear == e, gear: e))
                .toList(),
            jumpTo: _listViewHeaderIndex);
      });
    });
    _memoController.addListener(() {
      setState(() {});
    });
    _presentationController.addListener(() {
      _presentationMoney = int.tryParse(_presentationController.text) ?? 0;
      changeRechargeInfoForWin(
        giftAmount: _presentationMoney.toString(),
        showSelectedGoods: _showSelectGoods,
        selectedGoodsTitle: _selectGoodsTitle,
        selectedGoods: selectGoodsNames,
        showGift: _showChangeGift,
        giftTitle: _changeGiftTitle,
        gift: _currentGoodsGiftName,
        isExchange: isExchange,
      );
    });
    _currentGearController.addListener(() {
      setState(() {
        //更换了充值活动(档位),重置赠送商品
        _setPresentationGoods(
            _getGoodsDetailsFromGear(_currentGear), _currentGear?.id);
        int? jumpTo;
        if (_currentGear != null) {
          //根据当前档位的充值活动id拿到充值活动
          _currentStrategy = _strategyList.firstWhereOrNull(
              (element) => element.id == _currentGear!.rechargeId);
          _presentationController.text = (_currentGear?.details
                      ?.firstWhereOrNull((element) => element.detailType == 1)
                      ?.valueQty ??
                  0)
              .toInt()
              .toString();
          //选中了策略，若充值金额不足则更改充值金额，并且修改锁死赠送金额
          if (_rechargeMoney < (_currentGear?.chargeTotal ?? 0)) {
            _rechargeController.text =
                (_currentGear?.chargeTotal ?? 0).toString();
            return;
          }
          jumpTo = _visibleStrategyList.indexOf(_currentGear!);
        } else {
          _currentStrategy = null;
        }
        //刷新价格
        _getShouldPay();
        changeRechargeStrategyListForWin(
          list: _visibleStrategyList
              .map((e) => RechargeStrategyForWindows(
                  selected: _currentGear == e, gear: e))
              .toList(),
          jumpTo: jumpTo,
        );
        changeRechargeInfoForWin(
          showSelectedGoods: _showSelectGoods,
          selectedGoodsTitle: _selectGoodsTitle,
          selectedGoods: selectGoodsNames,
          showGift: _showChangeGift,
          giftTitle: _changeGiftTitle,
          gift: _currentGoodsGiftName,
          isExchange: isExchange,
        );
      });
    });
    //拉取充值活动
    getRechargeListByVip();
  }

  @override
  void dispose() {
    _currentGearController.dispose();
    _rechargeController.dispose();
    _presentationController.dispose();
    _memoController.dispose();
    hideRechargeForWin();
    super.dispose();
  }

  ///根据会员信息获取充值活动
  void getRechargeListByVip() {
    //刷新充值活动时，需要清除已经选中的充值活动，避免该活动无法使用
    _currentGear = null;
    _rechargeController.text = "0";
    _presentationController.text = "0";
    VipRechargeModel.getRechargeListByVip(context,
            otypeId: SpTool.getStoreInfo()!.otypeId ?? "",
            vipId: vipInfo?.vip?.id ?? "",
            vipLevelId: vipInfo?.level?.levelId ?? "")
        .then((value) {
      setState(() {
        _strategyList = value ?? [];
        _refreshVisibleStrategyList();
        changeRechargeStrategyListForWin(
            list: _visibleStrategyList
                .map((e) => RechargeStrategyForWindows(
                    selected: _currentGear == e, gear: e))
                .toList(),
            jumpTo: _listViewHeaderIndex);
      });
    });
  }

  ///根据当前输入的充值金额，刷新展示的策略列表
  ///这里前提是所有的策略列表已经被设置为按照充值金额升序排列
  void _refreshVisibleStrategyList() {
    List<Gear> sortList = _strategyList
        .expand<Gear>((element) => element.gears ?? [])
        .toList()
      ..sort((a, b) => (a.chargeTotal ?? 0).compareTo(b.chargeTotal ?? 0));
    _visibleStrategyList.clear();
    _listViewHeaderIndex = 0;
    if (sortList.isEmpty) {
      return;
    }
    if (_rechargeMoney == 0) {
      _visibleStrategyList.addAll(sortList);
      _visibleStrategyList = _visibleStrategyList.reversed.toList();
      return;
    }
    //这里会展示符合条件的上一级不符合条件的策略的值
    int? headValue;
    for (var strategy in sortList) {
      int money = strategy.chargeTotal ?? 0;
      if (money <= _rechargeMoney) {
        _visibleStrategyList.insert(0, strategy);
      } else {
        headValue ??= money;
        if (money > headValue) {
          break;
        }
        _visibleStrategyList.insert(0, strategy);
        _listViewHeaderIndex++;
      }
    }
    //若是当前选中了某个策略，则需要跳转到了该策略位置，将其展示出来
    if (_currentGear != null) {
      int index = _visibleStrategyList.indexOf(_currentGear!);
      if (index != -1) {
        _listViewHeaderIndex = index;
      }
    }
    _scrollController.jumpTo(index: _listViewHeaderIndex);
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
      margin: EdgeInsets.symmetric(horizontal: _padding.left, vertical: 20.h),
      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 30.h),
      color: Colors.white,
      borderRadius: BorderRadius.circular(8.w),
      mainAxisSize: MainAxisSize.max,
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(child: _buildStrategyList()),
        SizedBox(height: 20.h),
        Row(
          children: [
            _buildInput(
              title: "充值金额",
              numberList: const [100, 200, 500, 1000],
              controller: _rechargeController,
              onNumberClick: (int number) =>
                  _rechargeController.text = number.toString(),
            ),
            SizedBox(width: inputSpacingWidth),
            _buildInput(
              title: "赠送金额",
              numberList: const [5, 10, 50, 100],
              controller: _presentationController,
              enable: _currentGear == null && _presentationMoneyPermission,
              onNumberClick: (int number) {
                //如果勾选了充值策略，那么赠送金额将会被锁死,此时点击下面的默认赠送金额将无效
                if (_currentGear == null) {
                  _presentationController.text = number.toString();
                }
              },
            ),
          ],
        ),
        if (_showSelectGoods) ...[
          SizedBox(height: 20.h),
          _buildSelectGoodsWidget(),
        ],
        if (_showChangeGift) ...[
          SizedBox(height: 20.h),
          _buildChangeGoodsWidget(),
        ]
      ],
    );
  }

  @override
  Widget buildTopBody(BuildContext context) {
    return HaloContainer(
      height: 84.h,
      padding: _padding,
      color: Colors.white,
      children: [
        IconFont(IconNames.morentouxiang, size: 50.w),
        Padding(
          padding: EdgeInsets.only(left: 10.w),
          child: Text(
            vipInfo?.vip?.name ?? "",
            style: rechargeTextStyle.copyWith(
                fontSize: 26.sp, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(width: 55.w),
        RichText(
          text: TextSpan(children: [
            TextSpan(
                text: "手机号:",
                style: rechargeTextStyle.copyWith(
                    color: const Color(0xFF666666), fontSize: 24.sp)),
            TextSpan(
                text: vipInfo?.vip?.phone ?? "",
                style: rechargeTextStyle.copyWith(fontSize: 24.sp))
          ]),
        ),
        SizedBox(width: 45.w),
        RichText(
          text: TextSpan(children: [
            TextSpan(
                text: "当前储值余额:",
                style: rechargeTextStyle.copyWith(
                    color: const Color(0xFF666666), fontSize: 24.sp)),
            TextSpan(
                text: "￥${vipInfo?.asserts?.totalMoney}",
                style: rechargeTextStyle.copyWith(
                    color: _colorRed, fontSize: 24.sp))
          ]),
        ),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            //选择会员
            SelectVipMixin.searchVip(context, jumpToAdd: false).then((vipInfo) {
              if (vipInfo != null && vipInfo.vip?.expired != true) {
                setState(() {
                  this.vipInfo = vipInfo;
                  _memoController.text = "";
                  //刷新策略列表
                  getRechargeListByVip();
                });
              }
            });
          },
          child: Container(
            width: 138.w,
            height: 52.h,
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFC5C5C5), width: 1.w),
                color: Colors.white),
            alignment: Alignment.center,
            child: Text("更换会员",
                style: rechargeTextStyle.copyWith(fontSize: 22.sp)),
          ),
        ),
        Expanded(child: Container()),
      ],
    );
  }

  @override
  Widget buildBottomBody(BuildContext context) {
    return Column(
      children: [
        HaloContainer(
          height: 130.h,
          mainAxisSize: MainAxisSize.max,
          color: Colors.white,
          padding: _padding,
          children: [
            Expanded(
              child: Row(
                children: [
                  if (_memoController.text.isNotEmpty)
                    Flexible(
                      child: Padding(
                        padding: EdgeInsets.only(right: 10.w),
                        child: Text(
                          _memoController.text,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: rechargeTextStyle,
                        ),
                      ),
                    ),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      //跳转到编辑备注页面
                      showDialog(
                          context: context,
                          builder: (context) =>
                              MemoInputDialog(controller: _memoController));
                    },
                    child: Padding(
                      padding: EdgeInsets.only(right: 15.w),
                      child: Text(
                        TextUtil.isEmpty(_memoController.text)
                            ? "点击填写备注"
                            : "查看",
                        style: rechargeTextStyle.copyWith(color: _colorBlue),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (_shouldPayMoney != _rechargeMoney) ...[
              SizedBox(width: 5.w),
              Text(
                "明细",
                style: TextStyle(color: _colorBlue, fontSize: 26.sp),
              ),
              SizedBox(width: 10.w),
              HaloContainer(
                crossAxisAlignment: CrossAxisAlignment.start,
                constraints: BoxConstraints(maxWidth: 200.w),
                direction: Axis.vertical,
                children: [
                  buildPayDetailWidget(
                      title: "充值金额:", content: "￥$_rechargeMoney"),
                  buildPayDetailWidget(
                      title: "换购商品:",
                      content:
                          "￥${MathUtil.subtractDec(_shouldPayMoney, _rechargeMoney).toString()}")
                ],
              )
            ],
            //应付金额
            HaloContainer(
                constraints: BoxConstraints(maxWidth: 400.w),
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                children: [
                  Text("应付:",
                      style: rechargeTextStyle.copyWith(fontSize: 32.sp)),
                  Flexible(
                    child: Text("￥$_shouldPayMoney",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                          color: _colorRed,
                          fontSize: 40.sp,
                        )),
                  )
                ]),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: _showSettlementDialog,
              child: Container(
                width: 230.w,
                height: 80.h,
                alignment: AlignmentDirectional.center,
                decoration: BoxDecoration(
                    color: _colorBlue,
                    borderRadius: BorderRadius.circular(8.w)),
                child: Text("确认充值",
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: Colors.white,
                    )),
              ),
            )
          ],
        ),
      ],
    );
  }

  ///构建充值活动列表
  Widget _buildStrategyList() {
    return buildStrategyList<Gear>(
      scrollController: _scrollController,
      initialScrollIndex: _listViewHeaderIndex,
      dataGetter: () => _visibleStrategyList,
      gearGetter: (item) => item,
      isSelected: (item) => _currentGear == item,
      onTap: (item) => setState(() {
        //已经勾选的取消勾选
        if (_currentGear == item) {
          _currentGear = null;
          _presentationController.text = "0";
        }
        //勾选
        else {
          _currentGear = item;
        }
      }),
    );
  }

  ///输入框和底下四个默认值的按钮
  Widget _buildInput({
    required String title,
    required List<int> numberList,
    required TextEditingController controller,
    required ValueChanged<int> onNumberClick,
    bool enable = true,
  }) {
    final TextStyle style = rechargeTextStyle.copyWith(fontSize: 28.sp);
    return buildContentWithTitle(
        title: title,
        titleHeight: 70.h,
        alignment: CrossAxisAlignment.start,
        expandedContent: false,
        content: HaloContainer(
          width: inputTextFieldWidth,
          direction: Axis.vertical,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 70.h,
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 22.w),
              decoration: BoxDecoration(
                color: enable ? Colors.white : const Color(0xFFF7F7F7),
                borderRadius: BorderRadius.circular(8.w),
                border: Border.all(color: const Color(0xFFC5C5C5), width: 1.w),
              ),
              child: Row(
                children: [
                  Text("￥", style: style.copyWith(fontSize: 26.sp)),
                  Expanded(
                    child: TextField(
                      enabled: enable,
                      maxLines: 1,
                      controller: controller,
                      style: style,
                      keyboardType: const TextInputType.numberWithOptions(),
                      decoration: const InputDecoration(
                        isCollapsed: true,
                        border: OutlineInputBorder(borderSide: BorderSide.none),
                      ),
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(7), //限制最大长度7(百万级别)
                        DecimalScaleInputFormatter() //限制只能输入整数数字
                      ],
                    ),
                  ),
                ],
              ),
            ),
            HaloContainer(
              padding: EdgeInsets.only(top: 20.h),
              children: numberList.let((list) {
                var result = <Widget>[];
                for (int i = 0; i < list.length; i++) {
                  //添加分割间距
                  if (i != 0) {
                    result.add(SizedBox(width: 16.w));
                  }
                  var item = list[i];
                  result.add(Expanded(
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: enable ? () => onNumberClick(item) : null,
                      child: Container(
                        height: 58.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(4.w),
                        ),
                        child: Text("￥$item",
                            style: rechargeTextStyle.copyWith(fontSize: 26.sp),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis),
                      ),
                    ),
                  ));
                }
                return result;
              }),
            ),
          ],
        ));
  }

  ///构建支付明细
  Widget buildPayDetailWidget(
      {required String title, required String content}) {
    final fontSize = 20.sp;
    return RichText(
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        children: [
          TextSpan(
              text: title,
              style: TextStyle(
                  fontSize: fontSize, color: const Color(0xFF333333))),
          TextSpan(
            text: content,
            style: TextStyle(color: Colors.red, fontSize: fontSize),
          )
        ],
      ),
    );
  }

  ///更换赠品行
  Widget _buildChangeGoodsWidget() {
    return buildChangeGoodsWidget(
        title: _changeGiftTitle,
        content: _currentGoodsGiftName,
        buttonText: _currentStrategy?.rechargeType == 1 ? "更换换购商品" : "更换赠品",
        onTap: () {
          if (_showChangeGift) {
            List<Map<String, dynamic>> list = [];
            _currentStrategy!.gears?.forEach((element) {
              if (element.chargeTotal != null &&
                  element.chargeTotal! > 0 &&
                  element.chargeTotal! <= (_currentGear!.chargeTotal ?? 0) &&
                  element.details?.isNotEmpty == true) {
                var details = _getGoodsDetailsFromGear(element);
                if (details?.isNotEmpty == true) {
                  list.add({
                    "gearId": element.id,
                    "isCurrent": element.id == _gearIdOfPresentationGoods,
                    "details": details
                  });
                }
              }
            });
            showDialog<Map<String, dynamic>>(
                    context: context,
                    builder: (context) => RechargeGoodsChangeDialog(list))
                .then((value) {
              if (value != null) {
                setState(() {
                  _setPresentationGoods(value["details"], value["gearId"]);
                  //刷新价格
                  _getShouldPay();
                });
              }
            });
          }
        });
  }

  ///选择范围赠品
  Widget _buildSelectGoodsWidget() {
    String title = _selectGoodsTitle;
    return buildSelectGoodsWidget(
        title: title,
        goodsNames: selectGoodsNames,
        isExchange: isExchange,
        onTap: () {
          if (_rangeChooseGoodsDetail?.isEmpty != false) return;
          //弹窗选择赠品
          showDialog(
              context: context,
              builder: (context) => RechargeGoodsSelectDialog(
                  list: _rangeChooseGoodsDetail!,
                  isBuy: _currentStrategy?.rechargeType == 1)).then((value) {
            //选择了商品，刷新界面
            selectGoodsNames = getSelectedGoodsList()
                .map((e) => "${e.ptypeName ?? ""}x${e.localSelectQty}")
                .join(';');
            changeRechargeInfoForWin(
              rechargeAmount: _rechargeMoney.toString(),
              showSelectedGoods: _showSelectGoods,
              selectedGoodsTitle: _selectGoodsTitle,
              selectedGoods: selectGoodsNames,
              showGift: _showChangeGift,
              giftTitle: _changeGiftTitle,
              gift: _currentGoodsGiftName,
              isExchange: isExchange,
            );
            //刷新价格
            _getShouldPay();
            setState(() {});
          });
        });
  }

  void _showSettlementDialog({bool checkRangeGoods = true}) {
    if (vipInfo == null) {
      HaloToast.show(context, msg: "未选择会员");
      return;
    }
    if (_rechargeMoney <= 0) {
      HaloToast.show(context, msg: "充值金额必须大于0");
      return;
    }
    if (isVipExpired(vipInfo!.vip!.validDate,
        vipType: vipInfo!.level!.vipType == true ? 1 : 0)) {
      HaloToast.show(context, msg: "该会员已经过期，无法充值");
      return;
    }
    if (checkRangeGoods) {
      final selectGoodsList = getSelectedGoodsList();
      if (_rangeChooseGoodsDetail?.isNotEmpty == true &&
          selectGoodsList.isEmpty) {
        String title;
        if (_currentStrategy?.rechargeType == 1) {
          title = "还未选择换购商品，确定要继续吗";
        } else {
          title = "还未选择自选赠品，确定要继续吗";
        }
        DialogUtil.showConfirmDialog(context,
            content: title,
            actionLabels: ["取消", "确定"],
            confirmCallback: () =>
                _showSettlementDialog(checkRangeGoods: false));
        return;
      }
      if (_rangeChooseGoodsDetail?.isNotEmpty == true &&
          _rangeChooseGoodsDetail!.fold<Decimal>(
                  Decimal.zero,
                  (previousValue, element) =>
                      previousValue +
                      Decimal.parse((element.valueQty ?? 0).toString())) >
              selectGoodsList.fold<Decimal>(
                  Decimal.zero,
                  (previousValue, element) =>
                      previousValue +
                      Decimal.parse((element.localSelectQty).toString()))) {
        String title;
        if (_currentStrategy?.rechargeType == 1) {
          title = "当前所选商品数量小于可选换购商品数量，是否继续保存";
        } else {
          title = "当前所选商品数量小于可选赠品数量，是否继续保存";
        }
        DialogUtil.showConfirmDialog(context,
            content: title,
            actionLabels: ["取消", "确定"],
            confirmCallback: () =>
                _showSettlementDialog(checkRangeGoods: false));
        return;
      }
    }
    //拿到赠送的积分和优惠券
    Detail? score, coupon;
    if (_currentGear?.details?.isNotEmpty == true) {
      for (var detail in _currentGear!.details!) {
        if (detail.detailType == 2) {
          score = detail;
        } else if (detail.detailType == 4) {
          coupon = detail;
        }
        if (score != null && coupon != null) {
          break;
        }
      }
    }
    // 跳转到结算页面
    Navigator.push(
      context,
      MaterialPageRoute(
          builder: (context) => VipRechargeSettlementPage(
              vipInfo: vipInfo!,
              isExchange: _currentStrategy?.rechargeType == 1,
              rechargeMoney: _rechargeMoney,
              shouldPayMoney: _shouldPayMoney,
              presentationMoney: _presentationMoney,
              goods: _goodsDetailList,
              score: score,
              coupon: coupon,
              strategy: _currentGear,
              memoController: _memoController)),
    ).then(
      (value) {
        refreshVipInfo(context);
        if (value == true) {
          _memoController.text = "";
        }
      },
    ); //刷新会员信息
  }

  ///获取已选商品列表
  List<Rule> getSelectedGoodsList() {
    return _rangeChooseGoodsDetail
            ?.expand<Rule>((rule) => rule.rules ?? [])
            .where((e) => e.localSelectQty > 0)
            .toList() ??
        [];
  }

  ///计算应收金额
  void _getShouldPay([int? rechargeMoney]) {
    Decimal shouldPay = Decimal.fromInt(rechargeMoney ?? _rechargeMoney);
    //换购促销，且选了商品
    if (_currentStrategy?.rechargeType == 1 &&
        _rangeChooseGoodsDetail?.isNotEmpty == true) {
      shouldPay = _rangeChooseGoodsDetail!
          .expand<Rule>((element) => element.rules ?? [])
          .where((element) =>
              element.localSelectQty > 0 &&
              (element.giveType == 1 || element.giveType == 2))
          .fold<Decimal>(shouldPay, (previousValue, element) {
        //换购价
        if (element.giveType == 1) {
          num total = SystemConfigTool.doubleMultipleToDecimal(
              element.exchangePrice ?? 0,
              element.localSelectQty,
              BillDecimalType.TOTAL);
          previousValue += Decimal.parse(total.toString());
        }
        //换购折扣
        else if (element.giveType == 2) {
          num total = SystemConfigTool.doubleMultipleToDecimal(
              element.retailPrice ?? 0,
              element.localSelectQty,
              BillDecimalType.TOTAL);
          total = SystemConfigTool.doubleMultipleToDecimal(
              total, element.exchangeDiscount ?? 0, BillDecimalType.TOTAL);
          previousValue += Decimal.parse(total.toString());
        }
        return previousValue;
      });
    }
    _shouldPayMoney = shouldPay.toDouble().getIntWhenInteger;
  }

  ///记录当前的储值商品赠品
  ///1.切换充值档位
  ///2.更换充值赠品
  void _setPresentationGoods(
      List<Detail>? goodsDetails, String? goodsDetailGearId) {
    _goodsDetailList = null;
    _rangeChooseGoodsDetail = null;
    if (goodsDetails?.isNotEmpty == true) {
      _goodsDetailList = [];
      for (var e in goodsDetails!) {
        Detail detail = Detail.fromJson(e.toJson());
        _goodsDetailList!.add(detail);
        //范围自选
        if (detail.giveMode == 1 &&
            (detail.valueQty ?? 0) > 0 &&
            detail.rules!.isNotEmpty == true) {
          _rangeChooseGoodsDetail ??= [];
          _rangeChooseGoodsDetail!.add(detail);
        }
      }
    }
    selectGoodsNames = "";
    _currentGoodsGiftName = formatGoodsName(_goodsDetailList);
    changeRechargeInfoForWin(
      showSelectedGoods: _showSelectGoods,
      selectedGoodsTitle: _selectGoodsTitle,
      selectedGoods: selectGoodsNames,
      showGift: _showChangeGift,
      giftTitle: _changeGiftTitle,
      gift: _currentGoodsGiftName,
      isExchange: isExchange,
    );
    //当前赠品商品对应的充值档位id
    _gearIdOfPresentationGoods = goodsDetailGearId;
  }

  ///刷新当前会员信息
  Future<void> refreshVipInfo(BuildContext context) async {
    if (vipInfo?.vip?.id?.isNotEmpty == true) {
      VipWithLevelAssertsRightsCardDTO? vip =
          await VipModel.getVipWithLevelScoreRightsCardById(
              context, vipInfo!.vip!.id!, SpTool.getStoreInfo()!.otypeId ?? "");
      if (vip != null) {
        setState(() => vipInfo = vip);
      }
      getRechargeListByVip();
    }
  }
}
