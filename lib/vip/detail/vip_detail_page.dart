import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';

import '../../../vip/add/vip_add_page.dart';
import '../../../vip/entity/card_dto.dart';
import '../../../vip/model/vip_model.dart';
import '../../../vip/recharge/vip_recharge_page.dart';
import '../../../vip/widget/coupons_list.dart';
import '../../../vip/widget/mixin/vip_info_row_mixin.dart';
import '../../../vip/widget/right_card_list_widget.dart';
import '../../../vip/widget/vip_score_dialog.dart';
import '../../common/standard.dart';
import '../../common/tool/sp_tool.dart';
import '../../credits/credits_exchange.dart';
import '../../entity/system/permission_dto.dart';
import '../../iconfont/icon_font.dart';
import '../../plugin/secondary_screen_plugin.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../entity/get_vip_level_score_rights_card_response.dart';
import '../entity/vip_simple.dart';
import '../level/svip_select_page.dart';
import '../utils/svip_util.dart';

///会员详情页
class VipDetailPage extends BaseStatefulPage {
  ///会员id
  final String vipId;

  const VipDetailPage({Key? key, required this.vipId}) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _VipDetailPageState();
}

class _VipDetailPageState extends BaseStatefulPageState<VipDetailPage>
    with VipInfoRowMixin {
  ///权限
  final PermissionDto _permissionDto = SpTool.getPermission();

  final _paddingLeft = 300.w;

  final _paddingRight = 218.w;

  VipWithLevelAssertsRightsCardDTO? vipInfo;

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container(
      color: Colors.white,
      height: double.infinity,
      child: SingleChildScrollView(
        padding: EdgeInsets.only(left: _paddingLeft, right: _paddingRight),
        child: Column(
          children: [_buildTop(context), _buildContent(context)],
        ),
      ),
    );
  }

  ///将门店和权益卡名称字符串拼接
  String _append<T>(List<T> list, ResultFunction<T, String> getName) {
    StringBuffer buffer = StringBuffer();
    for (int i = 0; i < list.length; i++) {
      if (i != 0) {
        buffer.write("、");
      }
      buffer.write(getName(list[i]));
    }
    return buffer.toString();
  }

  ///中间内容
  Widget _buildContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        buildDivider(),
        Row(children: [
          Expanded(
              child: buildStringRow("性别", vipInfo?.vip?.sex == 1 ? "男" : "女")),
          Expanded(child: buildStringRow("生日", formatDateStringToLocal(vipInfo?.vip?.birthday,format: DateFormats.y_mo_d))),
        ]),
        buildDivider(),
        ...buildVipLevelWidgets(),
        Row(children: [
          //todo 这里先把身份证停了
          Visibility(
              visible: false,
              child: Expanded(
                  child: buildStringRow("身份证号", vipInfo?.vip?.idCode ?? ""))),

          Expanded(child: buildStringRow("邮箱", vipInfo?.vip?.email ?? "")),
        ]),
        buildDivider(),
        buildRow(
          title: "标签",
          content: SizedBox(
            height: 56.h,
            child: ListView.separated(
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) =>
                    buildTagItem(context, vipInfo!.tagList![index]),
                separatorBuilder: (context, index) => SizedBox(width: 12.w),
                itemCount: vipInfo?.tagList?.length ?? 0),
          ),
        ),
        buildDivider(),
        buildStringRow(
            "权益卡",
            vipInfo?.rightCardList?.let<String>((rightsCards) =>
                    _append<CardDto>(rightsCards,
                        (rightsCard) => rightsCard.cardName ?? "")) ??
                ""),
        buildDivider(),
        buildRow(
          title: "优惠券",
          content: ListView.separated(
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            itemCount: vipInfo?.cardList?.length ?? 0,
            separatorBuilder: (context, index) => SizedBox(width: 12.w),
            itemBuilder: (context, index) => Align(
              alignment: Alignment.center,
              child: Container(
                height: 56.h,
                padding: EdgeInsets.symmetric(horizontal: 18.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.w),
                  border:
                      Border.all(color: const Color(0xFFFF4141), width: 2.w),
                ),
                child: Text(vipInfo!.cardList![index].cardName ?? ""),
              ),
            ),
          ),
        ),
        buildDivider(),
        buildOtypeRow(),
        buildDivider(),
        ...buildVipConsumeWidgets(),
      ],
    );
  }

  Widget buildOtypeRow() {
    String otype = "全部门店";
    if (vipInfo?.vip?.applyStoreType == 1) {
      otype = vipInfo?.otypeList?.let<String>((otypeList) =>
              _append(otypeList, (Otype otype) => otype.otypeName ?? "")) ??
          "";
    }
    return buildStringRow("可用门店", otype);
  }

  ///会员消费信息
  List<Widget> buildVipConsumeWidgets() {
    var consume = vipInfo?.consume;
    if (consume == null) {
      return [];
    }
    String payMoneyCount = (consume.payMoneyCount ?? 0).toStringAsFixed(2);
    int payTimeCount = consume.payTimeCount ?? 0;
    Decimal average = (payTimeCount == 0)
        ? Decimal.zero
        : (Decimal.parse(payMoneyCount) / Decimal.fromInt(payTimeCount))
            .toDecimal(scaleOnInfinitePrecision: 8)
            .round(scale: 2);
    return [
      buildStringRow("平均消费", "￥${average.toStringAsFixed(2)}",
          textAlign: TextAlign.right),
      buildDivider(),
      buildStringRow("累计消费", "￥$payMoneyCount", textAlign: TextAlign.right),
      buildDivider(),
      buildStringRow("消费次数", payTimeCount.toString(),
          textAlign: TextAlign.right),
      buildDivider(),
      buildStringRow("最后消费", formatDateStringToLocal(consume.lastPayTime),
          textAlign: TextAlign.right),
      buildDivider(),
    ];
  }

  ///会员类型，如果是付费会员还要展示过期时间
  List<Widget> buildVipLevelWidgets() {
    if (vipInfo?.level?.vipType == true) {
      return [
        buildStringRow("会员类型", "付费会员"),
        buildDivider(),
        buildStringRow(
            "会员到期时间",
            isForever(vipInfo!.vip?.validDate)
                ? "一直有效"
                : (DateUtil.formatDateStr(vipInfo!.vip?.validDate ?? "",
                    isUtc: false))),
        buildDivider(),
      ];
    } else {
      return [buildStringRow("会员类型", "免费会员"), buildDivider()];
    }
  }

  ///顶部的头像，姓名，电话，余额，积分，成长值
  Widget _buildTop(BuildContext context) {
    TextStyle nameTitleStyle =
        TextStyle(color: const Color(0xFF666666), fontSize: 32.sp);
    TextStyle nameStyle =
        TextStyle(color: const Color(0xFF333333), fontSize: 32.sp);
    return Padding(
      padding: EdgeInsets.only(top: 60.h, bottom: 56.h),
      child: Row(children: [
        Expanded(
            flex: 3,
            child: Row(
              children: [
                Container(
                  alignment: Alignment.center,
                  width: 120.w,
                  height: 120.w,
                  decoration: BoxDecoration(
                    color: const Color(0xFFB3CFFC),
                    borderRadius: BorderRadius.circular(16.w),
                  ),
                  child:
                      IconFont(IconNames.huiyuan, size: 58.w, color: "#ffffff"),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 44.w),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Text("姓名：", style: nameTitleStyle, maxLines: 1),
                            Flexible(
                                child: Text(vipInfo?.vip?.name ?? "",
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: nameStyle)),
                            Container(
                              constraints: BoxConstraints(maxWidth: 150.w),
                              margin: EdgeInsets.only(left: 20.w),
                              padding: EdgeInsets.only(
                                  top: 4.h, bottom: 2.h, left: 8.w, right: 8.w),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFFF2DF),
                                borderRadius: BorderRadius.circular(4.w),
                              ),
                              child: Text(
                                vipInfo?.level?.levelName ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: const Color(0xFFEE6700),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 28.sp),
                              ),
                            )
                          ],
                        ),
                        Padding(
                            padding: EdgeInsets.only(top: 20.h),
                            child: Row(
                              children: [
                                Text("手机号码：",
                                    style: nameTitleStyle, maxLines: 1),
                                Flexible(
                                    child: Text(vipInfo?.vip?.phone ?? "",
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: nameStyle))
                              ],
                            ))
                      ],
                    ),
                  ),
                )
              ],
            )),
        Expanded(
            flex: 4,
            child: Row(
              children: [
                SizedBox(height: 64.h, child: buildDivider(false)),
                _buildScore("储值", "¥${vipInfo?.asserts?.totalMoney ?? 0}",
                    contentTextColor: const Color(0xFFEE6700)),
                SizedBox(height: 64.h, child: buildDivider(false)),
                _buildScore(
                    "可用积分", (vipInfo?.asserts?.availableScore ?? 0).toString()),
                SizedBox(height: 64.h, child: buildDivider(false)),
                _buildScore(
                    "保护期积分", (vipInfo?.asserts?.protectScore ?? 0).toString()),
                SizedBox(height: 64.h, child: buildDivider(false)),
                _buildScore("成长值", (vipInfo?.vip?.growthValue ?? 0).toString()),
                SizedBox(height: 64.h, child: buildDivider(false))
              ],
            ))
      ]),
    );
  }

  ///顶部余额/积分/成长值
  Widget _buildScore(String title, String content,
      {Color contentTextColor = const Color(0xFF333333)}) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(color: const Color(0xFF666666), fontSize: 30.sp),
          ),
          Padding(
            padding: EdgeInsets.only(top: 12.h),
            child: Text(
              content,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: contentTextColor,
                  fontSize: 34.sp,
                  fontWeight: FontWeight.bold),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget buildBottomBody(BuildContext context) {
    //如果发放权益卡、发放优惠券和赠送积分的权限都没有，则底部不会有任何按钮展示
    // todo 积分兑换暂时没有权限控制
    // if (!_permissionDto.memberVipBindRightsCard &&
    //     !_permissionDto.memberVipGiveScore &&
    //     !_permissionDto.memberVipCard) {
    //   return Container();
    // }
    final bool isExpired = isVipExpired(vipInfo?.vip!.validDate,
        vipType: vipInfo?.level!.vipType == true ? 1 : 0);
    EdgeInsets margin = EdgeInsets.only(left: 20.w);
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        buildDivider(),
        Container(
          color: Colors.white,
          padding:
              EdgeInsets.only(left: 30, right: 30, top: 34.h, bottom: 46.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (vipInfo?.level != null)
                _buildButton(context,
                    (vipInfo!.level!.vipType == true) ? "会员续费" : "升级付费会员",
                    margin: margin, onTap: () {
                  SecondaryScreenPlugin.hide();
                  NavigateUtil.navigateTo(context,
                          SVipLevelSelectPage(vipId: vipInfo!.vip!.id!))
                      .then((value) {
                    SecondaryScreenPlugin.showSecondaryScreenImage();
                    _getVipInfo(context);
                  });
                }),
              _buildButton(context, "会员充值", enable: !isExpired, margin: margin,
                  onTap: () {
                if (vipInfo != null) {
                  SecondaryScreenPlugin.hide();
                  NavigateUtil.navigateTo(
                          context, VipRechargePage(vipInfo: vipInfo!))
                      .then((value) {
                    SecondaryScreenPlugin.showSecondaryScreenImage();
                    _getVipInfo(context);
                  });
                }
              }),
              _buildButton(context, "积分兑换", enable: !isExpired, margin: margin,
                  onTap: () {
                if (vipInfo?.vip?.id?.isNotEmpty == true && !isExpired) {
                  SecondaryScreenPlugin.hide();
                  NavigateUtil.navigateTo(
                          context, CreditsExchangePages(vipInfo: vipInfo!))
                      .then((value) {
                    SecondaryScreenPlugin.showSecondaryScreenImage();
                    _getVipInfo(context);
                  });
                }
              }),
              Visibility(
                visible: _permissionDto.memberVipGiveScore == true,
                child: _buildButton(context, "积分调整",
                    margin: margin, enable: !isExpired, onTap: () {
                  if (vipInfo?.vip?.id?.isNotEmpty == true) {
                    showDialog(
                        context: context,
                        builder: (contextNext) => VipScoreDialog(
                              vipId: vipInfo!.vip!.id!,
                              voidCallback: () => _getVipInfo(context),
                            ));
                  }
                }),
              ),
              Visibility(
                visible: _permissionDto.memberVipBindRightsCard == true,
                child: _buildButton(context, "发放权益卡",
                    width: 268.w,
                    margin: margin,
                    enable: !isExpired, onTap: () {
                  context = Navigator.of(context).context;
                  if (vipInfo?.vip?.id?.isNotEmpty == true) {
                    showDialog(
                        context: context,
                        builder: (contextNext) => SendRightCardWidget(
                              vipIds: [vipInfo!.vip!.id!],
                              rightCardList: vipInfo!.rightCardList ?? [],
                              voidCallback: () => _getVipInfo(context),
                            ));
                  }
                }),
              ),
              Visibility(
                visible: _permissionDto.memberVipCard == true,
                child: _buildButton(context, "发放优惠券",
                    margin: margin,
                    width: 268.w,
                    enable: !isExpired, onTap: () {
                  context = Navigator.of(context).context;
                  if (vipInfo?.vip?.id?.isNotEmpty == true) {
                    showDialog(
                        context: context,
                        builder: (contextNext) => CouponsPages(
                              vipIds: [vipInfo!.vip!.id!],
                              voidCallback: () => _getVipInfo(context),
                            ));
                  }
                }),
              ),
              Expanded(child: Container()),
              Visibility(
                visible: _permissionDto.memberVipEdit == true,
                child: _buildButton(
                  context,
                  "编辑",
                  textColor: Colors.white,
                  background: const Color(0xFF4679FC),
                  borderColor: null,
                  onTap: () {
                    if (vipInfo?.vip?.id?.isNotEmpty == true) {
                      //跳转到会员编辑
                      NavigateUtil.navigateTo(
                              context, VipAddPage(vipId: vipInfo!.vip!.id!))
                          .then((value) => _getVipInfo(context));
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  ///底部按钮
  Widget _buildButton(
    BuildContext context,
    String content, {
    double? width,
    double? height,
    bool enable = true,
    Color textColor = const Color(0xFF4679FC),
    Color background = Colors.white,
    Color? borderColor = const Color(0xFF4679FC),
    EdgeInsets? margin,
    VoidCallback? onTap,
  }) {
    if (!enable) {
      textColor = const Color(0xFFBBBBBB);
      borderColor = const Color(0xFFBBBBBB);
      background = const Color(0xFFEEEEEE);
    }
    margin = margin ?? const EdgeInsets.only();
    width = width ?? 225.w;
    height = height ?? 80.h;
    Widget child = Container(
      height: height,
      width: width,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: background,
        borderRadius: BorderRadius.circular(8.w),
        border: borderColor
            ?.let((borderColor) => Border.all(color: borderColor, width: 2.w)),
      ),
      child: Text(content, style: TextStyle(color: textColor, fontSize: 34.sp)),
    );
    if (enable && onTap != null) {
      child = GestureDetector(
          behavior: HitTestBehavior.opaque, onTap: onTap, child: child);
    }
    return Padding(padding: margin, child: child);
  }

  @override
  Future<bool> backWillPop() {
    // Navigator.pop(context, vipInfo);

    Navigator.of(context).pop(vipInfo);
    return Future.value(false);
  }

  @override
  String getActionBarTitle() => "会员详情";

  @override
  Future<void> onInitState() async {
    await _getVipInfo(context);
  }

  ///查询会员信息
  Future<void> _getVipInfo(BuildContext context) async {
    vipInfo = await VipModel.getVipWithLevelScoreRightsCardById(
        context, widget.vipId, SpTool.getStoreInfo()!.otypeId ?? "");
    setState(() {});
  }
}
