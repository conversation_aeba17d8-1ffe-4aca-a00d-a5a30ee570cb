import 'package:flutter/cupertino.dart';
import 'package:halo_pos/vip/entity/score_quick_reason_dto.dart';
import '../../../bill/entity/ss_card_dto.dart';
import '../../../common/net/http_util.dart';
import '../../../common/net/request_method.dart';
import '../../../common/standard.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../vip/entity/add_or_edit_vip_request.dart';
import '../../../vip/entity/card_template.dart';
import '../../../vip/entity/right_card_item_model.dart';
import '../../../vip/entity/vip_level_dto.dart';
import '../../../vip/entity/vip_tag.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:haloui/haloui.dart';

import '../../bill/settlement/entity/atype_info_bean.dart';
import '../entity/card_coupon_card_template.dart';
import '../entity/get_vip_by_code_dto.dart';
import '../entity/get_vip_level_score_rights_card_response.dart';
import '../entity/page_response.dart';
import '../entity/vip_dto.dart';
import '../entity/vip_fee_record.dart';

///会员
class VipModel {
  VipModel._();

  ///根据vipId查询会员信息
  static Future<VipWithLevelAssertsRightsCardDTO?>
      getVipWithLevelScoreRightsCardById(
          BuildContext context, String vipId, String otypeId) async {
    if (TextUtil.isEmpty(vipId)) {
      return null;
    }
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_VIP_LEVEL_ASSERTS_RIGHTS_CARD_BY_ID,
        data: {
          "vipId": vipId,
          "otypeId": otypeId,
        });
    return (response.data as Map?)
        ?.let((it) => VipWithLevelAssertsRightsCardDTO.fromJson(it));
  }

  ///根据手机号获取会员信息（列表）,手机号码模糊查询，会出现多个结果
  ///[type] 会员类型，为4，则是直营店
  ///[fuzzyQuery] 不传模糊查询 ,false 精准查询
  static Future<List<VipWithLevelAssertsRightsCardDTO>?>
      getVipWithLevelScoreRightsCardByPhone(BuildContext context, String? phone,
          {int type = 4, bool fuzzyQuery = true}) async {
    if (TextUtil.isEmpty(phone)) {
      return null;
    }
    String? otypeId = SpTool.getStoreInfo()!.otypeId;
    if (TextUtil.isEmpty(otypeId)) {
      return null;
    }
    Map data = {
      "phone": phone,
      "otypeId": otypeId,
      "type": type,
    };
    if (fuzzyQuery == false) {
      data["fuzzyQuery"] = false;
    }
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_VIP_LEVEL_ASSERTS_RIGHTS_CARD_BY_PHONE,
        data: data);
    return (response.data as List?)
        ?.map((json) => VipWithLevelAssertsRightsCardDTO.fromJson(json))
        .toList();
  }

  ///根据手机号获取会员信息（列表）,手机号码模糊查询，会出现多个结果
  ///[type] 会员类型，为4，则是直营店
  ///[fuzzyQuery] 不传模糊查询 ,false 精准查询
  static Future<List<Vip>?>
      getVipWithLevelScoreRightsCardByCode(BuildContext context, String? code,
          {int type = 4, bool fuzzyQuery = true}) async {
    if (TextUtil.isEmpty(code)) {
      return null;
    }
    String? otypeId = SpTool.getStoreInfo()!.otypeId;
    if (TextUtil.isEmpty(otypeId)) {
      return null;
    }
    Map data = {
      "code": code,
      "otypeId": otypeId,
      "type": type,
    };
    if (fuzzyQuery == false) {
      data["fuzzyQuery"] = false;
    }
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_VIP_LEVEL_ASSERTS_RIGHTS_CARD_BY_CODE,
        data: data);
    return (response.data as List?)
        ?.map((json) => Vip.fromJson(json))
        .toList();
  }

  ///根据手机号获取会员信息（分页查询）,手机号码模糊查询，会出现多个结果
  ///[type] 会员类型，为4，则是直营店
  ///[fuzzyQuery] 不传模糊查询 ,false 精准查询
  ///[pageIndex] 页码，从1开始
  ///[pageSize] 每页大小
  static Future<PageResponse<Vip>?> getVipByCodeNewPage(
    BuildContext context,
    String? code, {
    int type = 4,
    bool fuzzyQuery = true,
    int pageIndex = 1,
    int pageSize = 20,
  }) async {
    if (TextUtil.isEmpty(code)) {
      return null;
    }
    String? otypeId = SpTool.getStoreInfo()!.otypeId;
    if (TextUtil.isEmpty(otypeId)) {
      return null;
    }

    GetVipByCodeDTO request = GetVipByCodeDTO(
      code: code,
      otypeId: otypeId,
      type: type,
      fuzzyQuery: fuzzyQuery,
      pageIndex: pageIndex,
      pageSize: pageSize,
    );

    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GET_VIP_BY_CODE_NEW,
      data: request.toJson(),
    );

    if (response.data != null) {
      return PageResponse<Vip>.fromJson(
        response.data,
        (json) => Vip.fromJson(json),
      );
    }
    return null;
  }

  ///获取会员等级列表
  ///[stoped] 是否查询停用的会员等级
  static Future<List<VipLevelDto>?> getVipLevelList(BuildContext context,
      {int stoped = 0, int? vipType}) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_VIP_LEVEL_LIST,
        data: {"stoped": stoped, "vipType": vipType});
    return (response.data as List?)
        ?.map((json) => VipLevelDto.fromJson(json))
        .toList();
  }

  ///获取全部的会员标签
  static Future<List<VipTag>?> getVipTagList(BuildContext context) async {
    ResponseModel response =
        await HttpUtil.request(context, method: RequestMethod.POST_GET_VIP_TAG);
    return (response.data?["tags"] as List?)
        ?.map((json) => VipTag.fromJson(json))
        .toList();
  }

  ///获取权益卡模板列表
  ///[page] 页码，从1开始
  ///[pageSize] 分页大小，大于0
  static Future<List<CardTemplate>?> getRightsCardTemplateList(
      BuildContext context, int page, int pageSize) async {
    if (page < 1) {
      page = 1;
    }
    if (pageSize <= 0) {
      pageSize = 20;
    }
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_RIGHTS_CARD_TEMPLATE_LIST,
        data: {"page": page, "pageSize": pageSize, "stoped": 0});
    return (response.data?["list"] as List?)
        ?.map((json) => CardTemplate.fromJson(json))
        .toList();
  }

  static Future<bool> givingScore(BuildContext context, Map requset) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GIVING_SCORE, data: requset);

    return response.code == 200 ? true : false;
  }

  ///获取次卡详情
  static Future<ResponseModel> onceCardDetailData(
      BuildContext context, Map requset) async {
    ResponseModel response = await HttpUtil.request(context,
        method: 'member/onceCard/onceCardDetail', data: requset);
    return response;
  }

  /// 获取权益卡
  static Future<ResponseModel> getRightsCardData(
      BuildContext context, Map requset) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_CARD_TEMPLATE_LIST, data: requset);
    return response;
  }

  ///发送权益卡
  static Future<bool> giveRightsCardData(
      BuildContext context, Map requset) async {
    ResponseModel response = await HttpUtil.request(context,
        method: 'member/cardTemplate/giveCardToVip', data: requset);
    return response.code == 200 ? true : false;
  }

  ///新增或修改一个会员
  ///成功时返回vipId
  static Future<String?> addOrEditVip(
      BuildContext context, AddOrEditVipDTO dto) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_ADD_OR_EDIT_VIP, data: dto.toJson());
    if (response.code != 200 || response.data == null) {
      if (context.mounted) {
        HaloToast.show(context, msg: response.message);
      }
    }
    return response.data as String?;
  }

  //获取优惠券列表
  static Future<List<RightCartItemModel>?> getCouponsList(
      BuildContext context, int page, int pageSize) async {
    if (page < 1) {
      page = 1;
    }
    if (pageSize <= 0) {
      pageSize = 20;
    }
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_CARD_TEMPLATE_LIST,
        data: {
          "page": page,
          "pageSize": pageSize,
          "stoped": 0,
          "cardTypes": [2, 3, 4],
          "checkDate": 1,
          "useRange": 0
        });
    if (response.data == null) {
      return null;
    }
    return (response.data?["list"] as List?)
        ?.map((json) => RightCartItemModel.fromMap(json))
        .toList();
  }

  //发优惠券
  static Future<bool> sendCouponsList(BuildContext context, Map request) async {
    ResponseModel response = await HttpUtil.request(context,
        method: 'member/cardTemplate/giveCardToVip', data: request);
    return response.code == 200 ? true : false;
  }

  ///根据会员获取卡券信息
  ///[vipId] 会员id
  ///[otypeId] 门店id，用于筛选这个门店可用的优惠券
  ///[cardType] 卡券类型 0：等级权益卡，1：普通权益卡，2：代金券 3 折扣券 4礼品券
  static Future<List<SsCardDto>?> getCardByVipId(BuildContext context,
      {required String vipId,
      required String otypeId,
      required List<int> cardType}) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_CARD_BY_VIP_ID,
        data: {"vipId": vipId, "otypeId": otypeId, "cardType": cardType});
    return (response.data as List?)
        ?.map((json) => SsCardDto.fromMap(json))
        .toList();
  }

  ///获取会员等级的权益
  static Future<CardCouponCardTemplate?> getLevelRightsByLevelId(
      BuildContext context, String levelId) async {
    ResponseModel response = await HttpUtil.request(context,
        method: "${RequestMethod.getCardByLevelId}?id=$levelId");
    return CardCouponCardTemplate.fromJson(response.data);
  }

  static Future<bool> submitVipLevelBill(
      BuildContext context, {
        required String vchcode,
        required String vipId,
        required String levelId,
        required String levelRuleId,
        required List<AtypeInfoBean> payment,
        required String btypeId,
        String? etypeId,
        String? otypeId,
        String? memo,
      }) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.submitVipLevelBill,
        data: {
      "vchcode":vchcode,
          "vipId": vipId,
          "levelId": levelId,
          "levelRuleId": levelRuleId,
          "payment": levelRuleId,
          "payment": payment
              .map((e) => {
            "atypeId": e.storePayway.atypeId,
            "paywayId": e.storePayway.paywayId,
            "currencyAtypeTotal": e.total,
            "paywayType": e.storePayway.paywayType,
            "paywayFullname": e.storePayway.paywayName,
            "outNo": e.payOutNo,
          })
              .toList(),
          "btypeId": btypeId,
          "etypeId": etypeId,
          "otypeId": otypeId,
          "memo": memo,
        });
    if (response.code != 200) {
      if (context.mounted) {
        HaloToast.show(context, msg: response.data);
      }
      return false;
    }
    return true;
  }

  static Future<void> renewOrUpgradeSVIP(
    BuildContext context, {
        required String vchcode,
        required String vipId,
    required String levelId,
    required String levelRuleId,
    required List<AtypeInfoBean> payment,
    required String btypeId,
    String? etypeId,
    String? otypeId,
    String? memo,
  }) async {
    await HttpUtil.request(context,
        method: RequestMethod.renewOrUpgradeSVIP,
        data: {
          "vchcode":vchcode,
          "vipId": vipId,
          "levelId": levelId,
          "levelRuleId": levelRuleId,
          "payment": levelRuleId,
          "payment": payment
              .map((e) => {
                    "atypeId": e.storePayway.atypeId,
                    "paywayId": e.storePayway.paywayId,
                    "currencyAtypeTotal": e.total,
                    "paywayType": e.storePayway.paywayType,
                    "paywayFullname": e.storePayway.paywayName,
                    "outNo": e.payOutNo,
                  })
              .toList(),
          "btypeId": btypeId,
          "etypeId": etypeId,
          "otypeId": otypeId,
          "memo": memo,
        });
  }

  ///根据会员id查询未过期的会员等级付费记录
  static Future<List<VipFeeRecord>> selectFeeRecordByVipId(BuildContext context,
      {required String vipId}) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.selectFeeRecordByVipId, data: vipId);
    return (response.data as List?)
            ?.map((json) => VipFeeRecord.fromMap(json))
            .toList() ??
        [];
  }

  static Future<List<ScoreQuickReasonDto>> getScoreQuickReasonList(BuildContext context) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.getScoreQuickReasonList);
    return (response.data as List?)
        ?.map((json) => ScoreQuickReasonDto.fromJson(json))
        .toList() ??
        [];
  }
}
