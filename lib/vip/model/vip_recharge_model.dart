import 'package:flutter/material.dart';
import 'package:halo_utils/utils/String_util.dart';
import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/settlement/entity/atype_info_bean.dart';
import '../../../common/net/http_util.dart';
import '../../../common/net/request_method.dart';
import '../../../vip/entity/recharge_strategy.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../common/tool/performance_capture_util.dart';
import '../../entity/page_info.dart';
import '../recharge_record/entity/get_recharge_record_request.dart';
import '../recharge_record/entity/invalidate_recharge_request.dart';
import '../recharge_record/entity/recharge_record.dart';

///会员充值
class VipRechargeModel {
  ///获取充值策略列表
  static Future<List<RechargeStrategy>?> getRechargeListByVip(
      BuildContext context,
      {required String otypeId,
      required String vipId,
      required String vipLevelId}) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.vipRechargeRule);
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_RECHARGE_LIST_BY_VIP,
        data: {
          "vipId": vipId,
          "vipLevelId": vipLevelId,
          "otypeId": otypeId,
        });
    PerformanceCaptureUtil.end(PerformanceTimeName.vipRechargeRule);
    return (response.data as List?)
        ?.map((json) => RechargeStrategy.fromJson(json))
        .toList();
  }

  ///会员充值收款单草稿
  static Future<bool> submitRechargeBill(
    BuildContext context, {
    required String vchcode,
    required String vipId,
    required num rechargeMoney,
    required List<AtypeInfoBean> payment,
    required String otypeId,
    required String memo,
    required String summary,
    required String ktypeId,
    required String btypeId,
    required String createEtypeId,
    required String vipLevelId,
    required String rechargeId,
    num? giveMoney,
    int? score,
    String? cardId,
    int? cardQty,
    GoodsBillDto? goodsBill,
    String? etypeId,
    String? cashierId,
  }) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VIP_RECHARGEBILL,
        data: {
          "vchcode": vchcode,
          "vipId": vipId,
          "vipLevelId": vipLevelId,
          "rechargeId": rechargeId,
          "rechargeMoney": rechargeMoney,
          "payment": payment
              .map((e) => {
                    "atypeId": e.storePayway.atypeId,
                    "paywayId": e.storePayway.paywayId,
                    "currencyAtypeTotal": e.total,
                    "paywayType": e.storePayway.paywayType,
                    "paywayFullname": e.storePayway.paywayName,
                    "outNo": e.payOutNo,
                  })
              .toList(),
          "otypeId": otypeId,
          "ktypeId": ktypeId,
          "btypeId": btypeId,
          "memo": memo,
          "summary": summary,
          "giveMoney": giveMoney,
          "score": score,
          "cardId": cardId,
          "cardQty": cardQty,
          "goodsBill": goodsBill?.toJson(),
          "etypeId": etypeId ?? "0",
          "createEtypeId": createEtypeId,
          "cashierId": cashierId,
        });
    if (response.code != 200) {
      if (context.mounted) {
        HaloToast.show(context, msg: response.data);
      }
      return false;
    }
    return true;
  }

  ///会员充值
  static Future<bool> recharge(
    BuildContext context, {
        required String vchcode,
        required String vipId,
    required num rechargeMoney,
    required List<AtypeInfoBean> payment,
    required String otypeId,
    required String memo,
    required String summary,
    required String ktypeId,
    required String btypeId,
    required String createEtypeId,
    required String vipLevelId,
    required String rechargeId,
    num? giveMoney,
    int? score,
    String? cardId,
    int? cardQty,
    GoodsBillDto? goodsBill,
    String? etypeId,
    String? cashierId,
  }) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VIP_RECHARGE,
        data: {
          "vchcode": vchcode,
          "vipId": vipId,
          "vipLevelId": vipLevelId,
          "rechargeId": rechargeId,
          "rechargeMoney": rechargeMoney,
          "payment": payment
              .map((e) => {
                    "atypeId": e.storePayway.atypeId,
                    "paywayId": e.storePayway.paywayId,
                    "currencyAtypeTotal": e.total,
                    "paywayType": e.storePayway.paywayType,
                    "paywayFullname": e.storePayway.paywayName,
                    "outNo": e.payOutNo,
                    "payOrderNo": e.payOrderNo,
                  })
              .toList(),
          "otypeId": otypeId,
          "ktypeId": ktypeId,
          "btypeId": btypeId,
          "memo": memo,
          "summary": summary,
          "giveMoney": giveMoney,
          "score": score,
          "cardId": cardId,
          "cardQty": cardQty,
          "goodsBill": goodsBill?.toJson(),
          "etypeId": etypeId ?? "0",
          "createEtypeId": createEtypeId,
          "cashierId": cashierId,
        });
    if (response.code != 200) {
      if (context.mounted) {
        HaloToast.show(context, msg: response.message);
      }
      return false;
    }
    String message = "充值成功";
    if (response.data is String) {
      message = message + response.data;
    }
    if (context.mounted) {
      HaloToast.show(context, msg: message);
    }
    return true;
  }

  ///获取会员充值记录
  static Future<PageInfo<RechargeRecordDTO>?> getRechargeRecordList(
      BuildContext context, GetRechargeRecordRequestDto request) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.getRechargeRecordList, data: request.toJson());
    if (response.data == null) return null;
    return PageInfo.fromMap(response.data,
        mapper: (e) => RechargeRecordDTO.fromMap(e));
  }

  ///获取会员充值记录详情
  static Future<RechargeRecordDetailDTO?> getRechargeRecordDetail(
      BuildContext context, String recordId) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.getRechargeRecordDetail, data: recordId);
    if (response.data == null) return null;
    return RechargeRecordDetailDTO.fromMap(response.data);
  }

  ///会员储值作废
  static Future<void> invalidateRechargeRecord(
      BuildContext context, InvalidateRechargeRequest request) async {
    await HttpUtil.request(context,
        method: RequestMethod.invalidateRechargeRecord, data: request.toJson());
  }
}
